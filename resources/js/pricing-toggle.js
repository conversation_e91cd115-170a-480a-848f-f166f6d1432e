/**
 * Pricing Toggle Component
 * Handles monthly/annual billing frequency switching
 */

document.addEventListener('DOMContentLoaded', function() {
    const pricingToggle = document.getElementById('pricing-toggle');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const annualPrices = document.querySelectorAll('.annual-price');
    const checkoutForms = document.querySelectorAll('.checkout-form');
    const savingsBadges = document.querySelectorAll('.savings-badge');

    if (!pricingToggle) return;

    // Initialize toggle state
    let isAnnual = pricingToggle.checked;
    updatePricing(isAnnual);

    // Handle toggle change
    pricingToggle.addEventListener('change', function() {
        isAnnual = this.checked;
        updatePricing(isAnnual);
    });

    function updatePricing(annual) {
        // Update price displays
        monthlyPrices.forEach(price => {
            price.style.display = annual ? 'none' : 'block';
        });

        annualPrices.forEach(price => {
            price.style.display = annual ? 'block' : 'none';
        });

        // Update checkout forms
        checkoutForms.forEach(form => {
            const frequencyInput = form.querySelector('input[name="frequency"]');
            if (frequencyInput) {
                frequencyInput.value = annual ? 'annual' : 'monthly';
            }
        });

        // Update savings badges
        savingsBadges.forEach(badge => {
            badge.style.display = annual ? 'block' : 'none';
        });

        // Add smooth transition effect
        const priceElements = document.querySelectorAll('.price-amount');
        priceElements.forEach(element => {
            element.style.transition = 'opacity 0.3s ease';
            element.style.opacity = '0.7';
            setTimeout(() => {
                element.style.opacity = '1';
            }, 150);
        });
    }

    // Handle plan selection with frequency
    function selectPlan(planId, frequency) {
        const form = document.querySelector(`#checkout-form-${planId}`);
        if (form) {
            const frequencyInput = form.querySelector('input[name="frequency"]');
            if (frequencyInput) {
                frequencyInput.value = frequency;
            }
            form.submit();
        }
    }

    // Expose function globally for inline handlers
    window.selectPlan = selectPlan;
});
