/**
 * Plan Selector Component
 * Handles dynamic plan selection and comparison on the welcome page
 */

class PlanSelector {
    constructor() {
        this.plans = [];
        this.selectedPlan = null;
        this.init();
    }

    async init() {
        await this.loadPlans();
        this.bindEvents();
        this.highlightFeaturedPlan();
    }

    /**
     * Load plans from API
     */
    async loadPlans() {
        try {
            const response = await fetch('/api/plans');
            const data = await response.json();

            if (data.success) {
                this.plans = data.data;
            }
        } catch (error) {
            console.error('Failed to load plans:', error);
        }
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Plan selection buttons
        document.querySelectorAll('[data-plan-id]').forEach(button => {
            button.addEventListener('click', (e) => {
                const planId = e.target.getAttribute('data-plan-id');
                this.selectPlan(planId);
            });
        });

        // Billing toggle (monthly/annual)
        const billingToggle = document.getElementById('billing-toggle');
        if (billingToggle) {
            billingToggle.addEventListener('change', (e) => {
                this.toggleBilling(e.target.checked);
            });
        }

        // Plan comparison modal
        const compareButton = document.getElementById('compare-plans');
        if (compareButton) {
            compareButton.addEventListener('click', () => {
                this.showComparison();
            });
        }
    }

    /**
     * Select a plan
     */
    selectPlan(planId) {
        this.selectedPlan = this.plans.find(plan => plan.id == planId);

        // Update UI to show selected plan
        document.querySelectorAll('.plan-card').forEach(card => {
            card.classList.remove('selected');
        });

        const selectedCard = document.querySelector(`[data-plan-id="${planId}"]`).closest('.plan-card');
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }

        // Update CTA buttons
        this.updateCTAButtons();
    }

    /**
     * Toggle between monthly and annual billing
     */
    toggleBilling(isAnnual) {
        document.querySelectorAll('.plan-price').forEach(priceElement => {
            const planId = priceElement.getAttribute('data-plan-id');
            const plan = this.plans.find(p => p.id == planId);

            if (plan) {
                const price = isAnnual ? plan.annual_price : plan.monthly_price;
                const period = isAnnual ? '/year' : '/month';

                priceElement.innerHTML = `$${price}<span class="text-base font-normal">${period}</span>`;
            }
        });

        // Update savings indicator
        const savingsIndicator = document.getElementById('savings-indicator');
        if (savingsIndicator) {
            savingsIndicator.style.display = isAnnual ? 'block' : 'none';
        }
    }

    /**
     * Highlight the featured plan
     */
    highlightFeaturedPlan() {
        const featuredPlan = this.plans.find(plan => plan.is_featured);
        if (featuredPlan) {
            const featuredCard = document.querySelector(`[data-plan-id="${featuredPlan.id}"]`).closest('.plan-card');
            if (featuredCard) {
                featuredCard.classList.add('featured');
            }
        }
    }

    /**
     * Update CTA buttons based on selected plan
     */
    updateCTAButtons() {
        if (!this.selectedPlan) return;

        const ctaButtons = document.querySelectorAll('.hero-cta');
        ctaButtons.forEach(button => {
            if (this.selectedPlan.is_free) {
                button.textContent = 'Start Free Trial';
                button.href = `/register?plan=${this.selectedPlan.id}`;
            } else {
                button.textContent = `Start with ${this.selectedPlan.name}`;
                button.href = `/register?plan=${this.selectedPlan.id}`;
            }
        });
    }

    /**
     * Show plan comparison modal
     */
    async showComparison() {
        try {
            const response = await fetch('/api/plans-comparison');
            const data = await response.json();

            if (data.success) {
                this.renderComparisonModal(data.data);
            }
        } catch (error) {
            console.error('Failed to load comparison data:', error);
        }
    }

    /**
     * Render comparison modal
     */
    renderComparisonModal(comparisonData) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold">Compare Plans</h3>
                    <button class="close-modal text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead>
                            <tr>
                                <th class="text-left py-2">Feature</th>
                                ${comparisonData.plans.map(plan => `
                                    <th class="text-center py-2 px-4">
                                        <div class="font-bold">${plan.name}</div>
                                        <div class="text-sm text-gray-500">$${plan.monthly_price}/mo</div>
                                    </th>
                                `).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${Object.entries(comparisonData.features).map(([feature, values]) => `
                                <tr class="border-t">
                                    <td class="py-3 font-medium capitalize">${feature.replace('_', ' ')}</td>
                                    ${comparisonData.plans.map(plan => `
                                        <td class="text-center py-3 px-4">${values[plan.id]}</td>
                                    `).join('')}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="mt-6 flex justify-center space-x-4">
                    ${comparisonData.plans.map(plan => `
                        <a href="/register?plan=${plan.id}"
                           class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition">
                            Choose ${plan.name}
                        </a>
                    `).join('')}
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Close modal functionality
        modal.querySelector('.close-modal').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    /**
     * Add smooth scrolling to pricing section
     */
    static initSmoothScrolling() {
        document.querySelectorAll('a[href="#pricing"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const pricingSection = document.getElementById('pricing');
                if (pricingSection) {
                    pricingSection.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Handle language switching
     */
    static handleLanguageSwitch() {
        // Add smooth transition for language changes
        document.addEventListener('click', (e) => {
            if (e.target.closest('a[href*="/language/"]')) {
                document.body.style.transition = 'opacity 0.3s ease';
                document.body.style.opacity = '0.7';
            }
        });
    }

    /**
     * Initialize plan selector on page load
     */
    static init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                new PlanSelector();
                PlanSelector.initSmoothScrolling();
                PlanSelector.handleLanguageSwitch();
            });
        } else {
            new PlanSelector();
            PlanSelector.initSmoothScrolling();
            PlanSelector.handleLanguageSwitch();
        }
    }
}

// Auto-initialize
PlanSelector.init();

// Export for manual initialization if needed
window.PlanSelector = PlanSelector;
