/**
 * Admin Bulk Actions Handler
 */
class BulkActions {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateBulkActionVisibility();
    }

    bindEvents() {
        // Select all checkbox
        document.addEventListener('change', (e) => {
            if (e.target.matches('#select-all')) {
                this.toggleAllCheckboxes(e.target.checked);
            }
        });

        // Individual checkboxes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.bulk-checkbox')) {
                this.updateSelectAllState();
                this.updateBulkActionVisibility();
            }
        });

        // Bulk action form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('#bulk-action-form')) {
                this.handleBulkAction(e);
            }
        });

        // Bulk action button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('.bulk-action-btn')) {
                this.executeBulkAction(e);
            }
        });
    }

    toggleAllCheckboxes(checked) {
        const checkboxes = document.querySelectorAll('.bulk-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
        this.updateBulkActionVisibility();
    }

    updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('select-all');
        const checkboxes = document.querySelectorAll('.bulk-checkbox');
        const checkedCheckboxes = document.querySelectorAll('.bulk-checkbox:checked');

        if (selectAllCheckbox) {
            if (checkedCheckboxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCheckboxes.length === checkboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    }

    updateBulkActionVisibility() {
        const checkedCheckboxes = document.querySelectorAll('.bulk-checkbox:checked');
        const bulkActionContainer = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');

        if (bulkActionContainer) {
            if (checkedCheckboxes.length > 0) {
                bulkActionContainer.classList.remove('hidden');
                if (selectedCount) {
                    selectedCount.textContent = checkedCheckboxes.length;
                }
            } else {
                bulkActionContainer.classList.add('hidden');
            }
        }
    }

    getSelectedIds() {
        const checkedCheckboxes = document.querySelectorAll('.bulk-checkbox:checked');
        return Array.from(checkedCheckboxes).map(checkbox => checkbox.value);
    }

    executeBulkAction(e) {
        e.preventDefault();
        
        const action = e.target.dataset.action;
        const selectedIds = this.getSelectedIds();

        if (selectedIds.length === 0) {
            alert('Please select at least one item.');
            return;
        }

        // Confirm destructive actions
        if (action === 'delete' || action === 'bulk-delete') {
            const confirmMessage = `Are you sure you want to delete ${selectedIds.length} item(s)? This action cannot be undone.`;
            if (!confirm(confirmMessage)) {
                return;
            }
        }

        this.performBulkAction(action, selectedIds);
    }

    performBulkAction(action, selectedIds) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        // Add method override if needed
        if (action === 'delete' || action === 'bulk-delete') {
            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            form.appendChild(methodInput);
        }

        // Add selected IDs
        selectedIds.forEach(id => {
            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'ids[]';
            idInput.value = id;
            form.appendChild(idInput);
        });

        // Set form action based on current page
        const currentPath = window.location.pathname;
        let actionUrl = currentPath;

        if (action === 'delete' || action === 'bulk-delete') {
            actionUrl = currentPath + '/bulk-destroy';
        } else {
            actionUrl = currentPath + '/bulk-' + action;
        }

        form.action = actionUrl;

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }

    handleBulkAction(e) {
        const selectedIds = this.getSelectedIds();
        
        if (selectedIds.length === 0) {
            e.preventDefault();
            alert('Please select at least one item.');
            return;
        }

        const action = e.target.querySelector('select[name="action"]')?.value;
        
        if (action === 'delete') {
            const confirmMessage = `Are you sure you want to delete ${selectedIds.length} item(s)? This action cannot be undone.`;
            if (!confirm(confirmMessage)) {
                e.preventDefault();
                return;
            }
        }
    }

    // Utility methods
    selectAll() {
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
            this.toggleAllCheckboxes(true);
        }
    }

    deselectAll() {
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
            this.toggleAllCheckboxes(false);
        }
    }

    getSelectedCount() {
        return document.querySelectorAll('.bulk-checkbox:checked').length;
    }

    showBulkActionModal(action, selectedIds) {
        // Create and show a modal for bulk actions
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
        modal.innerHTML = `
            <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                <div class="mt-3 text-center">
                    <h3 class="text-lg font-medium text-gray-900">Confirm Bulk Action</h3>
                    <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                            Are you sure you want to ${action} ${selectedIds.length} item(s)?
                        </p>
                    </div>
                    <div class="items-center px-4 py-3">
                        <button id="confirm-bulk-action" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300 mr-2">
                            Confirm
                        </button>
                        <button id="cancel-bulk-action" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle modal actions
        document.getElementById('confirm-bulk-action').addEventListener('click', () => {
            this.performBulkAction(action, selectedIds);
            document.body.removeChild(modal);
        });

        document.getElementById('cancel-bulk-action').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }
}

// Initialize bulk actions when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new BulkActions();
});

// Export for use in other modules
window.BulkActions = BulkActions;
