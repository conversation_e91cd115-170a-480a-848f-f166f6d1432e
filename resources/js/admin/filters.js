/**
 * Admin Filters Handler
 */
class AdminFilters {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadFiltersFromUrl();
    }

    bindEvents() {
        // Filter form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('#filter-form')) {
                this.handleFilterSubmit(e);
            }
        });

        // Clear filters button
        document.addEventListener('click', (e) => {
            if (e.target.matches('#clear-filters')) {
                this.clearFilters(e);
            }
        });

        // Quick filter buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('.quick-filter')) {
                this.applyQuickFilter(e);
            }
        });

        // Filter input changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.auto-filter')) {
                this.autoApplyFilter(e);
            }
        });

        // Search input with debounce
        document.addEventListener('input', (e) => {
            if (e.target.matches('#search-input')) {
                this.debounceSearch(e);
            }
        });
    }

    handleFilterSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const params = new URLSearchParams();

        // Build query parameters
        for (let [key, value] of formData.entries()) {
            if (value && value.trim() !== '') {
                params.append(key, value);
            }
        }

        // Update URL and reload page
        const newUrl = window.location.pathname + '?' + params.toString();
        window.location.href = newUrl;
    }

    clearFilters(e) {
        e.preventDefault();
        
        // Clear all filter inputs
        const filterForm = document.getElementById('filter-form');
        if (filterForm) {
            filterForm.reset();
        }

        // Clear search input
        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.value = '';
        }

        // Redirect to clean URL
        window.location.href = window.location.pathname;
    }

    applyQuickFilter(e) {
        e.preventDefault();
        
        const filterType = e.target.dataset.filter;
        const filterValue = e.target.dataset.value;

        if (filterType && filterValue) {
            const params = new URLSearchParams(window.location.search);
            params.set(filterType, filterValue);
            
            const newUrl = window.location.pathname + '?' + params.toString();
            window.location.href = newUrl;
        }
    }

    autoApplyFilter(e) {
        const filterForm = document.getElementById('filter-form');
        if (filterForm) {
            // Small delay to allow for multiple quick changes
            clearTimeout(this.autoFilterTimeout);
            this.autoFilterTimeout = setTimeout(() => {
                filterForm.dispatchEvent(new Event('submit'));
            }, 500);
        }
    }

    debounceSearch(e) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.performSearch(e.target.value);
        }, 300);
    }

    performSearch(query) {
        const params = new URLSearchParams(window.location.search);
        
        if (query && query.trim() !== '') {
            params.set('search', query);
        } else {
            params.delete('search');
        }

        const newUrl = window.location.pathname + '?' + params.toString();
        window.location.href = newUrl;
    }

    loadFiltersFromUrl() {
        const params = new URLSearchParams(window.location.search);
        
        // Populate filter form from URL parameters
        params.forEach((value, key) => {
            const input = document.querySelector(`[name="${key}"]`);
            if (input) {
                if (input.type === 'checkbox') {
                    input.checked = value === '1' || value === 'true';
                } else if (input.type === 'radio') {
                    const radioInput = document.querySelector(`[name="${key}"][value="${value}"]`);
                    if (radioInput) {
                        radioInput.checked = true;
                    }
                } else {
                    input.value = value;
                }
            }
        });

        // Update active filter indicators
        this.updateActiveFilters();
    }

    updateActiveFilters() {
        const params = new URLSearchParams(window.location.search);
        const activeFiltersContainer = document.getElementById('active-filters');
        
        if (!activeFiltersContainer) return;

        // Clear existing active filters
        activeFiltersContainer.innerHTML = '';

        // Add active filter tags
        params.forEach((value, key) => {
            if (key !== 'page' && value && value.trim() !== '') {
                const filterTag = this.createFilterTag(key, value);
                activeFiltersContainer.appendChild(filterTag);
            }
        });

        // Show/hide active filters container
        if (activeFiltersContainer.children.length > 0) {
            activeFiltersContainer.classList.remove('hidden');
        } else {
            activeFiltersContainer.classList.add('hidden');
        }
    }

    createFilterTag(key, value) {
        const tag = document.createElement('span');
        tag.className = 'inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800';
        
        const label = this.getFilterLabel(key);
        tag.innerHTML = `
            ${label}: ${value}
            <button type="button" class="ml-2 text-blue-600 hover:text-blue-800" onclick="AdminFilters.removeFilter('${key}')">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        `;
        
        return tag;
    }

    getFilterLabel(key) {
        const labels = {
            'search': 'Search',
            'status': 'Status',
            'role': 'Role',
            'plan': 'Plan',
            'category': 'Category',
            'type': 'Type',
            'date_from': 'From Date',
            'date_to': 'To Date',
            'is_active': 'Active',
            'is_featured': 'Featured',
            'language': 'Language',
        };
        
        return labels[key] || key.charAt(0).toUpperCase() + key.slice(1);
    }

    static removeFilter(key) {
        const params = new URLSearchParams(window.location.search);
        params.delete(key);
        
        const newUrl = window.location.pathname + (params.toString() ? '?' + params.toString() : '');
        window.location.href = newUrl;
    }

    // Export functionality
    exportFiltered(format = 'csv') {
        const params = new URLSearchParams(window.location.search);
        params.set('export', format);
        
        const exportUrl = window.location.pathname + '?' + params.toString();
        
        // Create temporary link to trigger download
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `export-${Date.now()}.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    // Advanced filter modal
    showAdvancedFilters() {
        const modal = document.getElementById('advanced-filters-modal');
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    hideAdvancedFilters() {
        const modal = document.getElementById('advanced-filters-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    // Save filter preset
    saveFilterPreset(name) {
        const params = new URLSearchParams(window.location.search);
        const preset = {
            name: name,
            filters: Object.fromEntries(params.entries()),
            created_at: new Date().toISOString()
        };

        // Save to localStorage
        const presets = JSON.parse(localStorage.getItem('admin_filter_presets') || '[]');
        presets.push(preset);
        localStorage.setItem('admin_filter_presets', JSON.stringify(presets));

        this.updatePresetsList();
    }

    // Load filter preset
    loadFilterPreset(presetIndex) {
        const presets = JSON.parse(localStorage.getItem('admin_filter_presets') || '[]');
        const preset = presets[presetIndex];

        if (preset) {
            const params = new URLSearchParams(preset.filters);
            const newUrl = window.location.pathname + '?' + params.toString();
            window.location.href = newUrl;
        }
    }

    // Update presets list in UI
    updatePresetsList() {
        const presetsList = document.getElementById('filter-presets-list');
        if (!presetsList) return;

        const presets = JSON.parse(localStorage.getItem('admin_filter_presets') || '[]');
        
        presetsList.innerHTML = presets.map((preset, index) => `
            <div class="flex items-center justify-between p-2 border rounded">
                <span class="text-sm font-medium">${preset.name}</span>
                <div class="flex space-x-2">
                    <button onclick="AdminFilters.loadFilterPreset(${index})" class="text-blue-600 hover:text-blue-800 text-sm">
                        Load
                    </button>
                    <button onclick="AdminFilters.deleteFilterPreset(${index})" class="text-red-600 hover:text-red-800 text-sm">
                        Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    // Delete filter preset
    static deleteFilterPreset(presetIndex) {
        const presets = JSON.parse(localStorage.getItem('admin_filter_presets') || '[]');
        presets.splice(presetIndex, 1);
        localStorage.setItem('admin_filter_presets', JSON.stringify(presets));
        
        // Update UI
        const filtersInstance = window.adminFilters;
        if (filtersInstance) {
            filtersInstance.updatePresetsList();
        }
    }

    static loadFilterPreset(presetIndex) {
        const filtersInstance = window.adminFilters;
        if (filtersInstance) {
            filtersInstance.loadFilterPreset(presetIndex);
        }
    }
}

// Initialize filters when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminFilters = new AdminFilters();
});

// Export for use in other modules
window.AdminFilters = AdminFilters;
