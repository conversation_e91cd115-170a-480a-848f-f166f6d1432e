/**
 * Admin Export Handler
 */
class AdminExport {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // Export button clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-btn')) {
                this.handleExport(e);
            }
        });

        // Export form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('#export-form')) {
                this.handleExportForm(e);
            }
        });

        // Export modal triggers
        document.addEventListener('click', (e) => {
            if (e.target.matches('.export-modal-trigger')) {
                this.showExportModal(e);
            }
        });

        // Close export modal
        document.addEventListener('click', (e) => {
            if (e.target.matches('.close-export-modal')) {
                this.hideExportModal();
            }
        });
    }

    handleExport(e) {
        e.preventDefault();
        
        const format = e.target.dataset.format || 'csv';
        const type = e.target.dataset.type || 'current';
        const includeFilters = e.target.dataset.includeFilters !== 'false';

        this.performExport(format, type, includeFilters);
    }

    handleExportForm(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const format = formData.get('format') || 'csv';
        const type = formData.get('type') || 'current';
        const includeFilters = formData.get('include_filters') === '1';
        const columns = formData.getAll('columns[]');

        this.performExport(format, type, includeFilters, columns);
    }

    performExport(format, type, includeFilters = true, columns = []) {
        // Show loading state
        this.showExportLoading();

        // Build export URL
        const baseUrl = window.location.pathname + '/export';
        const params = new URLSearchParams();

        params.set('format', format);
        params.set('type', type);

        if (includeFilters) {
            // Include current filters in export
            const currentParams = new URLSearchParams(window.location.search);
            currentParams.forEach((value, key) => {
                if (key !== 'page') {
                    params.set(key, value);
                }
            });
        }

        if (columns.length > 0) {
            columns.forEach(column => {
                params.append('columns[]', column);
            });
        }

        const exportUrl = baseUrl + '?' + params.toString();

        // Create download link
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = this.generateFilename(format, type);
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Hide loading state after a delay
        setTimeout(() => {
            this.hideExportLoading();
            this.showExportSuccess();
        }, 2000);
    }

    generateFilename(format, type) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
        const page = window.location.pathname.split('/').pop() || 'export';
        return `${page}-${type}-${timestamp}.${format}`;
    }

    showExportModal(e) {
        const modal = document.getElementById('export-modal');
        if (modal) {
            modal.classList.remove('hidden');
            
            // Set default values based on trigger
            const format = e.target.dataset.format;
            const type = e.target.dataset.type;
            
            if (format) {
                const formatSelect = modal.querySelector('[name="format"]');
                if (formatSelect) formatSelect.value = format;
            }
            
            if (type) {
                const typeSelect = modal.querySelector('[name="type"]');
                if (typeSelect) typeSelect.value = type;
            }
        }
    }

    hideExportModal() {
        const modal = document.getElementById('export-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    showExportLoading() {
        const loadingEl = document.getElementById('export-loading');
        if (loadingEl) {
            loadingEl.classList.remove('hidden');
        }

        // Disable export buttons
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.disabled = true;
            btn.classList.add('opacity-50', 'cursor-not-allowed');
        });
    }

    hideExportLoading() {
        const loadingEl = document.getElementById('export-loading');
        if (loadingEl) {
            loadingEl.classList.add('hidden');
        }

        // Re-enable export buttons
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('opacity-50', 'cursor-not-allowed');
        });
    }

    showExportSuccess() {
        const successEl = document.getElementById('export-success');
        if (successEl) {
            successEl.classList.remove('hidden');
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                successEl.classList.add('hidden');
            }, 3000);
        }
    }

    // API-based export for large datasets
    async performApiExport(format, type, includeFilters = true, columns = []) {
        try {
            this.showExportLoading();

            const params = new URLSearchParams();
            params.set('format', format);
            params.set('type', type);

            if (includeFilters) {
                const currentParams = new URLSearchParams(window.location.search);
                currentParams.forEach((value, key) => {
                    if (key !== 'page') {
                        params.set(key, value);
                    }
                });
            }

            if (columns.length > 0) {
                columns.forEach(column => {
                    params.append('columns[]', column);
                });
            }

            const response = await fetch(`/api/admin/export/${type}?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (!response.ok) {
                throw new Error('Export failed');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = this.generateFilename(format, type);
            link.click();
            
            window.URL.revokeObjectURL(url);
            
            this.hideExportLoading();
            this.showExportSuccess();

        } catch (error) {
            console.error('Export error:', error);
            this.hideExportLoading();
            this.showExportError(error.message);
        }
    }

    showExportError(message) {
        const errorEl = document.getElementById('export-error');
        if (errorEl) {
            errorEl.textContent = message || 'Export failed. Please try again.';
            errorEl.classList.remove('hidden');
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                errorEl.classList.add('hidden');
            }, 5000);
        }
    }

    // Bulk export with progress tracking
    async performBulkExport(items, format = 'csv') {
        const total = items.length;
        let processed = 0;

        this.showBulkExportProgress(0, total);

        try {
            const results = [];
            
            for (const item of items) {
                const result = await this.exportSingleItem(item, format);
                results.push(result);
                processed++;
                
                this.updateBulkExportProgress(processed, total);
                
                // Small delay to prevent overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            this.completeBulkExport(results, format);

        } catch (error) {
            console.error('Bulk export error:', error);
            this.showExportError('Bulk export failed: ' + error.message);
        }
    }

    showBulkExportProgress(processed, total) {
        const progressModal = document.getElementById('bulk-export-progress');
        if (progressModal) {
            progressModal.classList.remove('hidden');
            this.updateBulkExportProgress(processed, total);
        }
    }

    updateBulkExportProgress(processed, total) {
        const progressBar = document.getElementById('bulk-export-progress-bar');
        const progressText = document.getElementById('bulk-export-progress-text');
        
        const percentage = Math.round((processed / total) * 100);
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
        
        if (progressText) {
            progressText.textContent = `${processed} of ${total} items processed (${percentage}%)`;
        }
    }

    completeBulkExport(results, format) {
        // Hide progress modal
        const progressModal = document.getElementById('bulk-export-progress');
        if (progressModal) {
            progressModal.classList.add('hidden');
        }

        // Create and download the combined export file
        const combinedData = this.combineExportResults(results, format);
        const blob = new Blob([combinedData], { type: this.getMimeType(format) });
        const url = window.URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = this.generateFilename(format, 'bulk');
        link.click();
        
        window.URL.revokeObjectURL(url);
        this.showExportSuccess();
    }

    combineExportResults(results, format) {
        switch (format) {
            case 'csv':
                return results.join('\n');
            case 'json':
                return JSON.stringify(results, null, 2);
            case 'xml':
                return `<?xml version="1.0" encoding="UTF-8"?>\n<export>\n${results.join('\n')}\n</export>`;
            default:
                return results.join('\n');
        }
    }

    getMimeType(format) {
        const mimeTypes = {
            'csv': 'text/csv',
            'json': 'application/json',
            'xml': 'application/xml',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        };
        
        return mimeTypes[format] || 'text/plain';
    }

    async exportSingleItem(item, format) {
        // This would be implemented based on the specific item type
        // For now, return a placeholder
        return JSON.stringify(item);
    }
}

// Initialize export handler when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminExport = new AdminExport();
});

// Export for use in other modules
window.AdminExport = AdminExport;
