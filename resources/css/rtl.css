/* RTL (Right-to-Left) Support for Arabic */

/* Base RTL styles */
[dir="rtl"] {
    text-align: right;
}

[dir="rtl"] body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation adjustments */
[dir="rtl"] .flex.space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .flex.items-center.space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
}

/* Language switcher RTL */
[dir="rtl"] .language-switcher {
    left: 0;
    right: auto;
}

/* Plan cards RTL adjustments */
[dir="rtl"] .plan-card {
    text-align: center; /* Keep plan cards centered */
}

[dir="rtl"] .plan-features li {
    flex-direction: row-reverse;
}

[dir="rtl"] .plan-features .check-icon {
    margin-right: 0;
    margin-left: 8px;
}

/* Grid adjustments for RTL */
[dir="rtl"] .grid {
    direction: ltr; /* Keep grid layout LTR for proper alignment */
}

[dir="rtl"] .grid > * {
    direction: rtl; /* But content inside should be RTL */
}

/* Button and link adjustments */
[dir="rtl"] .flex.justify-center {
    flex-direction: row-reverse;
}

[dir="rtl"] .flex.justify-center.space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .flex.justify-center.space-x-6 > * + * {
    margin-left: 0;
    margin-right: 1.5rem;
}

/* Hero section RTL */
[dir="rtl"] .hero-section {
    text-align: center; /* Keep hero centered */
}

/* Features section RTL */
[dir="rtl"] .features-grid {
    direction: ltr; /* Keep grid LTR */
}

[dir="rtl"] .features-grid > * {
    direction: rtl; /* Content RTL */
    text-align: center; /* Center align feature cards */
}

/* Footer RTL adjustments */
[dir="rtl"] .footer-links {
    flex-direction: row-reverse;
}

[dir="rtl"] .footer-links.space-x-8 > * + * {
    margin-left: 0;
    margin-right: 2rem;
}

/* Dropdown menus RTL */
[dir="rtl"] .dropdown-menu {
    left: auto;
    right: 0;
}

/* Form elements RTL */
[dir="rtl"] input,
[dir="rtl"] select,
[dir="rtl"] textarea {
    text-align: right;
}

[dir="rtl"] .form-group {
    text-align: right;
}

/* Badge positioning for RTL */
[dir="rtl"] .absolute.-top-4.left-1\/2 {
    left: 50%;
    transform: translateX(-50%);
}

/* Testimonials RTL */
[dir="rtl"] .testimonials-grid {
    direction: ltr;
}

[dir="rtl"] .testimonials-grid > * {
    direction: rtl;
    text-align: center;
}

/* FAQ section RTL */
[dir="rtl"] .faq-item {
    text-align: right;
}

/* Content types showcase RTL */
[dir="rtl"] .content-types-grid {
    direction: ltr;
}

[dir="rtl"] .content-types-grid > * {
    direction: rtl;
    text-align: center;
}

/* Pricing section specific RTL adjustments */
[dir="rtl"] .pricing-grid {
    direction: ltr; /* Keep pricing grid LTR for proper card alignment */
}

[dir="rtl"] .pricing-grid > * {
    direction: rtl; /* Individual cards RTL */
}

/* Plan comparison modal RTL */
[dir="rtl"] .comparison-modal table {
    direction: rtl;
}

[dir="rtl"] .comparison-modal th,
[dir="rtl"] .comparison-modal td {
    text-align: center; /* Keep table content centered */
}

/* Mobile responsive RTL adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .flex-col.sm\:flex-row {
        flex-direction: column;
    }
    
    [dir="rtl"] .flex-col.sm\:flex-row.gap-4 > * + * {
        margin-right: 0;
        margin-top: 1rem;
    }
}

/* Smooth transitions for direction changes */
* {
    transition: margin 0.3s ease, padding 0.3s ease;
}

/* Arabic font improvements */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
    font-weight: 700;
    line-height: 1.4;
}

[dir="rtl"] p {
    line-height: 1.6;
}

/* Icon adjustments for RTL */
[dir="rtl"] .flex.items-center svg {
    margin-left: 0.5rem;
    margin-right: 0;
}

[dir="rtl"] .flex.items-center.justify-center svg {
    margin: 0; /* Keep centered icons centered */
}

/* Language switcher specific RTL styles */
[dir="rtl"] .language-switcher .dropdown-content {
    right: 0;
    left: auto;
}

/* Ensure proper text alignment for mixed content */
[dir="rtl"] .mixed-content {
    text-align: center;
}

/* Plan card hover effects RTL */
[dir="rtl"] .plan-card:hover {
    transform: translateY(-5px);
}

/* Gradient text RTL support */
[dir="rtl"] .text-gradient {
    background-clip: text;
    -webkit-background-clip: text;
}

/* Ensure buttons remain properly styled in RTL */
[dir="rtl"] button,
[dir="rtl"] .btn {
    text-align: center;
}

/* Fix for Alpine.js dropdowns in RTL */
[dir="rtl"] [x-data] .absolute.right-0 {
    right: auto;
    left: 0;
}

/* Ensure proper spacing for RTL flex containers */
[dir="rtl"] .space-x-reverse > * + * {
    margin-right: 0;
    margin-left: var(--space-x-reverse);
}

/* Custom scrollbar for RTL */
[dir="rtl"] ::-webkit-scrollbar {
    width: 8px;
}

[dir="rtl"] ::-webkit-scrollbar-track {
    background: #f1f1f1;
}

[dir="rtl"] ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

[dir="rtl"] ::-webkit-scrollbar-thumb:hover {
    background: #555;
}
