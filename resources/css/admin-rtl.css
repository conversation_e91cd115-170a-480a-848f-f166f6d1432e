/* RTL (Right-to-Left) Support for Admin Panel */

/* Apply RTL direction to Arabic admin interface */
[dir="rtl"] .admin-layout {
    direction: rtl;
}

/* RTL Sidebar */
[dir="rtl"] .admin-sidebar {
    @apply right-0 left-auto;
}

[dir="rtl"] .admin-sidebar.collapsed {
    @apply translate-x-full lg:-translate-x-0 lg:w-16;
}

[dir="rtl"] .admin-main {
    @apply lg:mr-64 lg:ml-0;
}

[dir="rtl"] .admin-main.sidebar-collapsed {
    @apply lg:mr-16 lg:ml-0;
}

/* RTL Navigation */
[dir="rtl"] .admin-nav-item {
    @apply text-right;
}

[dir="rtl"] .admin-nav-item.active {
    @apply border-l-2 border-r-0 border-blue-500;
}

[dir="rtl"] .admin-nav-icon {
    @apply ml-3 mr-0;
}

[dir="rtl"] .sidebar-collapsed .admin-nav-icon {
    @apply ml-0;
}

/* RTL Text Alignment */
[dir="rtl"] .admin-card {
    @apply text-right;
}

[dir="rtl"] .admin-table th {
    @apply text-right;
}

[dir="rtl"] .admin-table td {
    @apply text-right;
}

/* RTL Forms */
[dir="rtl"] .admin-form-label {
    @apply text-right;
}

[dir="rtl"] .admin-form-input {
    @apply text-right;
}

[dir="rtl"] .admin-form-textarea {
    @apply text-right;
}

[dir="rtl"] .admin-form-select {
    @apply text-right;
}

/* RTL Buttons */
[dir="rtl"] .btn-admin-primary,
[dir="rtl"] .btn-admin-secondary,
[dir="rtl"] .btn-admin-danger,
[dir="rtl"] .btn-admin-success {
    @apply text-right;
}

/* RTL Dropdowns */
[dir="rtl"] .admin-dropdown-menu {
    @apply left-0 right-auto;
}

[dir="rtl"] .admin-dropdown-item {
    @apply text-right;
}

/* RTL Search */
[dir="rtl"] .admin-search-input {
    @apply pr-10 pl-3 text-right;
}

[dir="rtl"] .admin-search-icon {
    @apply right-0 left-auto pr-3 pl-0;
}

/* RTL Breadcrumbs */
[dir="rtl"] .admin-breadcrumbs {
    @apply flex-row-reverse;
}

/* RTL Pagination */
[dir="rtl"] .admin-pagination {
    @apply flex-row-reverse;
}

/* RTL Specific Spacing */
[dir="rtl"] .space-x-2 > * + * {
    @apply ml-2 mr-0;
}

[dir="rtl"] .space-x-3 > * + * {
    @apply ml-3 mr-0;
}

[dir="rtl"] .space-x-4 > * + * {
    @apply ml-4 mr-0;
}

[dir="rtl"] .space-x-6 > * + * {
    @apply ml-6 mr-0;
}

/* RTL Margins */
[dir="rtl"] .mr-2 {
    @apply ml-2 mr-0;
}

[dir="rtl"] .mr-3 {
    @apply ml-3 mr-0;
}

[dir="rtl"] .mr-4 {
    @apply ml-4 mr-0;
}

[dir="rtl"] .mr-6 {
    @apply ml-6 mr-0;
}

[dir="rtl"] .ml-2 {
    @apply mr-2 ml-0;
}

[dir="rtl"] .ml-3 {
    @apply mr-3 ml-0;
}

[dir="rtl"] .ml-4 {
    @apply mr-4 ml-0;
}

[dir="rtl"] .ml-6 {
    @apply mr-6 ml-0;
}

/* RTL Padding */
[dir="rtl"] .pr-3 {
    @apply pl-3 pr-0;
}

[dir="rtl"] .pr-4 {
    @apply pl-4 pr-0;
}

[dir="rtl"] .pr-6 {
    @apply pl-6 pr-0;
}

[dir="rtl"] .pl-3 {
    @apply pr-3 pl-0;
}

[dir="rtl"] .pl-4 {
    @apply pr-4 pl-0;
}

[dir="rtl"] .pl-6 {
    @apply pr-6 pl-0;
}

/* RTL Positioning */
[dir="rtl"] .left-0 {
    @apply right-0 left-auto;
}

[dir="rtl"] .right-0 {
    @apply left-0 right-auto;
}

[dir="rtl"] .left-4 {
    @apply right-4 left-auto;
}

[dir="rtl"] .right-4 {
    @apply left-4 right-auto;
}

/* RTL Borders */
[dir="rtl"] .border-l {
    @apply border-r border-l-0;
}

[dir="rtl"] .border-r {
    @apply border-l border-r-0;
}

[dir="rtl"] .border-l-2 {
    @apply border-r-2 border-l-0;
}

[dir="rtl"] .border-r-2 {
    @apply border-l-2 border-r-0;
}

/* RTL Rounded Corners */
[dir="rtl"] .rounded-l {
    @apply rounded-r rounded-l-none;
}

[dir="rtl"] .rounded-r {
    @apply rounded-l rounded-r-none;
}

[dir="rtl"] .rounded-tl {
    @apply rounded-tr rounded-tl-none;
}

[dir="rtl"] .rounded-tr {
    @apply rounded-tl rounded-tr-none;
}

[dir="rtl"] .rounded-bl {
    @apply rounded-br rounded-bl-none;
}

[dir="rtl"] .rounded-br {
    @apply rounded-bl rounded-br-none;
}

/* RTL Flexbox */
[dir="rtl"] .justify-start {
    @apply justify-end;
}

[dir="rtl"] .justify-end {
    @apply justify-start;
}

[dir="rtl"] .items-start {
    @apply items-end;
}

[dir="rtl"] .items-end {
    @apply items-start;
}

/* RTL Text Alignment */
[dir="rtl"] .text-left {
    @apply text-right;
}

[dir="rtl"] .text-right {
    @apply text-left;
}

/* RTL Transform */
[dir="rtl"] .transform {
    transform: scaleX(-1);
}

[dir="rtl"] .transform svg {
    transform: scaleX(-1);
}

/* RTL Specific Components */
[dir="rtl"] .admin-stat-card {
    @apply text-right;
}

[dir="rtl"] .admin-badge {
    @apply text-right;
}

[dir="rtl"] .admin-alert {
    @apply text-right;
}

/* RTL Mobile Responsive */
@media (max-width: 768px) {
    [dir="rtl"] .admin-sidebar {
        @apply translate-x-full;
    }
    
    [dir="rtl"] .admin-sidebar.mobile-open {
        @apply -translate-x-0;
    }
    
    [dir="rtl"] .admin-main {
        @apply mr-0;
    }
}

/* RTL Typography */
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
    @apply text-right;
}

[dir="rtl"] p {
    @apply text-right;
}

[dir="rtl"] .admin-form-group label {
    @apply text-right;
}

/* RTL Icons */
[dir="rtl"] .admin-nav-icon,
[dir="rtl"] .admin-search-icon {
    transform: scaleX(-1);
}

/* RTL Animations */
[dir="rtl"] .admin-sidebar {
    transform: translateX(100%);
}

[dir="rtl"] .admin-sidebar.open {
    transform: translateX(0);
}

/* RTL Custom Properties */
[dir="rtl"] {
    --admin-sidebar-width: 16rem;
    --admin-sidebar-collapsed-width: 4rem;
}

/* RTL Grid */
[dir="rtl"] .grid {
    direction: rtl;
}

[dir="rtl"] .grid > * {
    direction: ltr;
}

/* RTL Tables */
[dir="rtl"] .admin-table {
    direction: rtl;
}

[dir="rtl"] .admin-table th:first-child {
    @apply rounded-tr-lg rounded-tl-none;
}

[dir="rtl"] .admin-table th:last-child {
    @apply rounded-tl-lg rounded-tr-none;
}

/* RTL Form Controls */
[dir="rtl"] input[type="checkbox"] {
    @apply ml-2 mr-0;
}

[dir="rtl"] input[type="radio"] {
    @apply ml-2 mr-0;
}

/* RTL Loading States */
[dir="rtl"] .admin-spinner {
    @apply ml-3 mr-0;
}

/* RTL Tooltips */
[dir="rtl"] .tooltip {
    direction: rtl;
}

/* RTL Modal */
[dir="rtl"] .modal {
    direction: rtl;
}

[dir="rtl"] .modal-content {
    @apply text-right;
}

/* RTL Notifications */
[dir="rtl"] .notification {
    @apply text-right;
}

[dir="rtl"] .notification-icon {
    @apply ml-3 mr-0;
}
