/* Admin Panel Specific Styles */

/* Enhanced Admin Layout */
.admin-layout {
    @apply min-h-screen bg-gray-50;
}

.admin-sidebar {
    @apply fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out;
}

.admin-sidebar.collapsed {
    @apply -translate-x-full lg:translate-x-0 lg:w-16;
}

.admin-main {
    @apply flex-1 lg:ml-64 transition-all duration-300 ease-in-out;
}

.admin-main.sidebar-collapsed {
    @apply lg:ml-16;
}

/* Admin Navigation */
.admin-nav-item {
    @apply flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-200;
}

.admin-nav-item.active {
    @apply bg-blue-100 text-blue-700 border-r-2 border-blue-500;
}

.admin-nav-icon {
    @apply w-5 h-5 mr-3 transition-all duration-200;
}

.admin-nav-text {
    @apply transition-opacity duration-200;
}

.sidebar-collapsed .admin-nav-text {
    @apply opacity-0 lg:hidden;
}

.sidebar-collapsed .admin-nav-icon {
    @apply mr-0;
}

/* Admin Cards */
.admin-card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200;
}

.admin-stat-card {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg p-6 shadow-lg;
}

.admin-stat-card.green {
    @apply from-green-500 to-green-600;
}

.admin-stat-card.yellow {
    @apply from-yellow-500 to-yellow-600;
}

.admin-stat-card.red {
    @apply from-red-500 to-red-600;
}

.admin-stat-card.purple {
    @apply from-purple-500 to-purple-600;
}

.admin-stat-card.indigo {
    @apply from-indigo-500 to-indigo-600;
}

/* Admin Tables */
.admin-table {
    @apply w-full bg-white rounded-lg shadow-sm overflow-hidden;
}

.admin-table thead {
    @apply bg-gray-50;
}

.admin-table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.admin-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
}

.admin-table tr:hover {
    @apply bg-gray-50;
}

/* Admin Buttons */
.btn-admin-primary {
    @apply inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition ease-in-out duration-150;
}

.btn-admin-secondary {
    @apply inline-flex items-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-25 transition ease-in-out duration-150;
}

.btn-admin-danger {
    @apply inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150;
}

.btn-admin-success {
    @apply inline-flex items-center px-4 py-2 bg-green-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-700 focus:bg-green-700 active:bg-green-900 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition ease-in-out duration-150;
}

/* Admin Forms */
.admin-form-group {
    @apply mb-6;
}

.admin-form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
}

.admin-form-input {
    @apply w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50;
}

.admin-form-textarea {
    @apply w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 resize-vertical;
}

.admin-form-select {
    @apply w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50;
}

.admin-form-checkbox {
    @apply rounded text-blue-600 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50;
}

.admin-form-error {
    @apply mt-1 text-sm text-red-600;
}

/* Admin Badges */
.admin-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.admin-badge.success {
    @apply bg-green-100 text-green-800;
}

.admin-badge.warning {
    @apply bg-yellow-100 text-yellow-800;
}

.admin-badge.danger {
    @apply bg-red-100 text-red-800;
}

.admin-badge.info {
    @apply bg-blue-100 text-blue-800;
}

.admin-badge.secondary {
    @apply bg-gray-100 text-gray-800;
}

/* Admin Alerts */
.admin-alert {
    @apply p-4 rounded-md mb-4;
}

.admin-alert.success {
    @apply bg-green-50 border border-green-200 text-green-800;
}

.admin-alert.error {
    @apply bg-red-50 border border-red-200 text-red-800;
}

.admin-alert.warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
}

.admin-alert.info {
    @apply bg-blue-50 border border-blue-200 text-blue-800;
}

/* Admin Loading States */
.admin-loading {
    @apply inline-flex items-center;
}

.admin-spinner {
    @apply animate-spin -ml-1 mr-3 h-5 w-5 text-current;
}

/* Admin Dropdown */
.admin-dropdown {
    @apply relative inline-block text-left;
}

.admin-dropdown-menu {
    @apply absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none;
}

.admin-dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
}

/* Admin Pagination */
.admin-pagination {
    @apply flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6;
}

/* Admin Search */
.admin-search {
    @apply relative;
}

.admin-search-input {
    @apply block w-full rounded-md border-gray-300 pl-10 focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
}

.admin-search-icon {
    @apply absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none;
}

/* Admin Filters */
.admin-filters {
    @apply bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-6;
}

.admin-filter-group {
    @apply flex flex-wrap gap-4 items-end;
}

/* Admin Breadcrumbs */
.admin-breadcrumbs {
    @apply flex items-center space-x-2 text-sm text-gray-500 mb-6;
}

.admin-breadcrumb-item {
    @apply hover:text-gray-700;
}

.admin-breadcrumb-separator {
    @apply text-gray-400;
}

/* Admin Mobile Responsive */
@media (max-width: 768px) {
    .admin-sidebar {
        @apply -translate-x-full;
    }
    
    .admin-sidebar.mobile-open {
        @apply translate-x-0;
    }
    
    .admin-main {
        @apply ml-0;
    }
    
    .admin-table {
        @apply text-xs;
    }
    
    .admin-table th,
    .admin-table td {
        @apply px-3 py-2;
    }
}

/* Admin Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .admin-layout {
        @apply bg-gray-900;
    }
    
    .admin-sidebar {
        @apply bg-gray-800 border-gray-700;
    }
    
    .admin-card {
        @apply bg-gray-800 border-gray-700;
    }
    
    .admin-nav-item {
        @apply text-gray-300 hover:bg-gray-700 hover:text-white;
    }
    
    .admin-nav-item.active {
        @apply bg-gray-700 text-white border-blue-400;
    }
}
