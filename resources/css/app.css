@tailwind base;
@tailwind components;
@tailwind utilities;

/* Advanced Background Animations */
@keyframes gradient-x {

    0%,
    100% {
        transform: translateX(0%);
    }

    50% {
        transform: translateX(100%);
    }
}

@keyframes floating-animation {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    33% {
        transform: translateY(-20px) rotate(1deg);
    }

    66% {
        transform: translateY(10px) rotate(-1deg);
    }
}

@keyframes pulse-glow {

    0%,
    100% {
        opacity: 0.4;
        transform: scale(1);
    }

    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes drift {

    0%,
    100% {
        transform: translateX(0px) translateY(0px);
    }

    25% {
        transform: translateX(10px) translateY(-10px);
    }

    50% {
        transform: translateX(-5px) translateY(-20px);
    }

    75% {
        transform: translateX(-10px) translateY(-5px);
    }
}

@keyframes float {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-15px) rotate(2deg);
    }
}

@keyframes float-delayed {

    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }

    50% {
        transform: translateY(-12px) rotate(-2deg);
    }
}

/* Apply animations */
.animate-gradient-x {
    animation: gradient-x 15s ease infinite;
}

.floating-animation {
    animation: floating-animation 6s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulse-glow 4s ease-in-out infinite;
}

.animate-drift {
    animation: drift 8s ease-in-out infinite;
}

.animate-float {
    animation: float 4s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float-delayed 4s ease-in-out infinite;
    animation-delay: 1s;
}

/* Modern card enhancements */
.modern-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Glass effect enhancements */
.glass-effect {
    backdrop-filter: blur(16px);
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient text improvements */
.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern button enhancements */
.modern-btn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

/* Fade in animations */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.stagger-animation {
    transition-delay: calc(var(--stagger) * 0.1s);
}