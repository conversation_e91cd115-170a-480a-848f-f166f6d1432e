/* Plan Selector Styles */

.plan-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.plan-card.selected {
    ring: 3px;
    ring-color: rgb(168 85 247);
    ring-opacity: 0.5;
    transform: translateY(-5px);
}

.plan-card.featured {
    position: relative;
}

.plan-card.featured::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Plan comparison modal styles */
.comparison-modal {
    backdrop-filter: blur(4px);
}

.comparison-table {
    border-collapse: separate;
    border-spacing: 0;
}

.comparison-table th,
.comparison-table td {
    border-bottom: 1px solid #e5e7eb;
    padding: 12px 16px;
}

.comparison-table th {
    background-color: #f9fafb;
    font-weight: 600;
    text-align: center;
}

.comparison-table tr:hover {
    background-color: #f9fafb;
}

/* Billing toggle styles */
.billing-toggle {
    position: relative;
    display: inline-flex;
    align-items: center;
    background-color: #f3f4f6;
    border-radius: 9999px;
    padding: 4px;
    margin-bottom: 2rem;
}

.billing-toggle input {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.billing-toggle label {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 9999px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 1;
}

.billing-toggle input:checked + label {
    color: white;
}

.billing-toggle::after {
    content: '';
    position: absolute;
    top: 4px;
    left: 4px;
    width: calc(50% - 4px);
    height: calc(100% - 8px);
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    border-radius: 9999px;
    transition: transform 0.2s ease;
    z-index: 0;
}

.billing-toggle input:checked ~ .toggle-slider {
    transform: translateX(100%);
}

/* Savings badge */
.savings-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 9999px;
    transform: rotate(12deg);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Plan features list */
.plan-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.plan-features li {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 0.875rem;
}

.plan-features li:last-child {
    margin-bottom: 0;
}

.plan-features .check-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    color: #10b981;
    flex-shrink: 0;
}

/* CTA button animations */
.cta-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-button:hover::before {
    left: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .plan-card {
        margin-bottom: 1.5rem;
    }
    
    .plan-card.scale-105 {
        transform: none;
    }
    
    .plan-card:hover {
        transform: translateY(-2px);
    }
}

/* Loading states */
.plan-loading {
    opacity: 0.6;
    pointer-events: none;
}

.plan-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Smooth scrolling enhancement */
html {
    scroll-behavior: smooth;
}

/* Focus states for accessibility */
.plan-card:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.cta-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}
