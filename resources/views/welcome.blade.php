<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Content Spark - AI Content Generator</title>
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @if (app()->getLocale() === 'ar')
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    @endif
    @vite(['resources/css/app.css', 'resources/css/plan-selector.css', 'resources/css/rtl.css', 'resources/js/app.js', 'resources/js/plan-selector.js'])
</head>

<body class="antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 min-h-screen flex flex-col">
    <!-- Header -->
    <header class="bg-white/80 backdrop-blur shadow-sm sticky top-0 z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <a href="{{ route('home') }}"
                        class="text-3xl font-extrabold text-gradient bg-gradient-to-r from-blue-600 via-purple-500 to-fuchsia-500 bg-clip-text text-transparent tracking-tight hover:opacity-80 transition-opacity">
                        Content Spark
                    </a>
                </div>
                <nav class="flex space-x-4">
                    @auth
                        <a href="{{ url('/dashboard') }}"
                            class="text-slate-700 hover:text-fuchsia-600 px-3 py-2 rounded-md text-sm font-medium transition">Dashboard</a>
                    @else
                        <a href="{{ route('login') }}"
                            class="text-slate-700 hover:text-fuchsia-600 px-3 py-2 rounded-md text-sm font-medium transition">Log
                            in</a>
                        @if (Route::has('register'))
                            <a href="{{ route('register') }}"
                                class="bg-gradient-to-r from-blue-600 to-fuchsia-500 hover:from-blue-700 hover:to-fuchsia-600 text-white px-4 py-2 rounded-md text-sm font-semibold shadow transition">Get
                                Started</a>
                        @endif
                    @endauth
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-50 via-fuchsia-50 to-white py-24 overflow-hidden">
        <div class="absolute inset-0 pointer-events-none">
            <div
                class="absolute -top-32 -left-32 w-96 h-96 bg-gradient-to-br from-blue-400/30 to-fuchsia-400/10 rounded-full blur-3xl">
            </div>
            <div
                class="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-tr from-fuchsia-400/20 to-blue-400/10 rounded-full blur-2xl">
            </div>
        </div>
        <div class="relative max-w-4xl mx-auto text-center px-4">
            <h1 class="text-5xl sm:text-6xl font-extrabold text-slate-900 mb-6 leading-tight drop-shadow">
                {{ __('app.hero_title') }}</h1>
            <p class="text-lg sm:text-xl text-slate-600 mb-10">{{ __('app.hero_subtitle') }}</p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                @php
                    $freePlan = \App\Models\Plan::where('monthly_price', 0)->where('is_active', true)->first();
                @endphp

                @if ($freePlan)
                    <a href="{{ route('register', ['plan' => $freePlan->id]) }}"
                        class="hero-cta inline-block bg-gradient-to-r from-blue-600 to-fuchsia-500 hover:from-blue-700 hover:to-fuchsia-600 text-white px-10 py-4 rounded-xl font-bold text-lg shadow-lg transition transform hover:scale-105">
                        {{ __('app.hero_cta') }}
                    </a>
                @else
                    <a href="{{ route('register') }}"
                        class="hero-cta inline-block bg-gradient-to-r from-blue-600 to-fuchsia-500 hover:from-blue-700 hover:to-fuchsia-600 text-white px-10 py-4 rounded-xl font-bold text-lg shadow-lg transition transform hover:scale-105">
                        {{ __('app.get_started') }}
                    </a>
                @endif

                <a href="#pricing"
                    class="inline-block border-2 border-fuchsia-500 text-fuchsia-600 hover:bg-fuchsia-50 px-10 py-4 rounded-xl font-bold text-lg transition transform hover:scale-105">
                    {{ __('app.hero_cta_secondary') }}
                </a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gradient-to-br from-white via-blue-50 to-fuchsia-50">
        <div class="max-w-6xl mx-auto px-4">
            <h2 class="text-3xl font-extrabold text-center text-slate-900 mb-14">{{ __('app.features_title') }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <div class="bg-white rounded-2xl p-10 text-center shadow-lg border-t-4 border-blue-500">
                    <div class="flex justify-center mb-4"><span
                            class="inline-flex items-center justify-center h-14 w-14 rounded-full bg-blue-100"><svg
                                class="h-7 w-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg></span></div>
                    <h3 class="font-bold text-lg mb-2 text-blue-700">Lightning Fast</h3>
                    <p class="text-slate-600">Generate content in seconds with advanced AI models.</p>
                </div>
                <div class="bg-white rounded-2xl p-10 text-center shadow-lg border-t-4 border-fuchsia-500">
                    <div class="flex justify-center mb-4"><span
                            class="inline-flex items-center justify-center h-14 w-14 rounded-full bg-fuchsia-100"><svg
                                class="h-7 w-7 text-fuchsia-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
                                </path>
                            </svg></span></div>
                    <h3 class="font-bold text-lg mb-2 text-fuchsia-700">Multi-Language</h3>
                    <p class="text-slate-600">Create content in English, Arabic, French, and more.</p>
                </div>
                <div class="bg-white rounded-2xl p-10 text-center shadow-lg border-t-4 border-purple-500">
                    <div class="flex justify-center mb-4"><span
                            class="inline-flex items-center justify-center h-14 w-14 rounded-full bg-purple-100"><svg
                                class="h-7 w-7 text-purple-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z">
                                </path>
                            </svg></span></div>
                    <h3 class="font-bold text-lg mb-2 text-purple-700">Multiple Content Types</h3>
                    <p class="text-slate-600">Social posts, product descriptions, blog outlines, and more.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Content Types Showcase -->
    <section class="py-20 bg-gradient-to-br from-fuchsia-50 via-blue-50 to-white">
        <div class="max-w-6xl mx-auto px-4">
            <h2 class="text-3xl font-extrabold text-center text-slate-900 mb-14">Content Types We Offer</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10">
                <div
                    class="bg-white rounded-2xl shadow-lg p-8 text-center border-b-4 border-blue-400 hover:scale-105 transition">
                    <img src="/images/social-media.jpg" alt="Social Media" class="mx-auto h-20 mb-6 rounded-xl shadow">
                    <h3 class="font-bold text-lg mb-2 text-blue-700">Social Media Posts</h3>
                    <p class="text-slate-600 text-sm">Engage your audience with catchy posts for all platforms.</p>
                </div>
                <div
                    class="bg-white rounded-2xl shadow-lg p-8 text-center border-b-4 border-fuchsia-400 hover:scale-105 transition">
                    <img src="/images/product-description.jpg" alt="Product Description"
                        class="mx-auto h-20 mb-6 rounded-xl shadow">
                    <h3 class="font-bold text-lg mb-2 text-fuchsia-700">Product Descriptions</h3>
                    <p class="text-slate-600 text-sm">Boost sales with persuasive product copy for e-commerce.</p>
                </div>
                <div
                    class="bg-white rounded-2xl shadow-lg p-8 text-center border-b-4 border-purple-400 hover:scale-105 transition">
                    <img src="/images/youtube-ideas.jpg" alt="YouTube Ideas"
                        class="mx-auto h-20 mb-6 rounded-xl shadow">
                    <h3 class="font-bold text-lg mb-2 text-purple-700">YouTube Video Ideas</h3>
                    <p class="text-slate-600 text-sm">Get creative video ideas and outlines for your channel.</p>
                </div>
                <div
                    class="bg-white rounded-2xl shadow-lg p-8 text-center border-b-4 border-blue-400 hover:scale-105 transition">
                    <img src="/images/blog-outline.jpg" alt="Blog Outline"
                        class="mx-auto h-20 mb-6 rounded-xl shadow">
                    <h3 class="font-bold text-lg mb-2 text-blue-700">Blog Post Outlines</h3>
                    <p class="text-slate-600 text-sm">Structure your blog posts for clarity and impact.</p>
                </div>
                <div
                    class="bg-white rounded-2xl shadow-lg p-8 text-center border-b-4 border-fuchsia-400 hover:scale-105 transition">
                    <img src="/images/email-marketing.jpg" alt="Email Marketing"
                        class="mx-auto h-20 mb-6 rounded-xl shadow">
                    <h3 class="font-bold text-lg mb-2 text-fuchsia-700">Email Marketing Content</h3>
                    <p class="text-slate-600 text-sm">Craft emails that convert and nurture your leads.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Plans Preview -->
    <section id="pricing" class="py-20 bg-gradient-to-br from-white via-blue-50 to-fuchsia-50">
        <div class="max-w-5xl mx-auto px-4">
            <h2 class="text-3xl font-extrabold text-center text-slate-900 mb-8">Choose Your Plan & Get Started</h2>
            <p class="text-center text-slate-600 mb-8 text-lg">Select a plan below and start creating amazing content
                in minutes</p>

            <!-- Billing Frequency Toggle -->
            <div class="flex items-center justify-center mb-12">
                <span class="text-sm font-medium text-gray-700 mr-3">Monthly</span>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="pricing-toggle" class="sr-only peer">
                    <div
                        class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600">
                    </div>
                </label>
                <span class="text-sm font-medium text-gray-700 ml-3">Annual</span>
                <span
                    class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Save up to 20%
                </span>
            </div>

            @php
                $plans = \App\Models\Plan::where('is_active', true)->orderBy('monthly_price')->get();
                $userSubscription = auth()->user()?->subscription;
                $currentPlanId = $userSubscription?->plan_id;
            @endphp

            <div class="grid grid-cols-1 md:grid-cols-{{ $plans->count() }} gap-8">
                @foreach ($plans as $index => $plan)
                    <div data-plan-id="{{ $plan->id }}"
                        class="plan-card relative bg-white rounded-2xl p-8 text-center shadow-lg border-b-4
                        @if ($currentPlanId === $plan->id) border-green-500 ring-2 ring-green-200
                        @elseif ($plan->is_featured || $plan->slug === 'pro') border-fuchsia-500 scale-105 bg-gradient-to-br from-blue-600 to-fuchsia-500 text-white
                        @elseif($plan->isFree())
                            border-blue-500
                        @else
                            border-purple-500 @endif
                        hover:scale-105 transition-transform duration-300 cursor-pointer">

                        @if ($currentPlanId === $plan->id)
                            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                <span class="bg-green-500 text-white px-4 py-1 rounded-full text-sm font-bold">
                                    {{ __('app.current_plan') }}
                                </span>
                            </div>
                        @elseif ($plan->is_featured || $plan->slug === 'pro')
                            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                                <span class="bg-yellow-400 text-yellow-900 px-4 py-1 rounded-full text-sm font-bold">
                                    Most Popular
                                </span>
                            </div>
                        @endif

                        <h3
                            class="font-bold text-xl mb-2
                            @if ($plan->is_featured || $plan->slug === 'pro') text-white
                            @elseif($plan->isFree()) text-blue-700
                            @else text-purple-700 @endif">
                            {{ $plan->name }}
                        </h3>

                        <!-- Monthly Price -->
                        <div class="monthly-price price-amount">
                            <p
                                class="text-4xl font-extrabold mb-4
                                @if ($plan->is_featured || $plan->slug === 'pro') text-white
                                @elseif($plan->isFree()) text-blue-700
                                @else text-purple-700 @endif">
                                ${{ number_format($plan->monthly_price, 0) }}
                                <span class="text-base font-normal">/mo</span>
                            </p>
                        </div>

                        <!-- Annual Price -->
                        <div class="annual-price price-amount" style="display: none;">
                            <p
                                class="text-4xl font-extrabold mb-2
                                @if ($plan->is_featured || $plan->slug === 'pro') text-white
                                @elseif($plan->isFree()) text-blue-700
                                @else text-purple-700 @endif">
                                ${{ number_format($plan->annual_price, 0) }}
                                <span class="text-base font-normal">/year</span>
                            </p>
                            @if (!$plan->isFree() && $plan->getAnnualSavingsPercentage() > 0)
                                <p class="text-sm text-green-600 font-medium mb-2">
                                    Save {{ $plan->getAnnualSavingsPercentage() }}% (${!! number_format($plan->getAnnualSavings(), 0) !!}/year)
                                </p>
                            @endif
                        </div>

                        <!-- Savings Badge (shown only in annual mode) -->
                        @if (!$plan->isFree() && $plan->getAnnualSavingsPercentage() > 0)
                            <div class="savings-badge absolute top-4 right-4" style="display: none;">
                                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                                    Save {{ $plan->getAnnualSavingsPercentage() }}%
                                </span>
                            </div>
                        @endif

                        <p
                            class="text-sm mb-6
                            @if ($plan->is_featured || $plan->slug === 'pro') text-blue-700
                            @else text-slate-600 @endif">
                            {{ $plan->description }}
                        </p>

                        <ul
                            class="mb-8 space-y-2 text-sm
                            @if ($plan->is_featured || $plan->slug === 'pro') text-blue-700
                            @else text-slate-600 @endif">
                            @if ($plan->monthly_generations_limit === -1)
                                <li class="flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    Unlimited generations
                                </li>
                            @else
                                <li class="flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    {{ number_format($plan->monthly_generations_limit) }} generations/month
                                </li>
                            @endif

                            <li class="flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                All content types
                            </li>

                            <li class="flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                Multi-language support
                            </li>

                            @if ($plan->isFree())
                                <li class="flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    Email support
                                </li>
                            @else
                                <li class="flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                    Priority support
                                </li>
                            @endif
                        </ul>

                        <div class="mt-8">
                            @auth
                                @if ($currentPlanId === $plan->id)
                                    <button disabled
                                        class="block w-full py-3 px-6 rounded-lg font-bold shadow bg-gray-100 text-gray-500 cursor-not-allowed">
                                        {{ __('app.current_plan') }}
                                    </button>
                                @elseif ($plan->isFree())
                                    <a href="{{ route('dashboard') }}"
                                        class="block w-full py-3 px-6 rounded-lg font-bold shadow transition
                                        bg-gradient-to-r from-blue-600 to-fuchsia-500 hover:from-blue-700 hover:to-fuchsia-600 text-white">
                                        {{ __('app.get_started') }}
                                    </a>
                                @else
                                    @php
                                        $currentPlan = $userSubscription?->plan;
                                        $isUpgrade =
                                            !$currentPlan || $plan->monthly_price > $currentPlan->monthly_price;
                                        $buttonText = $isUpgrade
                                            ? __('app.upgrade_to', ['plan' => $plan->name])
                                            : __('app.downgrade_to', ['plan' => $plan->name]);
                                    @endphp
                                    <form method="POST" action="{{ route('pricing.checkout', $plan) }}"
                                        class="checkout-form" id="checkout-form-{{ $plan->id }}">
                                        @csrf
                                        <input type="hidden" name="frequency" value="monthly">
                                        <button type="submit"
                                            class="block w-full py-3 px-6 rounded-lg font-bold shadow transition
                                            @if ($plan->is_featured || $plan->slug === 'pro') bg-white text-fuchsia-600 hover:bg-fuchsia-50
                                            @else
                                                bg-gradient-to-r from-purple-600 to-fuchsia-500 hover:from-purple-700 hover:to-fuchsia-600 text-white @endif">
                                            {{ $buttonText }}
                                        </button>
                                    </form>
                                @endif
                            @else
                                @if ($plan->isFree())
                                    <a href="{{ route('register', ['plan' => $plan->id]) }}"
                                        class="block w-full py-3 px-6 rounded-lg font-bold shadow transition
                                        bg-gradient-to-r from-blue-600 to-fuchsia-500 hover:from-blue-700 hover:to-fuchsia-600 text-white">
                                        Get Started Free
                                    </a>
                                @else
                                    <form method="POST" action="{{ route('pricing.checkout', $plan) }}"
                                        class="checkout-form" id="checkout-form-guest-{{ $plan->id }}">
                                        @csrf
                                        <input type="hidden" name="frequency" value="monthly">
                                        <button type="submit"
                                            class="block w-full py-3 px-6 rounded-lg font-bold shadow transition
                                            @if ($plan->is_featured || $plan->slug === 'pro') bg-white text-fuchsia-600 hover:bg-fuchsia-50
                                            @else
                                                bg-gradient-to-r from-purple-600 to-fuchsia-500 hover:from-purple-700 hover:to-fuchsia-600 text-white @endif">
                                            Start with {{ $plan->name }}
                                        </button>
                                    </form>
                                @endif
                            @endauth
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="mt-12 text-center">
                <button id="compare-plans"
                    class="mb-6 bg-slate-100 hover:bg-slate-200 text-slate-700 px-6 py-3 rounded-lg font-semibold transition">
                    Compare All Plans
                </button>

                <p class="text-slate-600 text-sm mb-4">
                    All plans include a 7-day free trial • No setup fees • Cancel anytime
                </p>
                <div class="flex justify-center space-x-6 text-sm text-slate-500">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        Secure payments
                    </span>
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        Instant activation
                    </span>
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        24/7 support
                    </span>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-20 bg-gradient-to-br from-fuchsia-50 via-blue-50 to-white">
        <div class="max-w-5xl mx-auto px-4">
            <h2 class="text-3xl font-extrabold text-center text-slate-900 mb-14">What Our Users Say</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                <div class="bg-white rounded-2xl p-8 shadow-lg text-center border-l-4 border-blue-400">
                    <p class="text-slate-700 italic mb-4">“Content Spark helped me save hours every week. The AI is
                        amazing!”</p>
                    <div class="font-bold text-blue-700">Sarah M.</div>
                    <div class="text-xs text-slate-400">Content Marketer</div>
                </div>
                <div class="bg-white rounded-2xl p-8 shadow-lg text-center border-l-4 border-fuchsia-400">
                    <p class="text-slate-700 italic mb-4">“The best tool for generating product descriptions. Highly
                        recommended.”</p>
                    <div class="font-bold text-fuchsia-700">James L.</div>
                    <div class="text-xs text-slate-400">E-commerce Owner</div>
                </div>
                <div class="bg-white rounded-2xl p-8 shadow-lg text-center border-l-4 border-purple-400">
                    <p class="text-slate-700 italic mb-4">“I love the multi-language support. It’s perfect for my
                        global audience.”</p>
                    <div class="font-bold text-purple-700">Amina K.</div>
                    <div class="text-xs text-slate-400">Blogger</div>
                </div>
            </div>
        </div>
    </section>

    @include('partials.faq')

    <!-- Footer -->
    <footer class="bg-gradient-to-r from-blue-900 via-fuchsia-900 to-purple-900 text-gray-200 py-12 mt-8">
        <div class="max-w-6xl mx-auto px-4 flex flex-col md:flex-row justify-between items-center">
            <div class="mb-6 md:mb-0">
                <span class="font-extrabold text-white text-2xl tracking-tight">Content Spark</span>
                <p class="text-gray-400 text-sm mt-2">&copy; {{ date('Y') }} Content Spark. All rights reserved.
                </p>
            </div>
            <div class="flex space-x-8">
                <a href="#" class="hover:text-white font-semibold transition">Privacy Policy</a>
                <a href="#" class="hover:text-white font-semibold transition">Terms of Service</a>
                <a href="#" class="hover:text-white font-semibold transition">Contact</a>
            </div>
        </div>
    </footer>
    <script src="{{ asset('js/pricing-toggle.js') }}"></script>
</body>

</html>
