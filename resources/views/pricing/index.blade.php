<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Pricing & Plans') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Current Subscription -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Subscription</h3>

                    @if ($subscription)
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="text-lg font-semibold text-gray-900">{{ $subscription->plan->name }} Plan
                                    </h4>
                                    <p class="text-sm text-gray-600">{{ $subscription->plan->description }}</p>
                                    <div class="mt-2 flex items-center space-x-4">
                                        <span class="text-sm text-gray-500">
                                            Generations: {{ $subscription->generations_used }} /
                                            {{ $subscription->plan->monthly_generations_limit === -1 ? 'Unlimited' : $subscription->plan->monthly_generations_limit }}
                                        </span>
                                        @if ($subscription->plan->monthly_generations_limit !== -1)
                                            <div class="flex-1 max-w-xs">
                                                <div class="bg-gray-200 rounded-full h-2">
                                                    <div class="bg-blue-600 h-2 rounded-full"
                                                        style="width: {{ ($subscription->generations_used / $subscription->plan->monthly_generations_limit) * 100 }}%">
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-2xl font-bold text-gray-900">
                                        ${{ $subscription->plan->monthly_price }}<span
                                            class="text-sm font-normal text-gray-500">/month</span>
                                    </p>
                                    @if ($subscription->plan->isFree())
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Free Plan
                                        </span>
                                    @else
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Active
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <p class="text-gray-500">No active subscription found.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Available Plans -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-6">Available Plans</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @foreach ($plans as $plan)
                            <div
                                class="border border-gray-200 rounded-lg p-6 {{ $plan->is_featured ? 'ring-2 ring-blue-500' : '' }}">
                                @if ($plan->is_featured)
                                    <div class="text-center mb-4">
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                            Most Popular
                                        </span>
                                    </div>
                                @endif

                                <div class="text-center">
                                    <h4 class="text-xl font-semibold text-gray-900">{{ $plan->name }}</h4>
                                    <p class="text-sm text-gray-600 mt-2">{{ $plan->description }}</p>

                                    <div class="mt-4">
                                        <span
                                            class="text-4xl font-bold text-gray-900">${{ $plan->monthly_price }}</span>
                                        <span class="text-sm text-gray-500">/month</span>
                                    </div>

                                    <div class="mt-4">
                                        <p class="text-sm text-gray-600">
                                            {{ $plan->monthly_generations_limit === -1 ? 'Unlimited' : $plan->monthly_generations_limit }}
                                            generations per month
                                        </p>
                                    </div>

                                    <div class="mt-6">
                                        @if ($subscription && $subscription->plan_id === $plan->id)
                                            <button disabled
                                                class="w-full bg-gray-100 text-gray-500 py-2 px-4 rounded-md cursor-not-allowed">
                                                Current Plan
                                            </button>
                                        @else
                                            @if ($plan->isFree())
                                                <a href="{{ route('dashboard') }}"
                                                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 inline-block text-center">
                                                    Get Started
                                                </a>
                                            @else
                                                <form method="POST" action="{{ route('pricing.checkout', $plan) }}">
                                                    @csrf
                                                    <button type="submit"
                                                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                                                        Upgrade to {{ $plan->name }}
                                                    </button>
                                                </form>
                                            @endif
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
