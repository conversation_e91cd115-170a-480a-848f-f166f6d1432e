<x-guest-layout>
    @php
        $pageSlug = 'pricing';
        $page = \App\Models\Page::where('slug', $pageSlug)->first();
        $translation = $page?->translation(app()->getLocale());
    @endphp

    <x-slot name="head">
        @include('partials.seo-meta', [
            'page' => $page,
            'translation' => $translation,
            'schemaType' => 'product',
            'ogType' => 'product',
        ])
    </x-slot>

    <!-- Hero Section -->
    <div class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 py-20">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <!-- Currency Symbols -->
            <div
                class="absolute top-16 left-20 w-16 h-16 bg-white/10 rounded-full animate-pulse flex items-center justify-center">
                <span class="text-white text-2xl font-bold">$</span>
            </div>
            <div
                class="absolute top-28 right-28 w-12 h-12 bg-yellow-300/20 rounded-full animate-bounce flex items-center justify-center">
                <span class="text-white text-lg font-bold">€</span>
            </div>
            <div
                class="absolute bottom-24 left-1/3 w-20 h-20 bg-green-300/10 rounded-full animate-ping flex items-center justify-center">
                <span class="text-white text-xl font-bold">£</span>
            </div>
            <div
                class="absolute bottom-20 right-1/4 w-14 h-14 bg-pink-300/20 rounded-full animate-pulse flex items-center justify-center">
                <span class="text-white text-lg font-bold">¥</span>
            </div>

            <!-- Pricing Icons -->
            <div class="absolute top-1/3 left-1/4 w-10 h-10 bg-white/20 rounded-lg animate-float">
                <svg class="w-full h-full p-2 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                </svg>
            </div>
            <div class="absolute top-1/2 right-1/3 w-8 h-8 bg-white/20 rounded-lg animate-float-delayed">
                <svg class="w-full h-full p-1 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z" />
                </svg>
            </div>

            <!-- Diamond Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0"
                    style="background-image: repeating-conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(255,255,255,0.1) 45deg, transparent 90deg);">
                </div>
            </div>

            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
                    {{ __('marketing.pricing_title') ?? 'Choose Your Plan' }}
                </h1>
                <p class="text-xl md:text-2xl text-purple-100 mb-8 max-w-3xl mx-auto drop-shadow-md">
                    {{ __('marketing.pricing_subtitle') ?? 'Select the perfect plan for your content generation needs. Start free and scale as you grow.' }}
                </p>
            </div>
        </div>
    </div>

    <!-- Pricing Content -->
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                @foreach ($plans as $plan)
                    <div
                        class="bg-white rounded-lg shadow-lg p-8 {{ $plan->is_featured ? 'ring-2 ring-blue-500 transform scale-105' : '' }}">
                        @if ($plan->is_featured)
                            <div class="text-center mb-4">
                                <span
                                    class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    Most Popular
                                </span>
                            </div>
                        @endif

                        <div class="text-center">
                            <h3 class="text-2xl font-bold text-gray-900">{{ $plan->name }}</h3>
                            <p class="text-gray-600 mt-2">{{ $plan->description }}</p>

                            <div class="mt-6">
                                <span class="text-5xl font-bold text-gray-900">${{ $plan->monthly_price }}</span>
                                <span class="text-lg text-gray-500">/month</span>
                            </div>

                            <div class="mt-6">
                                <p class="text-lg text-gray-700">
                                    {{ $plan->monthly_generations_limit === -1 ? 'Unlimited' : $plan->monthly_generations_limit }}
                                    generations per month
                                </p>
                            </div>

                            <div class="mt-8">
                                @auth
                                    @if ($plan->isFree())
                                        <a href="{{ route('dashboard') }}"
                                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 inline-block text-center">
                                            Get Started
                                        </a>
                                    @else
                                        <form method="POST" action="{{ route('pricing.checkout', $plan) }}">
                                            @csrf
                                            <button type="submit"
                                                class="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700">
                                                Choose {{ $plan->name }}
                                            </button>
                                        </form>
                                    @endif
                                @else
                                    @if ($plan->isFree())
                                        <a href="{{ route('register', ['plan' => $plan->id]) }}"
                                            class="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 inline-block text-center">
                                            Get Started
                                        </a>
                                    @else
                                        <form method="POST" action="{{ route('pricing.checkout', $plan) }}">
                                            @csrf
                                            <button type="submit"
                                                class="w-full bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700">
                                                Choose {{ $plan->name }}
                                            </button>
                                        </form>
                                    @endif
                                @endauth
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Features Comparison Section -->
            <div class="mt-20">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">All Plans Include</h2>
                    <p class="text-xl text-gray-600">Core features available in every Content Spark plan</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">AI-Powered Generation</h3>
                        <p class="text-gray-600 text-sm">Advanced AI models for high-quality content creation</p>
                    </div>

                    <div class="text-center">
                        <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Multi-Language Support</h3>
                        <p class="text-gray-600 text-sm">Generate content in 6+ languages including Arabic</p>
                    </div>

                    <div class="text-center">
                        <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Content Templates</h3>
                        <p class="text-gray-600 text-sm">Pre-built templates for various content types</p>
                    </div>

                    <div class="text-center">
                        <div class="bg-orange-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                            <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">24/7 Support</h3>
                        <p class="text-gray-600 text-sm">Round-the-clock customer support and assistance</p>
                    </div>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="mt-20 text-center">
                <div class="bg-gray-50 rounded-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Ready to get started?</h3>
                    <p class="text-gray-600 mb-6">Join thousands of creators and businesses using Content Spark to
                        generate amazing content.</p>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        @auth
                            <a href="{{ route('dashboard') }}"
                                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                                Go to Dashboard
                            </a>
                        @else
                            <a href="{{ route('register') }}"
                                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                                Start Free Trial
                            </a>
                        @endauth
                        <a href="{{ route('contact') }}"
                            class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            Contact Sales
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>
