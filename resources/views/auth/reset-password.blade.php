<x-auth-layout>
    <div class="space-y-6">
        <!-- Header -->
        <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-900">
                {{ __('messages.reset_password') ?? 'Reset your password' }}
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                {{ __('messages.reset_password_description') ?? 'Enter your new password below' }}
            </p>
        </div>

        <!-- Reset Form -->
        <form method="POST" action="{{ route('password.store') }}" class="space-y-6">
            @csrf

            <!-- Password Reset Token -->
            <input type="hidden" name="token" value="{{ $request->route('token') }}">

            <!-- Email Address -->
            <div>
                <x-input-label for="email" :value="__('Email')" class="text-gray-700 font-medium" />
                <x-text-input id="email"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="email" name="email" :value="old('email', $request->email)" required autofocus autocomplete="username"
                    placeholder="{{ __('messages.enter_email') ?? 'Enter your email' }}" />
                <x-input-error :messages="$errors->get('email')" class="mt-2" />
            </div>

            <!-- Password -->
            <div>
                <x-input-label for="password" :value="__('Password')" class="text-gray-700 font-medium" />
                <x-text-input id="password"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="password" name="password" required autocomplete="new-password"
                    placeholder="{{ __('messages.enter_new_password') ?? 'Enter your new password' }}" />
                <x-input-error :messages="$errors->get('password')" class="mt-2" />
            </div>

            <!-- Confirm Password -->
            <div>
                <x-input-label for="password_confirmation" :value="__('Confirm Password')" class="text-gray-700 font-medium" />
                <x-text-input id="password_confirmation"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="password" name="password_confirmation" required autocomplete="new-password"
                    placeholder="{{ __('messages.confirm_new_password') ?? 'Confirm your new password' }}" />
                <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit"
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105">
                    {{ __('Reset Password') }}
                </button>
            </div>
        </form>

        <!-- Back to Login Link -->
        <div class="text-center">
            <p class="text-sm text-gray-600">
                {{ __('messages.remember_password') ?? 'Remember your password?' }}
                <a href="{{ route('login') }}"
                    class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
                    {{ __('messages.back_to_login') ?? 'Back to login' }}
                </a>
            </p>
        </div>
    </div>
</x-auth-layout>
