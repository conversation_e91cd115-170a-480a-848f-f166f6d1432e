<x-auth-layout>
    <div class="space-y-6">
        <!-- Header -->
        <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-900">
                {{ __('messages.create_account') ?? 'Create your account' }}
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                {{ __('messages.join_content_spark') ?? 'Join Content Spark and start creating amazing content' }}
            </p>
        </div>

        <!-- Selected Plan Display -->
        @if ($selectedPlan)
            <div class="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-800">
                            {{ __('messages.selected_plan') ?? 'Selected Plan' }}: {{ $selectedPlan->name }}
                        </h3>
                        <div class="mt-1 text-sm text-blue-700">
                            <p>${{ $selectedPlan->monthly_price }}/month -
                                {{ $selectedPlan->monthly_generations_limit === -1 ? 'Unlimited' : $selectedPlan->monthly_generations_limit }}
                                generations</p>
                            <p class="mt-1">{{ $selectedPlan->description }}</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <!-- Registration Form -->
        <form method="POST" action="{{ route('register') }}" class="space-y-6">
            @csrf

            <!-- Hidden Plan Selection -->
            @if ($selectedPlan)
                <input type="hidden" name="selected_plan_id" value="{{ $selectedPlan->id }}">
            @endif

            <!-- Name -->
            <div>
                <x-input-label for="name" :value="__('Name')" class="text-gray-700 font-medium" />
                <x-text-input id="name"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="text" name="name" :value="old('name')" required autofocus autocomplete="name"
                    placeholder="{{ __('messages.enter_full_name') ?? 'Enter your full name' }}" />
                <x-input-error :messages="$errors->get('name')" class="mt-2" />
            </div>

            <!-- Email Address -->
            <div>
                <x-input-label for="email" :value="__('Email')" class="text-gray-700 font-medium" />
                <x-text-input id="email"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="email" name="email" :value="old('email')" required autocomplete="username"
                    placeholder="{{ __('messages.enter_email') ?? 'Enter your email' }}" />
                <x-input-error :messages="$errors->get('email')" class="mt-2" />
            </div>

            <!-- Password -->
            <div>
                <x-input-label for="password" :value="__('Password')" class="text-gray-700 font-medium" />
                <x-text-input id="password"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="password" name="password" required autocomplete="new-password"
                    placeholder="{{ __('messages.enter_password') ?? 'Enter your password' }}" />
                <x-input-error :messages="$errors->get('password')" class="mt-2" />
            </div>

            <!-- Confirm Password -->
            <div>
                <x-input-label for="password_confirmation" :value="__('Confirm Password')" class="text-gray-700 font-medium" />
                <x-text-input id="password_confirmation"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="password" name="password_confirmation" required autocomplete="new-password"
                    placeholder="{{ __('messages.confirm_password') ?? 'Confirm your password' }}" />
                <x-input-error :messages="$errors->get('password_confirmation')" class="mt-2" />
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit"
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105">
                    {{ __('Register') }}
                </button>
            </div>
        </form>

        <!-- Login Link -->
        <div class="text-center">
            <p class="text-sm text-gray-600">
                {{ __('Already registered?') }}
                <a href="{{ route('login') }}"
                    class="font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200">
                    {{ __('messages.sign_in') ?? 'Sign in' }}
                </a>
            </p>
        </div>
    </div>
</x-auth-layout>
