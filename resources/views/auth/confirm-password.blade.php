<x-auth-layout>
    <div class="space-y-6">
        <!-- Header -->
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-yellow-100 mb-4">
                <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
            </div>
            <h2 class="text-3xl font-bold text-gray-900">
                {{ __('messages.confirm_password_title') ?? 'Confirm your password' }}
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                {{ __('messages.confirm_password_description') ?? 'This is a secure area of the application. Please confirm your password before continuing.' }}
            </p>
        </div>

        <!-- Confirm Form -->
        <form method="POST" action="{{ route('password.confirm') }}" class="space-y-6">
            @csrf

            <!-- Password -->
            <div>
                <x-input-label for="password" :value="__('Password')" class="text-gray-700 font-medium" />
                <x-text-input id="password"
                    class="mt-2 block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                    type="password" name="password" required autocomplete="current-password"
                    placeholder="{{ __('messages.enter_password') ?? 'Enter your password' }}" />
                <x-input-error :messages="$errors->get('password')" class="mt-2" />
            </div>

            <!-- Submit Button -->
            <div>
                <button type="submit"
                    class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105">
                    {{ __('Confirm') }}
                </button>
            </div>
        </form>

        <!-- Cancel Link -->
        <div class="text-center">
            <a href="{{ url()->previous() }}"
                class="text-sm text-gray-600 hover:text-gray-500 font-medium transition-colors duration-200">
                {{ __('messages.cancel') ?? 'Cancel' }}
            </a>
        </div>
    </div>
</x-auth-layout>
