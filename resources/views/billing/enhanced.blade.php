<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Billing & Subscription') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8 space-y-6">
            
            <!-- Current Subscription -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Current Subscription</h3>
                    
                    @if($subscription)
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-medium text-blue-900">Plan</h4>
                                <p class="text-2xl font-bold text-blue-600">{{ $subscription->plan->name }}</p>
                                <p class="text-sm text-blue-700">
                                    ${{ number_format($subscription->plan->monthly_price, 2) }}/month
                                </p>
                            </div>
                            
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-medium text-green-900">Status</h4>
                                <p class="text-lg font-semibold text-green-600 capitalize">
                                    {{ $subscription->stripe_status ?? 'Active' }}
                                </p>
                                @if($subscription->cancelled_at)
                                    <p class="text-sm text-orange-600">
                                        Cancels on {{ $subscription->ends_at->format('M j, Y') }}
                                    </p>
                                @else
                                    <p class="text-sm text-green-700">
                                        Renews {{ $subscription->ends_at->format('M j, Y') }}
                                    </p>
                                @endif
                            </div>
                            
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-medium text-purple-900">Generations</h4>
                                @if($remainingGenerations === -1)
                                    <p class="text-lg font-semibold text-purple-600">Unlimited</p>
                                @else
                                    <p class="text-lg font-semibold text-purple-600">{{ $remainingGenerations }} remaining</p>
                                @endif
                                <p class="text-sm text-purple-700">{{ $thisMonthGenerations }} used this month</p>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex flex-wrap gap-3">
                            @if($subscription->cancelled_at)
                                <form method="POST" action="{{ route('billing.resume') }}">
                                    @csrf
                                    <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                        Resume Subscription
                                    </button>
                                </form>
                            @else
                                <button onclick="showCancelModal()" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                    Cancel Subscription
                                </button>
                            @endif
                            
                            <form method="POST" action="{{ route('billing.portal') }}">
                                @csrf
                                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Manage Payment Methods
                                </button>
                            </form>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No active subscription</h3>
                            <p class="mt-1 text-sm text-gray-500">Choose a plan to start generating content.</p>
                            <div class="mt-6">
                                <a href="{{ route('pricing.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    View Plans
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-blue-600">{{ $totalGenerations }}</div>
                            <div class="text-sm text-gray-600">Total Generations</div>
                        </div>
                        
                        <div class="text-center">
                            <div class="text-3xl font-bold text-green-600">{{ $thisMonthGenerations }}</div>
                            <div class="text-sm text-gray-600">This Month</div>
                        </div>
                        
                        <div class="text-center">
                            @if($remainingGenerations === -1)
                                <div class="text-3xl font-bold text-purple-600">∞</div>
                            @else
                                <div class="text-3xl font-bold text-purple-600">{{ $remainingGenerations }}</div>
                            @endif
                            <div class="text-sm text-gray-600">Remaining</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Plans -->
            @if($subscription)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Upgrade or Change Plan</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            @foreach($plans as $plan)
                                <div class="border rounded-lg p-6 {{ $subscription->plan_id === $plan->id ? 'border-blue-500 bg-blue-50' : 'border-gray-200' }}">
                                    <div class="text-center">
                                        <h4 class="text-xl font-semibold text-gray-900">{{ $plan->name }}</h4>
                                        <div class="mt-2">
                                            <span class="text-3xl font-bold text-gray-900">${{ number_format($plan->monthly_price, 0) }}</span>
                                            <span class="text-gray-600">/month</span>
                                        </div>
                                        <div class="mt-2">
                                            @if($plan->generation_limit === -1)
                                                <span class="text-sm text-gray-600">Unlimited generations</span>
                                            @else
                                                <span class="text-sm text-gray-600">{{ number_format($plan->generation_limit) }} generations/month</span>
                                            @endif
                                        </div>
                                        
                                        @if($subscription->plan_id === $plan->id)
                                            <div class="mt-4">
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                                    Current Plan
                                                </span>
                                            </div>
                                        @else
                                            <div class="mt-4">
                                                <a href="{{ route('pricing.index', ['plan' => $plan->id]) }}" 
                                                   class="w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                                    @if($plan->monthly_price > $subscription->plan->monthly_price)
                                                        Upgrade
                                                    @else
                                                        Downgrade
                                                    @endif
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Cancel Subscription Modal -->
    <div id="cancel-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Cancel Subscription</h3>
                
                <form method="POST" action="{{ route('billing.cancel') }}">
                    @csrf
                    
                    <div class="mb-4">
                        <label for="cancellation_reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Reason for cancellation (optional)
                        </label>
                        <select name="cancellation_reason" id="cancellation_reason" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="">Select a reason...</option>
                            <option value="too_expensive">Too expensive</option>
                            <option value="not_using">Not using enough</option>
                            <option value="missing_features">Missing features</option>
                            <option value="found_alternative">Found alternative</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    
                    <div class="mb-4">
                        <label for="feedback" class="block text-sm font-medium text-gray-700 mb-2">
                            Additional feedback (optional)
                        </label>
                        <textarea name="feedback" id="feedback" rows="3" 
                                  class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                  placeholder="Help us improve by sharing your feedback..."></textarea>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
                        <p class="text-sm text-yellow-800">
                            Your subscription will remain active until {{ $subscription?->ends_at?->format('M j, Y') }}. 
                            You can resume anytime before then.
                        </p>
                    </div>
                    
                    <div class="flex gap-3">
                        <button type="button" onclick="closeCancelModal()" 
                                class="flex-1 px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400">
                            Keep Subscription
                        </button>
                        <button type="submit" 
                                class="flex-1 px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700">
                            Cancel Subscription
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showCancelModal() {
            document.getElementById('cancel-modal').classList.remove('hidden');
        }

        function closeCancelModal() {
            document.getElementById('cancel-modal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('cancel-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCancelModal();
            }
        });
    </script>
</x-app-layout>
