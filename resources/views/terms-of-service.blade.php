<x-guest-layout>
    @php
        $pageSlug = 'terms-of-service';
        $page = \App\Models\Page::where('slug', $pageSlug)->first();
        $translation = $page?->translation(app()->getLocale());
    @endphp

    <x-slot name="head">
        @include('partials.seo-meta', [
            'page' => $page,
            'translation' => $translation,
            'schemaType' => 'webpage',
            'ogType' => 'website',
        ])
    </x-slot>

    <!-- Hero Section -->
    <div class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 py-20">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <!-- Geometric Shapes -->
            <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
            <div class="absolute top-32 right-20 w-16 h-16 bg-yellow-300/20 rounded-lg rotate-45 animate-bounce"></div>
            <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-300/20 rounded-full animate-ping"></div>
            <div class="absolute bottom-32 right-1/3 w-24 h-24 bg-green-300/10 rounded-lg rotate-12 animate-pulse"></div>

            <!-- Legal Icons -->
            <div
                class="absolute top-16 left-16 w-20 h-20 bg-white/10 rounded-full animate-pulse flex items-center justify-center">
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z" />
                    <polyline points="14,2 14,8 20,8" />
                    <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" />
                    <polyline points="10,9 9,9 8,9" />
                </svg>
            </div>
            <div
                class="absolute top-24 right-24 w-16 h-16 bg-yellow-300/20 rounded-lg animate-bounce flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                </svg>
            </div>
            <div
                class="absolute bottom-20 left-1/4 w-18 h-18 bg-green-300/20 rounded-full animate-ping flex items-center justify-center">
                <svg class="w-9 h-9 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>

            <!-- Floating Particles -->
            <div class="absolute top-1/4 left-1/3 w-2 h-2 bg-white/30 rounded-full animate-float"></div>
            <div class="absolute top-1/2 right-1/4 w-3 h-3 bg-blue-200/40 rounded-full animate-float-delayed"></div>
            <div class="absolute bottom-1/3 left-1/2 w-2 h-2 bg-purple-200/30 rounded-full animate-float"></div>

            <!-- Grid Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0"
                    style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 20px 20px;">
                </div>
            </div>

            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
                    {{ $translation?->title ?? 'Terms of Service' }}
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto drop-shadow-md">
                    {{ $translation?->description ?? 'Read Content Spark\'s terms of service. Understand your rights and responsibilities when using our AI content generation platform.' }}
                </p>
            </div>
        </div>
    </div>

    <!-- Content Section -->
    <div class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="prose prose-lg max-w-none">
                @if ($translation && $translation->content)
                    {!! $translation->content !!}
                @else
                    <div class="terms-content">
                        <h2>Terms of Service</h2>
                        <p><strong>Last updated:</strong> {{ date('F j, Y') }}</p>

                        <h3>Acceptance of Terms</h3>
                        <p>By accessing and using Content Spark, you accept and agree to be bound by the terms and
                            provision of this agreement.</p>

                        <h3>Use License</h3>
                        <p>Permission is granted to temporarily use Content Spark for personal and commercial content
                            generation purposes. This license shall automatically terminate if you violate any of these
                            restrictions.</p>

                        <h3>Service Description</h3>
                        <p>Content Spark provides AI-powered content generation services. We reserve the right to modify
                            or discontinue the service at any time.</p>

                        <h3>User Responsibilities</h3>
                        <ul>
                            <li>You are responsible for maintaining the confidentiality of your account</li>
                            <li>You agree not to use the service for illegal or unauthorized purposes</li>
                            <li>You retain ownership of content you create using our platform</li>
                            <li>You agree to comply with all applicable laws and regulations</li>
                        </ul>

                        <h3>Payment Terms</h3>
                        <p>Subscription fees are billed in advance on a monthly or annual basis. All payments are
                            processed securely through Stripe.</p>

                        <h3>Limitation of Liability</h3>
                        <p>Content Spark shall not be liable for any indirect, incidental, special, consequential, or
                            punitive damages.</p>

                        <h3>Contact Information</h3>
                        <p>Questions about the Terms of Service should be sent to <NAME_EMAIL>.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-gradient-to-r from-indigo-600 to-blue-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                {{ __('marketing.ready_to_start_title') }}
            </h2>
            <p class="text-xl text-indigo-100 mb-8">
                {{ __('marketing.get_started_subtitle') }}
            </p>
            <a href="{{ route('register') }}"
                class="inline-flex items-center px-8 py-4 bg-white text-indigo-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200">
                {{ __('marketing.get_started_title') }}
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6">
                    </path>
                </svg>
            </a>
        </div>
    </div>
</x-guest-layout>
