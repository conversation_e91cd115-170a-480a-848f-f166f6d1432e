 <!-- FAQ Section -->
 <section class="py-20 bg-gradient-to-br from-white via-blue-50 to-fuchsia-50">
     <div class="max-w-4xl mx-auto px-4">
         <h2 class="text-3xl font-extrabold text-center text-slate-900 mb-14">{{ __('app.faq_title') }}</h2>
         <div class="space-y-4">
             <div x-data="{ open: false }" class="bg-white rounded-xl shadow-lg border-l-4 border-blue-400">
                 <button @click="open = !open" class="w-full text-left p-8 focus:outline-none rounded-xl">
                     <div class="flex justify-between items-center">
                         <h3 class="font-bold text-lg text-blue-700">{{ __('app.faq_how_generate_q') }}</h3>
                         <svg class="w-5 h-5 text-blue-700 transition-transform duration-200"
                             :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                             </path>
                         </svg>
                     </div>
                 </button>
                 <div x-show="open" x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95" class="px-8 pb-8">
                     <p class="text-slate-600">{{ __('app.faq_how_generate_a') }}</p>
                 </div>
             </div>

             <div x-data="{ open: false }" class="bg-white rounded-xl shadow-lg border-l-4 border-fuchsia-400">
                 <button @click="open = !open" class="w-full text-left p-8 focus:outline-none rounded-xl">
                     <div class="flex justify-between items-center">
                         <h3 class="font-bold text-lg text-fuchsia-700">{{ __('app.faq_free_q') }}</h3>
                         <svg class="w-5 h-5 text-fuchsia-700 transition-transform duration-200"
                             :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                             </path>
                         </svg>
                     </div>
                 </button>
                 <div x-show="open" x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95" class="px-8 pb-8">
                     <p class="text-slate-600">{{ __('app.faq_free_a') }}</p>
                 </div>
             </div>

             <div x-data="{ open: false }" class="bg-white rounded-xl shadow-lg border-l-4 border-purple-400">
                 <button @click="open = !open" class="w-full text-left p-8 focus:outline-none rounded-xl">
                     <div class="flex justify-between items-center">
                         <h3 class="font-bold text-lg text-purple-700">{{ __('app.faq_languages_q') }}</h3>
                         <svg class="w-5 h-5 text-purple-700 transition-transform duration-200"
                             :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                             </path>
                         </svg>
                     </div>
                 </button>
                 <div x-show="open" x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95" class="px-8 pb-8">
                     <p class="text-slate-600">{{ __('app.faq_languages_a') }}</p>
                 </div>
             </div>

             <div x-data="{ open: false }" class="bg-white rounded-xl shadow-lg border-l-4 border-blue-400">
                 <button @click="open = !open" class="w-full text-left p-8 focus:outline-none  rounded-xl">
                     <div class="flex justify-between items-center">
                         <h3 class="font-bold text-lg text-blue-700">{{ __('app.faq_secure_q') }}</h3>
                         <svg class="w-5 h-5 text-blue-700 transition-transform duration-200"
                             :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7">
                             </path>
                         </svg>
                     </div>
                 </button>
                 <div x-show="open" x-transition:enter="transition ease-out duration-300"
                     x-transition:enter-start="opacity-0 transform scale-95"
                     x-transition:enter-end="opacity-100 transform scale-100"
                     x-transition:leave="transition ease-in duration-200"
                     x-transition:leave-start="opacity-100 transform scale-100"
                     x-transition:leave-end="opacity-0 transform scale-95" class="px-8 pb-8">
                     <p class="text-slate-600">{{ __('app.faq_secure_a') }}</p>
                 </div>
             </div>
         </div>
     </div>
 </section>
