{{--
    Structured Data (JSON-LD) Schemas
    Usage: Included automatically from seo-meta partial
--}}

@php
    $baseUrl = url('/');
    $currentUrl = $canonicalUrl ?? request()->url();
    $logoUrl = asset('images/content-spark-logo.png');
    $organizationData = [
        '@context' => 'https://schema.org',
        '@type' => 'Organization',
        'name' => 'Content Spark',
        'url' => $baseUrl,
        'logo' => $logoUrl,
        'description' => 'AI-powered content generation platform for businesses and creators',
        'foundingDate' => '2024',
        'contactPoint' => [
            '@type' => 'ContactPoint',
            'telephone' => '******-CONTENT',
            'contactType' => 'customer service',
            'email' => '<EMAIL>',
            'availableLanguage' => ['English', 'Arabic', 'French', 'Spanish', 'Portuguese', 'Russian']
        ],
        'sameAs' => [
            'https://twitter.com/ContentSpark',
            'https://linkedin.com/company/content-spark',
            'https://facebook.com/ContentSpark'
        ]
    ];
@endphp

<script type="application/ld+json">
@if($schemaType === 'organization')
{!! json_encode($organizationData, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) !!}
@elseif($schemaType === 'website')
{
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Content Spark",
    "url": "{{ $baseUrl }}",
    "description": "{{ $metaDescription }}",
    "publisher": {!! json_encode($organizationData, JSON_UNESCAPED_SLASHES) !!},
    "potentialAction": {
        "@type": "SearchAction",
        "target": {
            "@type": "EntryPoint",
            "urlTemplate": "{{ $baseUrl }}/search?q={search_term_string}"
        },
        "query-input": "required name=search_term_string"
    },
    "inLanguage": ["en", "ar", "fr", "es", "pt", "ru"]
}
@elseif($schemaType === 'product')
{
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "Content Spark",
    "description": "{{ $metaDescription }}",
    "url": "{{ $currentUrl }}",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web Browser",
    "offers": [
        {
            "@type": "Offer",
            "name": "Free Plan",
            "price": "0",
            "priceCurrency": "USD",
            "priceValidUntil": "{{ date('Y-12-31') }}",
            "availability": "https://schema.org/InStock",
            "description": "Free tier with limited generations"
        },
        {
            "@type": "Offer",
            "name": "Pro Plan",
            "price": "29",
            "priceCurrency": "USD",
            "priceValidUntil": "{{ date('Y-12-31') }}",
            "availability": "https://schema.org/InStock",
            "description": "Professional plan with unlimited generations"
        }
    ],
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "150",
        "bestRating": "5",
        "worstRating": "1"
    },
    "publisher": {!! json_encode($organizationData, JSON_UNESCAPED_SLASHES) !!},
    "featureList": [
        "AI Content Generation",
        "Multi-language Support",
        "Social Media Posts",
        "Marketing Copy",
        "Blog Content",
        "Email Templates"
    ]
}
@elseif($schemaType === 'faqpage')
{
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "name": "{{ $metaTitle }}",
    "description": "{{ $metaDescription }}",
    "url": "{{ $currentUrl }}",
    "mainEntity": [
        {
            "@type": "Question",
            "name": "How does the AI content generation work?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "Content Spark uses advanced AI models to generate high-quality content based on your input keywords and preferences. Simply enter your topic, select the content type, and our AI creates professional content in seconds."
            }
        },
        {
            "@type": "Question",
            "name": "What languages are supported?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "Content Spark supports 6 languages: English, Arabic, French, Spanish, Portuguese, and Russian. You can generate content in any of these languages with native-level quality."
            }
        },
        {
            "@type": "Question",
            "name": "Is there a free plan available?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "Yes, we offer a free plan that includes limited content generations per month. You can upgrade to our Pro plan for unlimited generations and advanced features."
            }
        },
        {
            "@type": "Question",
            "name": "How quickly can I generate content?",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "Content generation typically takes 3-10 seconds depending on the content type and length. Our AI is optimized for speed without compromising quality."
            }
        }
    ],
    "publisher": {!! json_encode($organizationData, JSON_UNESCAPED_SLASHES) !!}
}
@elseif($schemaType === 'contactpage')
{
    "@context": "https://schema.org",
    "@type": "ContactPage",
    "name": "{{ $metaTitle }}",
    "description": "{{ $metaDescription }}",
    "url": "{{ $currentUrl }}",
    "mainEntity": {
        "@type": "Organization",
        "name": "Content Spark",
        "contactPoint": [
            {
                "@type": "ContactPoint",
                "telephone": "******-CONTENT",
                "contactType": "customer service",
                "email": "<EMAIL>",
                "availableLanguage": ["English", "Arabic", "French", "Spanish", "Portuguese", "Russian"],
                "hoursAvailable": {
                    "@type": "OpeningHoursSpecification",
                    "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
                    "opens": "00:00",
                    "closes": "23:59"
                }
            },
            {
                "@type": "ContactPoint",
                "contactType": "sales",
                "email": "<EMAIL>",
                "availableLanguage": ["English"]
            }
        ]
    },
    "publisher": {!! json_encode($organizationData, JSON_UNESCAPED_SLASHES) !!}
}
@elseif($schemaType === 'webpage')
{
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "{{ $metaTitle }}",
    "description": "{{ $metaDescription }}",
    "url": "{{ $currentUrl }}",
    "inLanguage": "{{ app()->getLocale() }}",
    "isPartOf": {
        "@type": "WebSite",
        "name": "Content Spark",
        "url": "{{ $baseUrl }}"
    },
    "publisher": {!! json_encode($organizationData, JSON_UNESCAPED_SLASHES) !!},
    "dateModified": "{{ date('c') }}",
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "{{ $baseUrl }}"
            }
            @if(isset($page) && $page->slug !== 'home')
            ,{
                "@type": "ListItem",
                "position": 2,
                "name": "{{ $translation->title ?? $page->slug }}",
                "item": "{{ $currentUrl }}"
            }
            @endif
        ]
    }
}
@endif
</script>
