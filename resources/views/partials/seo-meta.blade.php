{{--
    SEO Meta Tags Partial Component
    Usage: @include('partials.seo-meta', ['page' => $page, 'translation' => $translation])
--}}

@php
    $currentLocale = app()->getLocale();
    $supportedLocales = ['en', 'ar', 'fr', 'es', 'pt', 'ru'];
    
    // Default values
    $defaultTitle = 'Content Spark - AI-Powered Content Generation Platform';
    $defaultDescription = 'Create professional content in seconds with Content Spark\'s AI-powered platform. Generate social media posts, marketing copy, and more in 6 languages.';
    $defaultKeywords = 'AI content generator, content creation platform, SaaS content tools, marketing automation, AI writing, multi-language content generation';
    $defaultImage = asset('images/content-spark-og-image.jpg');
    
    // Dynamic values from page/translation or defaults
    $metaTitle = isset($translation) ? $translation->getSeoTitle() : (isset($title) ? $title : $defaultTitle);
    $metaDescription = isset($translation) ? $translation->getSeoDescription() : (isset($description) ? $description : $defaultDescription);
    $metaKeywords = isset($translation) ? $translation->seo_keywords : (isset($keywords) ? $keywords : $defaultKeywords);
    $metaImage = isset($ogImage) ? $ogImage : $defaultImage;
    
    // Ensure proper title format
    if (isset($translation) && $translation->title && !str_contains($metaTitle, 'Content Spark')) {
        $metaTitle = $translation->title . ' | Content Spark';
    }
    
    // Validate description length (150-160 chars optimal)
    if (strlen($metaDescription) > 160) {
        $metaDescription = substr($metaDescription, 0, 157) . '...';
    }
    
    // Current URL
    $currentUrl = request()->url();
    $canonicalUrl = isset($canonical) ? $canonical : $currentUrl;
    
    // Page type for Open Graph
    $pageType = isset($ogType) ? $ogType : 'website';
    
    // Robots meta
    $robotsContent = isset($robots) ? $robots : 'index, follow';
@endphp

{{-- Basic Meta Tags --}}
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="csrf-token" content="{{ csrf_token() }}">

{{-- Title --}}
<title>{{ $metaTitle }}</title>

{{-- SEO Meta Tags --}}
<meta name="description" content="{{ $metaDescription }}">
<meta name="keywords" content="{{ $metaKeywords }}">
<meta name="robots" content="{{ $robotsContent }}">
<meta name="author" content="Content Spark">
<meta name="generator" content="Content Spark AI Platform">

{{-- Canonical URL --}}
<link rel="canonical" href="{{ $canonicalUrl }}">

{{-- Language Alternates (hreflang) --}}
@foreach($supportedLocales as $locale)
    @php
        $localeUrl = $locale === 'en' 
            ? url('/') 
            : url('/' . $locale . (isset($page) ? '/' . $page->slug : ''));
    @endphp
    <link rel="alternate" hreflang="{{ $locale }}" href="{{ $localeUrl }}">
@endforeach
<link rel="alternate" hreflang="x-default" href="{{ url('/') }}">

{{-- Open Graph Tags --}}
<meta property="og:type" content="{{ $pageType }}">
<meta property="og:title" content="{{ $metaTitle }}">
<meta property="og:description" content="{{ $metaDescription }}">
<meta property="og:url" content="{{ $canonicalUrl }}">
<meta property="og:image" content="{{ $metaImage }}">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="{{ $metaTitle }}">
<meta property="og:site_name" content="Content Spark">
<meta property="og:locale" content="{{ str_replace('-', '_', app()->getLocale()) }}">

{{-- Twitter Card Tags --}}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ $metaTitle }}">
<meta name="twitter:description" content="{{ $metaDescription }}">
<meta name="twitter:image" content="{{ $metaImage }}">
<meta name="twitter:image:alt" content="{{ $metaTitle }}">
<meta name="twitter:site" content="@ContentSpark">
<meta name="twitter:creator" content="@ContentSpark">

{{-- Additional SEO Tags --}}
<meta name="theme-color" content="#3B82F6">
<meta name="msapplication-TileColor" content="#3B82F6">
<meta name="application-name" content="Content Spark">

{{-- Preconnect for Performance --}}
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

{{-- Favicon --}}
<link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
<link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicon-32x32.png') }}">
<link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicon-16x16.png') }}">
<link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/apple-touch-icon.png') }}">

{{-- Additional Meta for Mobile --}}
<meta name="mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="default">
<meta name="apple-mobile-web-app-title" content="Content Spark">

{{-- Schema.org Structured Data --}}
@if(isset($schemaType))
    @include('partials.structured-data', [
        'schemaType' => $schemaType,
        'page' => $page ?? null,
        'translation' => $translation ?? null,
        'metaTitle' => $metaTitle,
        'metaDescription' => $metaDescription,
        'canonicalUrl' => $canonicalUrl
    ])
@endif
