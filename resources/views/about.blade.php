<x-guest-layout>
    @php
        $pageSlug = 'about-us';
        $page = \App\Models\Page::where('slug', $pageSlug)->first();
        $translation = $page ? $page->translation(app()->getLocale()) : null;
    @endphp

    <x-slot name="head">
        @include('partials.seo-meta', [
            'page' => $page,
            'translation' => $translation,
            'schemaType' => 'organization',
            'ogType' => 'website',
        ])
    </x-slot>

    <!-- Hero Section -->
    <div class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 py-20">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <!-- Geometric Shapes -->
            <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
            <div class="absolute top-32 right-20 w-16 h-16 bg-yellow-300/20 rounded-lg rotate-45 animate-bounce"></div>
            <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-300/20 rounded-full animate-ping"></div>
            <div class="absolute bottom-32 right-1/3 w-24 h-24 bg-green-300/10 rounded-lg rotate-12 animate-pulse"></div>

            <!-- Floating Particles -->
            <div class="absolute top-1/4 left-1/3 w-2 h-2 bg-white/30 rounded-full animate-float"></div>
            <div class="absolute top-1/2 right-1/4 w-3 h-3 bg-blue-200/40 rounded-full animate-float-delayed"></div>
            <div class="absolute bottom-1/3 left-1/2 w-2 h-2 bg-purple-200/30 rounded-full animate-float"></div>

            <!-- Grid Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0"
                    style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 20px 20px;">
                </div>
            </div>

            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
                    {{ __('messages.about') }} Content Spark
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto drop-shadow-md">
                    We're revolutionizing content creation with AI-powered tools that help businesses and creators
                    produce high-quality content in seconds.
                </p>
            </div>
        </div>
    </div>

    <!-- Mission Section -->
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Mission</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    To democratize content creation by making professional-quality content accessible to everyone
                    through the power of artificial intelligence.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="bg-blue-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Speed</h3>
                    <p class="text-gray-600">
                        Generate professional content in seconds, not hours. Our AI understands context and creates
                        relevant, engaging content instantly.
                    </p>
                </div>

                <div class="text-center">
                    <div class="bg-green-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Quality</h3>
                    <p class="text-gray-600">
                        Our AI is trained on high-quality data to ensure every piece of content meets professional
                        standards and engages your audience.
                    </p>
                </div>

                <div class="text-center">
                    <div class="bg-purple-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Global</h3>
                    <p class="text-gray-600">
                        Create content in multiple languages including Arabic, English, French, Spanish, Portuguese, and
                        Russian to reach global audiences.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Story Section -->
    <div class="py-16 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Story</h2>
            </div>

            <div class="prose prose-lg mx-auto text-gray-600">
                <p class="text-xl leading-relaxed mb-6">
                    Content Spark was born from a simple observation: creating high-quality content takes too much time
                    and expertise, limiting many businesses and creators from reaching their full potential.
                </p>

                <p class="text-lg leading-relaxed mb-6">
                    Our team of AI researchers, content creators, and developers came together with a shared vision: to
                    build a platform that could understand context, maintain quality, and generate content that truly
                    resonates with audiences.
                </p>

                <p class="text-lg leading-relaxed mb-6">
                    Today, Content Spark serves thousands of users worldwide, from small business owners to large
                    enterprises, helping them create everything from social media posts to comprehensive marketing
                    campaigns.
                </p>

                <p class="text-lg leading-relaxed">
                    We're just getting started. Our commitment to innovation and user success drives us to continuously
                    improve our AI models and expand our capabilities.
                </p>
            </div>
        </div>
    </div>

    <!-- Values Section -->
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Our Values</h2>
                <p class="text-xl text-gray-600">The principles that guide everything we do</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Innovation</h3>
                    <p class="text-gray-600">Continuously pushing the boundaries of what's possible with AI and content
                        creation.</p>
                </div>

                <div class="text-center">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Accessibility</h3>
                    <p class="text-gray-600">Making professional content creation tools available to everyone,
                        regardless of budget or expertise.</p>
                </div>

                <div class="text-center">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Quality</h3>
                    <p class="text-gray-600">Never compromising on the quality of content our platform generates for our
                        users.</p>
                </div>

                <div class="text-center">
                    <h3 class="text-lg font-bold text-gray-900 mb-2">Support</h3>
                    <p class="text-gray-600">Providing exceptional support to help our users succeed in their content
                        creation journey.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="py-16 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Transform Your Content Creation?
            </h2>
            <p class="text-xl text-blue-700 mb-8">
                Join thousands of creators and businesses who trust Content Spark for their content needs.
            </p>

            @auth
                <a href="{{ route('dashboard') }}"
                    class="bg-white hover:bg-gray-100 text-blue-600 font-bold py-4 px-8 rounded-lg text-lg transition-colors inline-block">
                    Go to Dashboard
                </a>
            @else
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('register') }}"
                        class="bg-white hover:bg-gray-100 text-blue-600 font-bold py-4 px-8 rounded-lg text-lg transition-colors">
                        Start Free Trial
                    </a>
                    <a href="{{ route('pricing') }}"
                        class="bg-transparent hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-lg border-2 border-white transition-colors">
                        View Pricing
                    </a>
                </div>
            @endauth
        </div>
    </div>
</x-guest-layout>
