<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $translation->getSeoTitle() }} - Content Spark</title>
    <meta name="description" content="{{ $translation->getSeoDescription() }}">
    @if ($translation->seo_keywords)
        <meta name="keywords" content="{{ $translation->seo_keywords }}">
    @endif

    <!-- Hreflang tags for SEO -->
    @foreach (['en', 'ar', 'fr'] as $locale)
        @php
            $localeTranslation = $page->translation($locale);
        @endphp
        @if ($localeTranslation)
            <link rel="alternate" hreflang="{{ $locale }}" href="{{ url()->current() }}?lang={{ $locale }}">
        @endif
    @endforeach

    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="antialiased bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}"
                            class="text-2xl font-extrabold text-gradient bg-gradient-to-r from-blue-600 via-purple-500 to-fuchsia-500 bg-clip-text text-transparent tracking-tight hover:opacity-80 transition-opacity">Content
                            Spark</a>
                    </div>
                    <nav class="flex items-center space-x-4">
                        <x-language-switcher />
                        @auth
                            <a href="{{ url('/dashboard') }}"
                                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">{{ __('app.dashboard') }}</a>
                        @else
                            <a href="{{ route('login') }}"
                                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">{{ __('app.login') }}</a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">{{ __('app.get_started') }}</a>
                            @endif
                        @endauth
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1">
            <!-- Hero Section -->
            @if ($translation->featured_image)
                <div class="relative bg-gray-900">
                    <div class="absolute inset-0">
                        <img class="w-full h-full object-cover"
                            src="{{ asset('storage/' . $translation->featured_image) }}"
                            alt="{{ $translation->title }}">
                        <div class="absolute inset-0 bg-gray-900 opacity-50"></div>
                    </div>
                    <div class="relative max-w-7xl mx-auto py-24 px-4 sm:py-32 sm:px-6 lg:px-8">
                        <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
                            {{ $translation->title }}
                        </h1>
                        @if ($translation->description)
                            <p class="mt-6 text-xl text-gray-300 max-w-3xl">
                                {{ $translation->description }}
                            </p>
                        @endif
                    </div>
                </div>
            @else
                <div class="bg-white">
                    <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
                        <div class="text-center">
                            <h1 class="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl lg:text-6xl">
                                {{ $translation->title }}
                            </h1>
                            @if ($translation->description)
                                <p class="mt-6 text-xl text-gray-600 max-w-3xl mx-auto">
                                    {{ $translation->description }}
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Content Section -->
            @if ($translation->content)
                <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                    <div class="prose prose-lg max-w-none">
                        {!! nl2br(e($translation->content)) !!}
                    </div>
                </div>
            @endif

            <!-- Call to Action -->
            <div class="bg-blue-50">
                <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
                    <div class="text-center">
                        <h2 class="text-3xl font-extrabold text-gray-900">
                            {{ __('app.hero_cta') }}
                        </h2>
                        <p class="mt-4 text-lg text-gray-600">
                            {{ __('app.hero_subtitle') }}
                        </p>
                        <div class="mt-8 flex justify-center space-x-4">
                            @auth
                                <a href="{{ route('generations.dashboard') }}"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium">
                                    {{ __('app.generate_content') }}
                                </a>
                            @else
                                <a href="{{ route('register') }}"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-md text-lg font-medium">
                                    {{ __('app.get_started') }}
                                </a>
                                <a href="{{ route('pricing') }}"
                                    class="border border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-md text-lg font-medium">
                                    {{ __('app.hero_cta_secondary') }}
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="text-center text-gray-500">
                    <p>&copy; {{ date('Y') }} Content Spark. {{ __('app.all_rights_reserved') }}</p>
                </div>
            </div>
        </footer>
    </div>
</body>

</html>
