<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $translation->getSeoTitle() }} - Content Spark</title>
    <meta name="description" content="{{ $translation->getSeoDescription() }}">
    @if ($translation->seo_keywords)
        <meta name="keywords" content="{{ $translation->seo_keywords }}">
    @endif

    <!-- Hreflang tags for SEO -->
    @foreach (['en', 'ar', 'fr'] as $locale)
        @php
            $localeTranslation = $page->translation($locale);
        @endphp
        @if ($localeTranslation)
            <link rel="alternate" hreflang="{{ $locale }}"
                href="{{ route('content-types.show', ['contentType' => $contentType, 'lang' => $locale]) }}">
        @endif
    @endforeach

    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>

<body class="antialiased bg-gray-50">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="bg-white shadow-sm">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <div class="flex items-center">
                        <a href="{{ route('home') }}" class="text-2xl font-bold text-gray-900">Content Spark</a>
                    </div>
                    <nav class="flex items-center space-x-4">
                        <x-language-switcher />
                        @auth
                            <a href="{{ url('/dashboard') }}"
                                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">{{ __('app.dashboard') }}</a>
                        @else
                            <a href="{{ route('login') }}"
                                class="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium">{{ __('app.login') }}</a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}"
                                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">{{ __('app.get_started') }}</a>
                            @endif
                        @endauth
                    </nav>
                </div>
            </div>
        </header>

        <!-- Breadcrumb -->
        <nav class="bg-gray-100 border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center space-x-4 py-3 text-sm">
                    <a href="{{ route('home') }}" class="text-gray-500 hover:text-gray-700">{{ __('app.home') }}</a>
                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <span class="text-gray-900">{{ $translation->title }}</span>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="flex-1">
            <!-- Hero Section -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600">
                <div class="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
                    <div class="text-center">
                        <h1 class="text-4xl font-extrabold tracking-tight text-white sm:text-5xl lg:text-6xl">
                            {{ $translation->title }}
                        </h1>
                        @if ($translation->description)
                            <p class="mt-6 text-xl text-blue-700 max-w-3xl mx-auto">
                                {{ $translation->description }}
                            </p>
                        @endif
                        <div class="mt-8">
                            @auth
                                <a href="{{ route('generations.dashboard') }}"
                                    class="bg-white text-blue-600 hover:bg-gray-50 px-8 py-3 rounded-md text-lg font-medium inline-flex items-center">
                                    {{ __('app.generate_content') }}
                                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </a>
                            @else
                                <a href="{{ route('register') }}"
                                    class="bg-white text-blue-600 hover:bg-gray-50 px-8 py-3 rounded-md text-lg font-medium inline-flex items-center">
                                    {{ __('app.get_started') }}
                                    <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Section -->
            @if ($translation->content)
                <div class="max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                    <div class="prose prose-lg max-w-none">
                        {!! nl2br(e($translation->content)) !!}
                    </div>
                </div>
            @endif

            <!-- Examples Section -->
            <div class="bg-gray-50">
                <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-extrabold text-gray-900">
                            {{ __('Example Generated Content') }}
                        </h2>
                        <p class="mt-4 text-lg text-gray-600">
                            {{ __('See what our AI can create for you') }}
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- Example content cards would go here -->
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('Example 1') }}</h3>
                            <p class="text-gray-600">{{ __('Sample generated content will be displayed here...') }}</p>
                        </div>
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('Example 2') }}</h3>
                            <p class="text-gray-600">{{ __('Sample generated content will be displayed here...') }}</p>
                        </div>
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">{{ __('Example 3') }}</h3>
                            <p class="text-gray-600">{{ __('Sample generated content will be displayed here...') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Content Types -->
            @if ($relatedContentTypes->count() > 0)
                <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-extrabold text-gray-900">
                            {{ __('Related Content Types') }}
                        </h2>
                        <p class="mt-4 text-lg text-gray-600">
                            {{ __('Explore other content generation tools') }}
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        @foreach ($relatedContentTypes as $slug => $relatedType)
                            <a href="{{ route('content-types.show', $slug) }}"
                                class="block bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    {{ __(str_replace('-', '_', $slug)) }}
                                </h3>
                                <p class="text-gray-600">
                                    {{ __('Generate high-quality ' . str_replace('-', ' ', $slug)) }}
                                </p>
                            </a>
                        @endforeach
                    </div>
                </div>
            @endif

            <!-- Final CTA -->
            <div class="bg-blue-600">
                <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
                    <div class="text-center">
                        <h2 class="text-3xl font-extrabold text-white">
                            {{ __('Ready to Get Started?') }}
                        </h2>
                        <p class="mt-4 text-lg text-blue-700">
                            {{ __('Join thousands of content creators using AI to boost their productivity') }}
                        </p>
                        <div class="mt-8">
                            @auth
                                <a href="{{ route('generations.dashboard') }}"
                                    class="bg-white text-blue-600 hover:bg-gray-50 px-8 py-3 rounded-md text-lg font-medium">
                                    {{ __('app.generate_content') }}
                                </a>
                            @else
                                <a href="{{ route('register') }}"
                                    class="bg-white text-blue-600 hover:bg-gray-50 px-8 py-3 rounded-md text-lg font-medium">
                                    {{ __('app.get_started') }}
                                </a>
                            @endauth
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="bg-white border-t">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div class="text-center text-gray-500">
                    <p>&copy; {{ date('Y') }} Content Spark. {{ __('app.all_rights_reserved') }}</p>
                </div>
            </div>
        </footer>
    </div>
</body>

</html>
