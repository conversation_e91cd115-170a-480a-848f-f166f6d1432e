<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    @if (isset($head))
        {{ $head }}
    @else
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>{{ $title ?? config('app.name', 'Content Spark') }}</title>
        <meta name="description" content="{{ $description ?? 'AI-powered content generation platform' }}">
    @endif

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />
    @if (app()->getLocale() === 'ar')
        <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    @endif

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/css/rtl.css', 'resources/js/app.js'])
</head>

<body class="font-sans antialiased bg-white">
    <!-- Navigation -->
    <nav x-data="{ open: false }" class="glass-effect sticky top-0 z-50 border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{{ \App\Helpers\LocalizedUrlHelper::route('home') }}"
                        class="text-3xl font-display font-bold gradient-text tracking-tight hover:scale-105 transition-transform duration-300">
                        Content Spark
                    </a>

                    <!-- Navigation Links -->
                    <div class="hidden md:flex md:ml-12 md:space-x-1">
                        <a href="{{ \App\Helpers\LocalizedUrlHelper::route('home') }}#features"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300">
                            {{ __('messages.features') }}
                        </a>
                        <a href="{{ \App\Helpers\LocalizedUrlHelper::route('pricing') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('pricing') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.pricing') }}
                        </a>
                        <a href="{{ \App\Helpers\LocalizedUrlHelper::route('faq') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('faq') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.faq') }}
                        </a>
                        <a href="{{ \App\Helpers\LocalizedUrlHelper::route('about') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('about') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.about') }}
                        </a>
                        <a href="{{ \App\Helpers\LocalizedUrlHelper::route('contact') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('contact') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.contact') }}
                        </a>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Language Switcher -->
                    <div class="hidden md:block">
                        <x-language-switcher />
                    </div>

                    <!-- Auth Links -->
                    <div class="hidden md:flex md:space-x-4">
                        @guest
                            <a href="{{ route('login') }}"
                                class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300">
                                {{ __('messages.login') }}
                            </a>
                            <a href="{{ route('register') }}"
                                class="modern-btn bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-105">
                                {{ __('marketing.hero_cta_primary') }}
                            </a>
                        @else
                            <a href="{{ route('dashboard') }}"
                                class="modern-btn bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-105">
                                {{ __('messages.dashboard') }}
                            </a>
                        @endguest
                    </div>

                    <!-- Mobile menu button -->
                    <div class="md:hidden">
                        <button @click="open = !open" type="button"
                            class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                            aria-controls="mobile-menu" aria-expanded="false">
                            <span class="sr-only">Open main menu</span>
                            <svg class="h-6 w-6" :class="{ 'hidden': open, 'block': !open }"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                            <svg class="h-6 w-6" :class="{ 'block': open, 'hidden': !open }"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div x-show="open" x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="transition ease-in duration-100" x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-95" class="md:hidden" id="mobile-menu">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
                <a href="{{ \App\Helpers\LocalizedUrlHelper::route('home') }}#features"
                    class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium">{{ __('messages.features') }}</a>
                <a href="{{ \App\Helpers\LocalizedUrlHelper::route('pricing') }}"
                    class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('pricing') ? 'bg-blue-50 text-blue-700' : '' }}">{{ __('messages.pricing') }}</a>
                <a href="{{ \App\Helpers\LocalizedUrlHelper::route('faq') }}"
                    class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('faq') ? 'bg-blue-50 text-blue-700' : '' }}">{{ __('messages.faq') }}</a>
                <a href="{{ \App\Helpers\LocalizedUrlHelper::route('about') }}"
                    class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('about') ? 'bg-blue-50 text-blue-700' : '' }}">{{ __('messages.about') }}</a>
                <a href="{{ \App\Helpers\LocalizedUrlHelper::route('contact') }}"
                    class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium {{ \App\Helpers\LocalizedUrlHelper::isRouteActive('contact') ? 'bg-blue-50 text-blue-700' : '' }}">{{ __('messages.contact') }}</a>

                <div class="border-t border-gray-200 pt-4">
                    <div class="px-3 py-2">
                        <x-language-switcher />
                    </div>
                    @guest
                        <a href="{{ route('login') }}"
                            class="text-gray-700 hover:text-gray-900 block px-3 py-2 rounded-md text-base font-medium">{{ __('messages.login') }}</a>
                        <a href="{{ route('register') }}"
                            class="bg-blue-600 hover:bg-blue-700 text-white block px-3 py-2 rounded-md text-base font-medium mt-2">{{ __('marketing.hero_cta_primary') }}</a>
                    @else
                        <a href="{{ route('dashboard') }}"
                            class="bg-blue-600 hover:bg-blue-700 text-white block px-3 py-2 rounded-md text-base font-medium">{{ __('messages.dashboard') }}</a>
                    @endguest
                </div>
            </div>
        </div>
    </nav>

    <!-- Page Content -->
    <main>
        {{ $slot }}
    </main>

    <!-- Footer -->
    @include('marketing.partials.footer')
</body>

</html>
