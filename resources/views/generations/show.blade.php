<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Generated Content') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <!-- Generation Info -->
                    <div class="mb-6 border-b border-gray-200 pb-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Content Type</h3>
                                <p class="mt-1 text-sm text-gray-900">{{ $generation->contentType->name }}</p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Language</h3>
                                <p class="mt-1 text-sm text-gray-900">
                                    @switch($generation->language)
                                        @case('ar')
                                            العربية
                                        @break

                                        @case('en')
                                            English
                                        @break

                                        @case('fr')
                                            Français
                                        @break

                                        @default
                                            {{ $generation->language }}
                                    @endswitch
                                </p>
                            </div>
                            <div>
                                <h3 class="text-sm font-medium text-gray-500">Keywords</h3>
                                <p class="mt-1 text-sm text-gray-900">{{ $generation->keywords }}</p>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h3 class="text-sm font-medium text-gray-500">Generated</h3>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ $generation->created_at->format('M d, Y \a\t g:i A') }}</p>
                        </div>
                    </div>

                    <!-- Generated Content -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Generated Content</h3>
                            <button onclick="copyToClipboard()"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z">
                                    </path>
                                </svg>
                                Copy
                            </button>
                        </div>
                        <div id="generated-content" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                            <pre class="whitespace-pre-wrap text-sm text-gray-900 font-mono">{{ $generation->result }}</pre>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-between">
                        <a href="{{ route('generations.index') }}" class="text-gray-600 hover:text-gray-800">
                            ← Back to History
                        </a>
                        <div class="flex space-x-3">
                            <a href="{{ route('generations.dashboard') }}"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                Generate New Content
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard() {
            const content = document.getElementById('generated-content').textContent;
            navigator.clipboard.writeText(content).then(function() {
                // Show success message
                const button = event.target.closest('button');
                const originalText = button.innerHTML;
                button.innerHTML =
                    '<svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
                setTimeout(() => {
                    button.innerHTML = originalText;
                }, 2000);
            });
        }
    </script>
</x-app-layout>
