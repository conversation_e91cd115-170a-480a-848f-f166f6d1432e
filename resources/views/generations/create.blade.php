<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Generate Content') }}
            </h2>
            <div class="text-sm text-gray-600">
                @if ($remainingGenerations === -1)
                    <span class="text-green-600 font-medium">Unlimited generations</span>
                @else
                    <span class="font-medium">{{ $remainingGenerations }} generations remaining</span>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Generation Limit Warning -->
            @if ($remainingGenerations !== -1 && $remainingGenerations <= 5)
                <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                    clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Low Generation Count
                            </h3>
                            <div class="mt-1 text-sm text-yellow-700">
                                <p>You have {{ $remainingGenerations }}
                                    generation{{ $remainingGenerations !== 1 ? 's' : '' }} remaining.
                                    <a href="{{ route('pricing.index') }}" class="font-medium underline">Upgrade your
                                        plan</a> for more generations.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('generations.store') }}">
                        @csrf

                        <!-- Content Type Selection -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-4">
                                Select Content Type <span class="text-red-500">*</span>
                            </label>

                            @foreach ($contentTypes as $category => $types)
                                <div class="mb-6">
                                    <h3 class="text-lg font-medium text-gray-900 mb-3">{{ $category ?: 'General' }}</h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        @foreach ($types as $contentType)
                                            <label class="relative cursor-pointer">
                                                <input type="radio" name="content_type_id"
                                                    value="{{ $contentType->id }}"
                                                    {{ old('content_type_id') == $contentType->id ? 'checked' : '' }}
                                                    class="sr-only peer" required>
                                                <div
                                                    class="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                                    <div class="flex items-start">
                                                        <div class="flex-1">
                                                            <h4 class="font-medium text-gray-900">
                                                                {{ $contentType->name }}</h4>
                                                            @if ($contentType->description)
                                                                <p class="text-sm text-gray-600 mt-1">
                                                                    {{ $contentType->description }}</p>
                                                            @endif
                                                        </div>
                                                        <div class="ml-2 flex-shrink-0">
                                                            <div
                                                                class="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-blue-500 peer-checked:bg-blue-500 transition-all">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </label>
                                        @endforeach
                                    </div>
                                </div>
                            @endforeach

                            <x-input-error :messages="$errors->get('content_type_id')" class="mt-2" />
                        </div>

                        <!-- Language Selection -->
                        <div class="mb-6">
                            <label for="language" class="block text-sm font-medium text-gray-700 mb-3">
                                Language <span class="text-red-500">*</span>
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                @foreach ($languages as $code => $data)
                                    <label class="relative cursor-pointer">
                                        <input type="radio" name="language" value="{{ $code }}"
                                            {{ old('language') == $code ? 'checked' : '' }} class="sr-only peer"
                                            required>
                                        <div
                                            class="p-3 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                            <div class="flex items-center">
                                                <span class="text-2xl mr-3">{{ $data['flag'] }}</span>
                                                <span class="font-medium text-gray-900">{{ $data['name'] }}</span>
                                            </div>
                                        </div>
                                    </label>
                                @endforeach
                            </div>
                            <x-input-error :messages="$errors->get('language')" class="mt-2" />
                        </div>

                        <!-- Keywords Input -->
                        <div class="mb-8">
                            <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                Keywords / Topic <span class="text-red-500">*</span>
                            </label>
                            <textarea id="keywords" name="keywords" rows="4" required maxlength="500"
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                placeholder="Enter keywords, topic, or description for your content. Be specific for better results...">{{ old('keywords') }}</textarea>
                            <div class="flex justify-between mt-1">
                                <div>
                                    <x-input-error :messages="$errors->get('keywords')" class="text-sm" />
                                </div>
                                <div class="text-sm text-gray-500">
                                    <span id="char-count">0</span>/500 characters
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex items-center justify-between">
                            <a href="{{ route('dashboard') }}" class="text-gray-600 hover:text-gray-800">
                                ← Back to Dashboard
                            </a>
                            <button type="submit" id="generate-btn"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg focus:outline-none focus:shadow-outline transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                                <span id="btn-text">Generate Content</span>
                                <span id="btn-loading" class="hidden">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline"
                                        xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10"
                                            stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                        </path>
                                    </svg>
                                    Generating...
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Tips Section -->
            <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-4">💡 Tips for Better Results</h3>
                <ul class="space-y-2 text-sm text-blue-800">
                    <li>• Be specific with your keywords and topic description</li>
                    <li>• Include context about your target audience or purpose</li>
                    <li>• Mention any specific requirements (tone, length, style)</li>
                    <li>• Use relevant industry terms or technical details when needed</li>
                    <li>• For social media content, specify the platform if relevant</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const keywordsTextarea = document.getElementById('keywords');
            const charCount = document.getElementById('char-count');
            const generateBtn = document.getElementById('generate-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');

            // Character counter
            keywordsTextarea.addEventListener('input', function() {
                const count = this.value.length;
                charCount.textContent = count;

                if (count > 450) {
                    charCount.classList.add('text-red-500');
                } else {
                    charCount.classList.remove('text-red-500');
                }
            });

            // Form submission with loading state
            form.addEventListener('submit', function() {
                generateBtn.disabled = true;
                btnText.classList.add('hidden');
                btnLoading.classList.remove('hidden');
            });

            // Initialize character count
            keywordsTextarea.dispatchEvent(new Event('input'));
        });
    </script>
</x-app-layout>
