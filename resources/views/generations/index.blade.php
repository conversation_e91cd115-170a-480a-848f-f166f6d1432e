<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Generation History') }}
            </h2>
            <a href="{{ route('generations.dashboard') }}"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Generate New Content
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if ($generations->count() > 0)
                        <div class="space-y-6">
                            @foreach ($generations as $generation)
                                <div
                                    class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow duration-200">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-4 mb-3">
                                                <h3 class="text-lg font-medium text-gray-900">
                                                    {{ $generation->contentType->name }}</h3>
                                                <span
                                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    @switch($generation->language)
                                                        @case('ar')
                                                            العربية
                                                        @break

                                                        @case('en')
                                                            English
                                                        @break

                                                        @case('fr')
                                                            Français
                                                        @break

                                                        @default
                                                            {{ $generation->language }}
                                                    @endswitch
                                                </span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-2">
                                                <strong>Keywords:</strong> {{ $generation->keywords }}
                                            </p>
                                            <p class="text-sm text-gray-500 mb-4">
                                                Generated {{ $generation->created_at->diffForHumans() }}
                                            </p>
                                            <div class="bg-gray-50 rounded-lg p-3">
                                                <p class="text-sm text-gray-700 line-clamp-3">
                                                    {{ Str::limit($generation->result, 200) }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="ml-6 flex-shrink-0">
                                            <a href="{{ route('generations.show', $generation) }}"
                                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                                View Full
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <!-- Pagination -->
                        <div class="mt-8">
                            {{ $generations->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No generations yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating your first content generation.
                            </p>
                            <div class="mt-6">
                                <a href="{{ route('generations.dashboard') }}"
                                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    Generate Content
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
