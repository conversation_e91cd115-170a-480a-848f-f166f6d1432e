<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Generation History') }}
            </h2>
            <div class="flex space-x-3">
                <a href="{{ route('generations.dashboard') }}"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Generate New Content
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filters -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('generations.index') }}" class="flex flex-wrap gap-4">
                        <div class="flex-1 min-w-64">
                            <input type="text" name="search" value="{{ request('search') }}"
                                placeholder="Search by keywords, content, or type..."
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <div>
                            <select name="content_type"
                                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Content Types</option>
                                @foreach ($contentTypes as $contentType)
                                    <option value="{{ $contentType->id }}"
                                        {{ request('content_type') == $contentType->id ? 'selected' : '' }}>
                                        {{ $contentType->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <select name="language"
                                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">All Languages</option>
                                @foreach ($languages as $code => $name)
                                    <option value="{{ $code }}"
                                        {{ request('language') === $code ? 'selected' : '' }}>
                                        {{ $name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div>
                            <input type="date" name="date_from" value="{{ request('date_from') }}"
                                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <div>
                            <input type="date" name="date_to" value="{{ request('date_to') }}"
                                class="rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                        </div>

                        <button type="submit"
                            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                            Filter
                        </button>

                        <a href="{{ route('generations.index') }}"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Clear
                        </a>
                    </form>
                </div>
            </div>

            <!-- Bulk Actions -->
            @if ($generations->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input type="checkbox" id="select-all"
                                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                    <span class="ml-2 text-sm text-gray-600">Select All</span>
                                </label>
                                <span id="selected-count" class="text-sm text-gray-500">0 selected</span>
                            </div>
                            <div class="flex space-x-2">
                                <button id="bulk-delete-btn"
                                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                                    disabled>
                                    Delete Selected
                                </button>
                                <div class="relative">
                                    <button id="export-btn"
                                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                                        disabled>
                                        Export Selected
                                    </button>
                                    <div id="export-menu"
                                        class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                                        <a href="#"
                                            class="export-option block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            data-format="txt">Export as TXT</a>
                                        <a href="#"
                                            class="export-option block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            data-format="csv">Export as CSV</a>
                                        <a href="#"
                                            class="export-option block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                            data-format="json">Export as JSON</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Generations List -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if ($generations->count() > 0)
                        <div class="space-y-6">
                            @foreach ($generations as $generation)
                                <div class="border border-gray-200 rounded-lg p-6 generation-item">
                                    <div class="flex justify-between items-start mb-4">
                                        <div class="flex items-start space-x-3">
                                            <input type="checkbox"
                                                class="generation-checkbox mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                                value="{{ $generation->id }}">
                                            <div>
                                                <h3 class="text-lg font-semibold text-gray-900">
                                                    {{ $generation->contentType->name }}
                                                </h3>
                                                <p class="text-sm text-gray-500">
                                                    {{ $languages[$generation->language] ?? $generation->language }} •
                                                    {{ $generation->created_at->format('M j, Y g:i A') }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <a href="{{ route('generations.show', $generation) }}"
                                                class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                View
                                            </a>
                                            <form method="POST"
                                                action="{{ route('generations.regenerate', $generation) }}"
                                                class="inline">
                                                @csrf
                                                <button type="submit"
                                                    class="text-green-600 hover:text-green-800 text-sm font-medium">
                                                    Regenerate
                                                </button>
                                            </form>
                                            <form method="POST"
                                                action="{{ route('generations.destroy', $generation) }}" class="inline"
                                                onsubmit="return confirm('Are you sure you want to delete this generation?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit"
                                                    class="text-red-600 hover:text-red-800 text-sm font-medium">
                                                    Delete
                                                </button>
                                            </form>
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <p class="text-sm text-gray-600 mb-2">Keywords:</p>
                                        <p class="text-gray-900">{{ $generation->keywords }}</p>
                                    </div>

                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <p class="text-sm text-gray-600 mb-2">Generated Content:</p>
                                        <p class="text-gray-900">{{ Str::limit($generation->result, 300) }}</p>
                                        @if (strlen($generation->result) > 300)
                                            <button class="text-blue-600 hover:text-blue-800 text-sm font-medium mt-2"
                                                onclick="toggleFullContent(this, {{ $generation->id }})">
                                                Show More
                                            </button>
                                            <div id="full-content-{{ $generation->id }}" class="hidden mt-2">
                                                <p class="text-gray-900">{{ $generation->result }}</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        <div class="mt-6">
                            {{ $generations->appends(request()->query())->links() }}
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No generations found</h3>
                            <p class="mt-1 text-sm text-gray-500">
                                @if (request()->hasAny(['search', 'content_type', 'language', 'date_from', 'date_to']))
                                    Try adjusting your search criteria or <a href="{{ route('generations.index') }}"
                                        class="text-blue-600 hover:text-blue-800">clear filters</a>.
                                @else
                                    Get started by creating your first content generation.
                                @endif
                            </p>
                            <div class="mt-6">
                                <a href="{{ route('generations.dashboard') }}"
                                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    Generate Content
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Delete Form -->
    <form id="bulk-delete-form" method="POST" action="{{ route('generations.bulk-destroy') }}" class="hidden">
        @csrf
        <input type="hidden" name="generation_ids" id="bulk-delete-ids">
    </form>

    <!-- Export Form -->
    <form id="export-form" method="GET" action="{{ route('generations.export') }}" class="hidden">
        <input type="hidden" name="generation_ids" id="export-ids">
        <input type="hidden" name="format" id="export-format">
    </form>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('select-all');
            const generationCheckboxes = document.querySelectorAll('.generation-checkbox');
            const selectedCount = document.getElementById('selected-count');
            const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
            const exportBtn = document.getElementById('export-btn');
            const exportMenu = document.getElementById('export-menu');

            // Select all functionality
            selectAllCheckbox.addEventListener('change', function() {
                generationCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBulkActions();
            });

            // Individual checkbox functionality
            generationCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBulkActions);
            });

            function updateBulkActions() {
                const checkedBoxes = document.querySelectorAll('.generation-checkbox:checked');
                const count = checkedBoxes.length;

                selectedCount.textContent = `${count} selected`;
                bulkDeleteBtn.disabled = count === 0;
                exportBtn.disabled = count === 0;

                selectAllCheckbox.indeterminate = count > 0 && count < generationCheckboxes.length;
                selectAllCheckbox.checked = count === generationCheckboxes.length;
            }

            // Bulk delete
            bulkDeleteBtn.addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.generation-checkbox:checked');
                if (checkedBoxes.length === 0) return;

                if (confirm(`Are you sure you want to delete ${checkedBoxes.length} generation(s)?`)) {
                    const ids = Array.from(checkedBoxes).map(cb => cb.value);
                    document.getElementById('bulk-delete-ids').value = JSON.stringify(ids);
                    document.getElementById('bulk-delete-form').submit();
                }
            });

            // Export functionality
            exportBtn.addEventListener('click', function() {
                exportMenu.classList.toggle('hidden');
            });

            document.querySelectorAll('.export-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    const format = this.dataset.format;
                    const checkedBoxes = document.querySelectorAll('.generation-checkbox:checked');

                    if (checkedBoxes.length === 0) return;

                    const ids = Array.from(checkedBoxes).map(cb => cb.value);
                    document.getElementById('export-ids').value = JSON.stringify(ids);
                    document.getElementById('export-format').value = format;
                    document.getElementById('export-form').submit();

                    exportMenu.classList.add('hidden');
                });
            });

            // Close export menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!exportBtn.contains(e.target) && !exportMenu.contains(e.target)) {
                    exportMenu.classList.add('hidden');
                }
            });
        });

        function toggleFullContent(button, generationId) {
            const fullContent = document.getElementById(`full-content-${generationId}`);
            if (fullContent.classList.contains('hidden')) {
                fullContent.classList.remove('hidden');
                button.textContent = 'Show Less';
            } else {
                fullContent.classList.add('hidden');
                button.textContent = 'Show More';
            }
        }
    </script>
</x-app-layout>
