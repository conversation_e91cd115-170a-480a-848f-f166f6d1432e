<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Content Generation') }}
            </h2>
            <div class="text-sm text-gray-600">
                @if($remainingGenerations === -1)
                    <span class="text-green-600 font-medium">Unlimited generations</span>
                @else
                    <span class="font-medium">{{ $remainingGenerations }} generations remaining</span>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Generation Limit Warning -->
            @if($remainingGenerations !== -1 && $remainingGenerations <= 5)
                <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Low Generation Count
                            </h3>
                            <div class="mt-1 text-sm text-yellow-700">
                                <p>You have {{ $remainingGenerations }} generation{{ $remainingGenerations !== 1 ? 's' : '' }} remaining. 
                                   <a href="{{ route('pricing.index') }}" class="font-medium underline">Upgrade your plan</a> for more generations.</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Category Cards -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Choose Your Content Category</h3>
                        <p class="text-gray-600">Select a category to start generating professional content with AI</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        @foreach($categories as $categoryName => $categoryData)
                            <a href="{{ route($categoryData['route']) }}" 
                               class="group block p-6 bg-white border-2 border-gray-200 rounded-xl hover:border-blue-500 hover:shadow-lg transition-all duration-200">
                                <div class="text-center">
                                    <div class="text-4xl mb-4">{{ $categoryData['icon'] }}</div>
                                    <h4 class="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600">
                                        {{ $categoryName }}
                                    </h4>
                                    <p class="text-gray-600 mb-4">{{ $categoryData['description'] }}</p>
                                    
                                    <div class="text-sm text-gray-500 mb-4">
                                        <strong>Available Tools:</strong>
                                        <div class="mt-1">
                                            @foreach($categoryData['tools'] as $tool)
                                                <span class="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs mr-1 mb-1">
                                                    {{ $tool }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </div>
                                    
                                    <div class="inline-flex items-center text-blue-600 font-medium group-hover:text-blue-700">
                                        Start Creating
                                        <svg class="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </div>
                                </div>
                            </a>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl mb-3">📊</div>
                        <h4 class="font-semibold text-gray-900 mb-2">View History</h4>
                        <p class="text-gray-600 text-sm mb-4">Browse your previous generations</p>
                        <a href="{{ route('generations.index') }}" 
                           class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors">
                            View History
                        </a>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl mb-3">⚙️</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Settings</h4>
                        <p class="text-gray-600 text-sm mb-4">Manage your account preferences</p>
                        <a href="{{ route('settings.index') }}" 
                           class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors">
                            Settings
                        </a>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl mb-3">💳</div>
                        <h4 class="font-semibold text-gray-900 mb-2">Billing</h4>
                        <p class="text-gray-600 text-sm mb-4">Manage your subscription</p>
                        <a href="{{ route('billing.index') }}" 
                           class="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition-colors">
                            Billing
                        </a>
                    </div>
                </div>
            </div>

            <!-- Usage Statistics -->
            @if(auth()->user()->generations()->count() > 0)
                <div class="mt-8 bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Your Usage This Month</h3>
                        
                        @php
                            $thisMonthGenerations = auth()->user()->generations()
                                ->whereMonth('created_at', now()->month)
                                ->whereYear('created_at', now()->year)
                                ->count();
                            $totalGenerations = auth()->user()->generations()->count();
                        @endphp
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600">{{ $thisMonthGenerations }}</div>
                                <div class="text-sm text-gray-600">This Month</div>
                            </div>
                            
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600">{{ $totalGenerations }}</div>
                                <div class="text-sm text-gray-600">Total Generated</div>
                            </div>
                            
                            <div class="text-center">
                                @if($remainingGenerations === -1)
                                    <div class="text-3xl font-bold text-purple-600">∞</div>
                                @else
                                    <div class="text-3xl font-bold text-purple-600">{{ $remainingGenerations }}</div>
                                @endif
                                <div class="text-sm text-gray-600">Remaining</div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
