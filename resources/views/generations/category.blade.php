<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div class="flex items-center">
                <a href="{{ route('generations.dashboard') }}" class="text-gray-500 hover:text-gray-700 mr-4">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                </a>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ $category }} Tools
                </h2>
            </div>
            <div class="text-sm text-gray-600">
                @if($remainingGenerations === -1)
                    <span class="text-green-600 font-medium">Unlimited generations</span>
                @else
                    <span class="font-medium">{{ $remainingGenerations }} generations remaining</span>
                @endif
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <!-- Generation Limit Warning -->
            @if($remainingGenerations !== -1 && $remainingGenerations <= 5)
                <div class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Low Generation Count
                            </h3>
                            <div class="mt-1 text-sm text-yellow-700">
                                <p>You have {{ $remainingGenerations }} generation{{ $remainingGenerations !== 1 ? 's' : '' }} remaining. 
                                   <a href="{{ route('pricing.index') }}" class="font-medium underline">Upgrade your plan</a> for more generations.</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-8">
                    <form method="POST" action="{{ route('generations.store') }}" id="generation-form">
                        @csrf

                        <!-- Content Type Selection -->
                        <div class="mb-8">
                            <label class="block text-sm font-medium text-gray-700 mb-4">
                                Select {{ $category }} Tool <span class="text-red-500">*</span>
                            </label>
                            
                            @if($contentTypes->count() > 0)
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    @foreach ($contentTypes as $contentType)
                                        <label class="relative cursor-pointer">
                                            <input type="radio" name="content_type_id" value="{{ $contentType->id }}" 
                                                   {{ old('content_type_id') == $contentType->id ? 'checked' : '' }}
                                                   class="sr-only peer" required>
                                            <div class="p-6 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                                <div class="flex items-start">
                                                    <div class="flex-1">
                                                        <h4 class="font-medium text-gray-900 text-lg">{{ $contentType->name }}</h4>
                                                        @if($contentType->description)
                                                            <p class="text-sm text-gray-600 mt-2">{{ $contentType->description }}</p>
                                                        @endif
                                                    </div>
                                                    <div class="ml-3 flex-shrink-0">
                                                        <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-blue-500 peer-checked:bg-blue-500 transition-all"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </label>
                                    @endforeach
                                </div>
                            @else
                                <div class="text-center py-8">
                                    <p class="text-gray-500">No tools available in this category yet.</p>
                                    <a href="{{ route('generations.dashboard') }}" class="mt-4 inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-700 text-white rounded-lg">
                                        ← Back to Categories
                                    </a>
                                </div>
                            @endif
                            
                            @error('content_type_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        @if($contentTypes->count() > 0)
                            <!-- Language Selection -->
                            <div class="mb-6">
                                <label for="language" class="block text-sm font-medium text-gray-700 mb-3">
                                    Language <span class="text-red-500">*</span>
                                </label>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    @foreach ($languages as $code => $data)
                                        <label class="relative cursor-pointer">
                                            <input type="radio" name="language" value="{{ $code }}" 
                                                   {{ old('language') == $code ? 'checked' : '' }}
                                                   class="sr-only peer" required>
                                            <div class="p-3 border-2 border-gray-200 rounded-lg hover:border-blue-300 peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-all">
                                                <div class="flex items-center">
                                                    <span class="text-2xl mr-3">{{ $data['flag'] }}</span>
                                                    <span class="font-medium text-gray-900">{{ $data['name'] }}</span>
                                                </div>
                                            </div>
                                        </label>
                                    @endforeach
                                </div>
                                @error('language')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Keywords Input -->
                            <div class="mb-8">
                                <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                    Keywords / Topic <span class="text-red-500">*</span>
                                </label>
                                <textarea id="keywords" name="keywords" rows="4" required maxlength="500"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                    placeholder="Enter keywords, topic, or description for your content. Be specific for better results...">{{ old('keywords') }}</textarea>
                                <div class="flex justify-between mt-1">
                                    <div>
                                        @error('keywords')
                                            <p class="text-sm text-red-600">{{ $message }}</p>
                                        @enderror
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <span id="char-count">0</span>/500 characters
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex items-center justify-between">
                                <a href="{{ route('generations.dashboard') }}" class="text-gray-600 hover:text-gray-800">
                                    ← Back to Categories
                                </a>
                                <button type="submit" id="generate-btn"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-8 rounded-lg focus:outline-none focus:shadow-outline transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                                    <span id="btn-text">Generate Content</span>
                                    <span id="btn-loading" class="hidden">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Generating...
                                    </span>
                                </button>
                            </div>
                        @endif
                    </form>
                </div>
            </div>

            <!-- Tips Section -->
            @if($contentTypes->count() > 0)
                <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                    <h3 class="text-lg font-medium text-blue-900 mb-4">💡 Tips for {{ $category }} Content</h3>
                    <ul class="space-y-2 text-sm text-blue-800">
                        @switch($category)
                            @case('Content Creation')
                                <li>• Specify your target audience and content purpose</li>
                                <li>• Include main topics and key points to cover</li>
                                <li>• Mention desired tone (professional, casual, educational)</li>
                                <li>• Add any specific requirements or constraints</li>
                                @break
                            @case('E-commerce')
                                <li>• Include product features, benefits, and specifications</li>
                                <li>• Mention target customer demographics</li>
                                <li>• Specify unique selling points and differentiators</li>
                                <li>• Include price range or positioning if relevant</li>
                                @break
                            @case('Social Media')
                                <li>• Specify the social media platform (Instagram, Facebook, Twitter)</li>
                                <li>• Include relevant hashtags and mentions</li>
                                <li>• Mention your brand voice and personality</li>
                                <li>• Add call-to-action requirements</li>
                                @break
                            @case('Marketing')
                                <li>• Define your target audience clearly</li>
                                <li>• Include the main offer or promotion</li>
                                <li>• Specify the desired action (click, buy, sign up)</li>
                                <li>• Mention any urgency or scarcity elements</li>
                                @break
                            @case('SEO')
                                <li>• Include primary and secondary keywords</li>
                                <li>• Specify target search intent (informational, commercial)</li>
                                <li>• Mention competitor analysis if available</li>
                                <li>• Include target word count and structure preferences</li>
                                @break
                            @case('Website Content')
                                <li>• Specify the page type (homepage, about, landing page)</li>
                                <li>• Include your value proposition and benefits</li>
                                <li>• Mention target audience and their pain points</li>
                                <li>• Add call-to-action requirements</li>
                                @break
                            @case('Video Content')
                                <li>• Specify video length and format requirements</li>
                                <li>• Include key messages and talking points</li>
                                <li>• Mention target audience and platform</li>
                                <li>• Add timing and pacing preferences</li>
                                @break
                            @default
                                <li>• Be specific with your keywords and topic description</li>
                                <li>• Include context about your target audience or purpose</li>
                                <li>• Mention any specific requirements (tone, length, style)</li>
                                <li>• Use relevant industry terms or technical details when needed</li>
                        @endswitch
                    </ul>
                </div>
            @endif
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const keywordsTextarea = document.getElementById('keywords');
            const charCount = document.getElementById('char-count');
            const generateBtn = document.getElementById('generate-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');

            if (keywordsTextarea && charCount) {
                // Character counter
                keywordsTextarea.addEventListener('input', function() {
                    const count = this.value.length;
                    charCount.textContent = count;
                    
                    if (count > 450) {
                        charCount.classList.add('text-red-500');
                    } else {
                        charCount.classList.remove('text-red-500');
                    }
                });

                // Initialize character count
                keywordsTextarea.dispatchEvent(new Event('input'));
            }

            // Form submission with loading state
            if (form && generateBtn) {
                form.addEventListener('submit', function() {
                    generateBtn.disabled = true;
                    if (btnText) btnText.classList.add('hidden');
                    if (btnLoading) btnLoading.classList.remove('hidden');
                });
            }
        });
    </script>
</x-app-layout>
