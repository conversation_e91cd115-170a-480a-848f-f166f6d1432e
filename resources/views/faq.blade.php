<x-guest-layout>
    @php
        $pageSlug = 'faq';
        $page = \App\Models\Page::where('slug', $pageSlug)->first();
        $translation = $page?->translation(app()->getLocale());
    @endphp

    <x-slot name="head">
        @include('partials.seo-meta', [
            'page' => $page,
            'translation' => $translation,
            'schemaType' => 'faqpage',
            'ogType' => 'website',
        ])
    </x-slot>

    <!-- Hero Section -->
    <div class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 py-20">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <!-- Question <PERSON> -->
            <div
                class="absolute top-12 left-12 w-16 h-16 bg-white/10 rounded-full animate-pulse flex items-center justify-center">
                <span class="text-white text-2xl font-bold">?</span>
            </div>
            <div
                class="absolute top-24 right-24 w-12 h-12 bg-yellow-300/20 rounded-full animate-bounce flex items-center justify-center">
                <span class="text-white text-lg font-bold">?</span>
            </div>
            <div
                class="absolute bottom-20 left-1/4 w-20 h-20 bg-blue-300/10 rounded-full animate-ping flex items-center justify-center">
                <span class="text-white text-xl font-bold">?</span>
            </div>
            <div
                class="absolute bottom-32 right-1/3 w-14 h-14 bg-green-300/20 rounded-full animate-pulse flex items-center justify-center">
                <span class="text-white text-lg font-bold">?</span>
            </div>

            <!-- FAQ Icons -->
            <div class="absolute top-1/3 left-1/3 w-8 h-8 bg-white/20 rounded-lg animate-float">
                <svg class="w-full h-full p-1 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z" />
                </svg>
            </div>
            <div class="absolute top-1/2 right-1/4 w-6 h-6 bg-white/20 rounded-lg animate-float-delayed">
                <svg class="w-full h-full p-1 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            </div>

            <!-- Zigzag Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0"
                    style="background-image: repeating-linear-gradient(90deg, transparent, transparent 15px, rgba(255,255,255,0.1) 15px, rgba(255,255,255,0.1) 30px);">
                </div>
            </div>

            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
                    {{ __('marketing.faq_title') ?? 'Frequently Asked Questions' }}
                </h1>
                <p class="text-xl md:text-2xl text-orange-100 mb-8 max-w-3xl mx-auto drop-shadow-md">
                    {{ __('marketing.faq_subtitle') ?? 'Find answers to common questions about Content Spark and our AI-powered content generation platform.' }}
                </p>
            </div>
        </div>
    </div>

    <!-- FAQ Content -->
    <div class="py-16 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">

            <div class="space-y-6" x-data="{ openFaq: null }">
                @forelse($faqs as $index => $faq)
                    @php
                        $translation = $faq->translations->first();
                    @endphp

                    @if ($translation)
                        <div class="bg-gray-50 rounded-lg">
                            <button @click="openFaq = openFaq === {{ $index + 1 }} ? null : {{ $index + 1 }}"
                                class="w-full text-left px-6 py-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        {{ $translation->question }}
                                    </h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform"
                                        :class="{ 'rotate-180': openFaq === {{ $index + 1 }} }" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7">
                                        </path>
                                    </svg>
                                </div>
                            </button>
                            <div x-show="openFaq === {{ $index + 1 }}"
                                x-transition:enter="transition ease-out duration-200"
                                x-transition:enter-start="opacity-0 transform scale-95"
                                x-transition:enter-end="opacity-100 transform scale-100"
                                x-transition:leave="transition ease-in duration-150"
                                x-transition:leave-start="opacity-100 transform scale-100"
                                x-transition:leave-end="opacity-0 transform scale-95" class="px-6 pb-4">
                                <p class="pt-4 text-gray-600 leading-relaxed">
                                    {{ $translation->answer }}
                                </p>
                            </div>
                        </div>
                    @endif
                @empty
                    <div class="text-center py-12">
                        <p class="text-gray-500">{{ __('messages.no_faqs_available') }}</p>
                    </div>
                @endforelse
            </div>

            <!-- Still Have Questions Section -->
            <div class="mt-16 text-center">
                <div class="bg-gray-50 rounded-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Still have questions?</h3>
                    <p class="text-gray-600 mb-6">Can't find the answer you're looking for? We're here to help.</p>

                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('contact') }}"
                            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors">
                            Contact Support
                        </a>
                        @auth
                            <a href="{{ route('dashboard') }}"
                                class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                                Go to Dashboard
                            </a>
                        @else
                            <a href="{{ route('register') }}"
                                class="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                                Get Started Free
                            </a>
                        @endauth
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>
