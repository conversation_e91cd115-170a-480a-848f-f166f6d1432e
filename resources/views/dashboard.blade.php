<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Current Plan Card -->
            @if ($subscription && $subscription->plan)
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 overflow-hidden shadow-sm sm:rounded-lg mb-6">
                    <div class="p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold">Current Plan</h3>
                                <p class="text-2xl font-bold">{{ $subscription->plan->name }}</p>
                                <p class="text-blue-700">
                                    {{ $subscription->plan->monthly_generations_limit === -1 ? 'Unlimited' : $subscription->plan->monthly_generations_limit }}
                                    generations per
                                    {{ $subscription->billing_frequency === 'annual' ? 'year' : 'month' }}
                                </p>
                                @if ($subscription->billing_frequency === 'annual')
                                    <p class="text-blue-900 text-sm">
                                        Billed annually - Next renewal:
                                        {{ $subscription->ends_at ? $subscription->ends_at->format('M j, Y') : 'N/A' }}
                                    </p>
                                @endif
                            </div>
                            <div class="text-right">
                                @if (!$subscription->plan->isFree())
                                    <p class="text-blue-700">Next billing</p>
                                    <p class="font-semibold">
                                        {{ $subscription->ends_at ? $subscription->ends_at->format('M j, Y') : 'N/A' }}
                                    </p>
                                @endif
                                <a href="{{ route('pricing.index') }}"
                                    class="inline-block mt-2 px-4 py-2 bg-white bg-opacity-20 rounded-lg text-sm hover:bg-opacity-30 transition">
                                    {{ $subscription->plan->isFree() ? 'Upgrade Plan' : 'Manage Plan' }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Remaining Generations -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Remaining Generations</p>
                                <p class="text-2xl font-semibold text-gray-900">
                                    {{ $remainingGenerations === -1 ? 'Unlimited' : $remainingGenerations }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Generations -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Generations</p>
                                <p class="text-2xl font-semibold text-gray-900">{{ $totalGenerations }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Generate -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <a href="{{ route('generations.dashboard') }}"
                            class="flex items-center justify-center w-full h-full bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200">
                            <svg class="h-6 w-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4v16m8-8H4"></path>
                            </svg>
                            Generate Content
                        </a>
                    </div>
                </div>
            </div>

            <!-- Recent Generations -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Recent Generations</h3>
                        <a href="{{ route('generations.index') }}" class="text-blue-500 hover:text-blue-600">View
                            All</a>
                    </div>

                    @if ($recentGenerations->count() > 0)
                        <div class="space-y-4">
                            @foreach ($recentGenerations as $generation)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium text-gray-900">{{ $generation->contentType->name }}
                                            </h4>
                                            <p class="text-sm text-gray-500">{{ $generation->keywords }}</p>
                                            <p class="text-xs text-gray-400">
                                                {{ $generation->created_at->diffForHumans() }}</p>
                                        </div>
                                        <a href="{{ route('generations.show', $generation) }}"
                                            class="text-blue-500 hover:text-blue-600">
                                            View
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No generations yet</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating your first content generation.
                            </p>
                            <div class="mt-6">
                                <a href="{{ route('generations.dashboard') }}"
                                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                                    <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    Generate Content
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
