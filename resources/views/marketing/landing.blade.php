<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    @include('partials.seo-meta', [
        'title' => __('marketing.meta_title'),
        'description' => __('marketing.meta_description'),
        'keywords' => __('marketing.meta_keywords'),
        'schemaType' => 'website',
        'ogType' => 'website',
    ])

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link
        href="https://fonts.bunny.net/css?family=inter:300,400,500,600,700,800&family=space-grotesk:400,500,600,700&display=swap"
        rel="stylesheet" />

    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Modern CSS Variables -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 20px 60px rgba(0, 0, 0, 0.15);
            --shadow-strong: 0 30px 80px rgba(0, 0, 0, 0.2);
            --border-radius: 16px;
            --border-radius-lg: 24px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --font-primary: 'Inter', sans-serif;
            --font-display: 'Space Grotesk', sans-serif;
        }

        * {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-primary);
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .font-display {
            font-family: var(--font-display);
        }

        .glass-effect {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
        }

        .modern-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            transition: var(--transition);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .modern-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-medium);
        }

        .modern-btn {
            position: relative;
            overflow: hidden;
            transition: var(--transition);
            border-radius: 12px;
            font-weight: 600;
            letter-spacing: 0.025em;
        }

        .modern-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modern-btn:hover::before {
            left: 100%;
        }

        .gradient-text {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0px);
            }

            50% {
                transform: translateY(-20px);
            }
        }

        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease-out;
        }

        .fade-in-up.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .stagger-animation {
            transition-delay: calc(var(--stagger) * 0.1s);
        }
    </style>

    <!-- Analytics -->
    @if (config('services.google_analytics.id'))
        <!-- Google Analytics -->
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.id') }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }
            gtag('js', new Date());
            gtag('config', '{{ config('services.google_analytics.id') }}');
        </script>
    @endif
</head>

<body class="font-sans antialiased bg-white">
    <!-- Navigation -->
    <nav class="glass-effect sticky top-0 z-50 border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{{ route('home') }}"
                        class="text-3xl font-display font-bold gradient-text tracking-tight hover:scale-105 transition-transform duration-300">
                        Content Spark
                    </a>

                    <!-- Navigation Links -->
                    <div class="hidden md:flex md:ml-12 md:space-x-1">
                        <a href="{{ route('home') }}#features"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300">
                            {{ __('messages.features') }}
                        </a>
                        <a href="{{ route('pricing') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ request()->routeIs('pricing') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.pricing') }}
                        </a>
                        <a href="{{ route('faq') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ request()->routeIs('faq') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.faq') }}
                        </a>
                        <a href="{{ route('about') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ request()->routeIs('about') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.about') }}
                        </a>
                        <a href="{{ route('contact') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300 {{ request()->routeIs('contact') ? 'bg-blue-50 text-blue-700' : '' }}">
                            {{ __('messages.contact') }}
                        </a>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Language Switcher -->
                    <x-language-switcher />

                    <!-- Auth Links -->
                    @guest
                        <a href="{{ route('login') }}"
                            class="text-gray-700 hover:text-gray-900 px-4 py-2 text-sm font-medium rounded-lg hover:bg-white/50 transition-all duration-300">
                            {{ __('messages.login') }}
                        </a>
                        <a href="{{ route('register') }}"
                            class="modern-btn bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-105">
                            {{ __('marketing.hero_cta_primary') }}
                        </a>
                    @else
                        <a href="{{ route('dashboard') }}"
                            class="modern-btn bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 text-sm font-semibold shadow-lg hover:shadow-xl transform hover:scale-105">
                            {{ __('messages.dashboard') }}
                        </a>
                    @endguest
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <!-- Advanced Animated Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
            <!-- Animated Gradient Overlay -->
            <div
                class="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-indigo-600/10 animate-gradient-x">
            </div>

            <!-- Hero Content Icons Background -->
            <div class="absolute top-20 left-20 text-9xl text-blue-100/30 transform rotate-12 animate-pulse">✨</div>
            <div class="absolute top-40 right-20 text-7xl text-purple-100/30 transform -rotate-12 animate-pulse"
                style="animation-delay: 1s;">🚀</div>
            <div class="absolute bottom-40 left-32 text-8xl text-indigo-100/30 transform rotate-45 animate-pulse"
                style="animation-delay: 2s;">💡</div>
            <div class="absolute bottom-20 right-32 text-6xl text-pink-100/30 transform -rotate-45 animate-pulse"
                style="animation-delay: 3s;">⚡</div>

            <!-- Floating Hero Elements -->
            <div
                class="absolute top-1/4 left-1/6 w-48 h-48 bg-gradient-to-br from-blue-200/20 to-indigo-300/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse">
            </div>
            <div class="absolute bottom-1/4 right-1/6 w-56 h-56 bg-gradient-to-br from-purple-200/20 to-pink-300/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
                style="animation-delay: 2s;"></div>

            <!-- Dynamic Lines -->
            <svg class="absolute inset-0 w-full h-full opacity-20" viewBox="0 0 1000 1000" preserveAspectRatio="none">
                <defs>
                    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.4" />
                        <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:0.4" />
                        <stop offset="100%" style="stop-color:#6366f1;stop-opacity:0.4" />
                    </linearGradient>
                </defs>
                <path d="M0,300 Q500,200 1000,300" stroke="url(#heroGradient)" stroke-width="3" fill="none"
                    class="animate-pulse" />
                <path d="M0,700 Q500,800 1000,700" stroke="url(#heroGradient)" stroke-width="3" fill="none"
                    class="animate-pulse" style="animation-delay: 1.5s;" />
                <circle cx="200" cy="200" r="100" stroke="url(#heroGradient)" stroke-width="2"
                    fill="none" opacity="0.3" class="animate-pulse" style="animation-delay: 0.5s;" />
                <circle cx="800" cy="800" r="80" stroke="url(#heroGradient)" stroke-width="2" fill="none"
                    opacity="0.3" class="animate-pulse" style="animation-delay: 2.5s;" />
            </svg>

            <!-- Geometric Pattern -->
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="100" height="100"
                viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"%3E%3Cdefs%3E%3Cpattern id="grid"
                width="20" height="20" patternUnits="userSpaceOnUse"%3E%3Cpath d="M 20 0 L 0 0 0 20"
                fill="none" stroke="%23e2e8f0" stroke-width="0.5"/%3E%3C/pattern%3E%3C/defs%3E%3Crect width="100"
                height="100" fill="url(%23grid)"/%3E%3C/svg%3E')] opacity-30"></div>

            <!-- Floating Geometric Shapes -->
            <div
                class="absolute top-20 left-20 w-96 h-96 bg-gradient-to-br from-blue-400/20 to-purple-500/20 rounded-full mix-blend-multiply filter blur-3xl floating-animation">
            </div>
            <div class="absolute top-40 right-20 w-80 h-80 bg-gradient-to-br from-purple-400/20 to-pink-500/20 rounded-full mix-blend-multiply filter blur-3xl floating-animation"
                style="animation-delay: 2s;"></div>
            <div class="absolute bottom-20 left-1/2 w-72 h-72 bg-gradient-to-br from-indigo-400/20 to-blue-500/20 rounded-full mix-blend-multiply filter blur-3xl floating-animation"
                style="animation-delay: 4s;"></div>

            <!-- Animated Particles -->
            <div class="absolute top-1/4 left-1/4 w-4 h-4 bg-blue-400 rounded-full opacity-60 animate-bounce"
                style="animation-delay: 1s;"></div>
            <div class="absolute top-1/3 right-1/3 w-3 h-3 bg-purple-400 rounded-full opacity-60 animate-bounce"
                style="animation-delay: 2s;"></div>
            <div class="absolute bottom-1/3 left-1/3 w-2 h-2 bg-indigo-400 rounded-full opacity-60 animate-bounce"
                style="animation-delay: 3s;"></div>
            <div class="absolute bottom-1/4 right-1/4 w-3 h-3 bg-pink-400 rounded-full opacity-60 animate-bounce"
                style="animation-delay: 4s;"></div>

            <!-- Geometric Lines -->
            <svg class="absolute inset-0 w-full h-full opacity-10" viewBox="0 0 1000 1000"
                preserveAspectRatio="none">
                <defs>
                    <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
                        <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:0.3" />
                        <stop offset="100%" style="stop-color:#6366f1;stop-opacity:0.3" />
                    </linearGradient>
                </defs>
                <path d="M0,200 Q250,100 500,200 T1000,200" stroke="url(#lineGradient)" stroke-width="2"
                    fill="none" class="animate-pulse" />
                <path d="M0,400 Q250,300 500,400 T1000,400" stroke="url(#lineGradient)" stroke-width="2"
                    fill="none" class="animate-pulse" style="animation-delay: 1s;" />
                <path d="M0,600 Q250,500 500,600 T1000,600" stroke="url(#lineGradient)" stroke-width="2"
                    fill="none" class="animate-pulse" style="animation-delay: 2s;" />
            </svg>

            <!-- Mesh Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-br from-transparent via-white/5 to-transparent"></div>
        </div>

        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="fade-in-up">
                <div
                    class="inline-flex items-center px-4 py-2 rounded-full bg-white/80 backdrop-blur-sm border border-gray-200 text-sm font-medium text-gray-700 mb-8 shadow-lg">
                    <span class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                    {{ __('marketing.hero_trusted_by') }}
                </div>

                <h1 class="font-display text-5xl md:text-6xl lg:text-6xl font-bold mb-8 leading-tight">
                    <span class="gradient-text">{{ __('marketing.hero_title') }}</span>
                </h1>

                <p class="text-xl md:text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed font-medium">
                    {{ __('marketing.platform_description') }}
                </p>

                <div class="flex flex-col sm:flex-row gap-6 justify-center mb-16">
                    @guest
                        <a href="{{ route('register') }}" data-track="cta"
                            class="modern-btn group bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-5 px-10 text-lg shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300">
                            <span class="flex items-center justify-center">
                                {{ __('marketing.hero_cta_primary') }}
                                <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </span>
                        </a>
                        <a href="#demo"
                            class="modern-btn group glass-effect text-gray-700 font-bold py-5 px-10 text-lg border border-white/30 hover:bg-white/20 transition-all duration-300">
                            <span class="flex items-center justify-center">
                                {{ __('marketing.hero_cta_secondary') }}
                                <svg class="w-5 h-5 ml-2 group-hover:scale-110 transition-transform" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                    </path>
                                </svg>
                            </span>
                        </a>
                    @else
                        <a href="{{ route('dashboard') }}"
                            class="modern-btn group bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-5 px-10 text-lg shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300">
                            <span class="flex items-center justify-center">
                                {{ __('messages.dashboard') }}
                                <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                </svg>
                            </span>
                        </a>
                    @endguest
                </div>

                <!-- Trust Indicators -->
                <div class="flex flex-wrap justify-center items-center gap-8 opacity-60">
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium">{{ __('marketing.trust_no_credit_card') }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium">{{ __('marketing.trust_free_forever') }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-sm font-medium">{{ __('marketing.trust_quick_setup') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3">
                </path>
            </svg>
        </div>
    </section>

    <!-- Statistics Section -->
    <section class="py-24 bg-gradient-to-br from-gray-50 via-slate-50 to-white relative overflow-hidden">
        <!-- Enhanced Background Elements -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80"
            xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23cbd5e1"
            fill-opacity="0.08"%3E%3Cpath
            d="M40 40c0-11 9-20 20-20s20 9 20 20-9 20-20 20-20-9-20-20zm-20-20c0-11 9-20 20-20s20 9 20 20-9 20-20 20-20-9-20-20z"
            /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

        <!-- Floating Elements -->
        <div
            class="absolute top-10 left-10 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full filter blur-2xl animate-pulse">
        </div>
        <div class="absolute bottom-10 right-10 w-40 h-40 bg-gradient-to-br from-indigo-200/30 to-pink-200/30 rounded-full filter blur-2xl animate-pulse"
            style="animation-delay: 2s;"></div>

        <!-- Geometric Accent -->
        <div
            class="absolute top-1/2 left-0 w-1 h-32 bg-gradient-to-b from-transparent via-blue-300 to-transparent opacity-50">
        </div>
        <div
            class="absolute top-1/2 right-0 w-1 h-32 bg-gradient-to-b from-transparent via-purple-300 to-transparent opacity-50">
        </div>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-16 fade-in-up">
                <h2 class="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    {{ __('marketing.stats_title') }}
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full"></div>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                @foreach ($stats as $index => $stat)
                    <div class="text-center fade-in-up stagger-animation" style="--stagger: {{ $index }}">
                        <div class="modern-card p-8 hover:scale-105 transition-all duration-300 group">
                            <div
                                class="text-5xl md:text-6xl font-display font-bold gradient-text mb-4 group-hover:scale-110 transition-transform duration-300">
                                {{ $stat['number'] }}
                            </div>
                            <div class="text-gray-600 font-medium text-lg">
                                {{ __($stat['label']) }}
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Benefits Section -->
    <section class="py-24 bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 relative overflow-hidden">
        <!-- Enhanced Background Pattern -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60"
            xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366f1"
            fill-opacity="0.06"%3E%3Cpath
            d="M30 30c0-8.3 6.7-15 15-15s15 6.7 15 15-6.7 15-15 15-15-6.7-15-15zm-15-15c0-8.3 6.7-15 15-15s15 6.7 15 15-6.7 15-15 15-15-6.7-15-15z"
            /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

        <!-- Animated Background Elements -->
        <div
            class="absolute top-20 left-1/4 w-64 h-64 bg-gradient-to-br from-blue-300/20 to-indigo-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse">
        </div>
        <div class="absolute bottom-20 right-1/4 w-80 h-80 bg-gradient-to-br from-purple-300/20 to-pink-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
            style="animation-delay: 3s;"></div>

        <!-- Decorative Lines -->
        <svg class="absolute inset-0 w-full h-full opacity-20" viewBox="0 0 1000 800" preserveAspectRatio="none">
            <defs>
                <linearGradient id="benefitsGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.2" />
                    <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:0.2" />
                </linearGradient>
            </defs>
            <path d="M0,100 Q500,50 1000,100" stroke="url(#benefitsGradient)" stroke-width="1" fill="none" />
            <path d="M0,700 Q500,750 1000,700" stroke="url(#benefitsGradient)" stroke-width="1" fill="none" />
        </svg>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-20 fade-in-up">
                <h2 class="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    {{ __('marketing.benefits_title') }}
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    {{ __('marketing.benefits_subtitle') }}
                </p>
                <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full mt-8"></div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach ($benefits as $index => $benefit)
                    <div class="fade-in-up stagger-animation" style="--stagger: {{ $index }}">
                        <div
                            class="modern-card p-8 h-full group hover:bg-gradient-to-br hover:from-white hover:to-blue-50 transition-all duration-500">
                            <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                                {{ $benefit['icon'] }}
                            </div>
                            <h3
                                class="font-display text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors">
                                {{ __($benefit['title']) }}
                            </h3>
                            <p class="pt-4 text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">
                                {{ __($benefit['description']) }}
                            </p>

                            <!-- Decorative element -->
                            <div
                                class="mt-6 w-12 h-1 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features"
        class="py-24 bg-gradient-to-br from-white via-gray-50 to-slate-50 relative overflow-hidden">
        <!-- Advanced Background Design -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-indigo-50/30 to-purple-50/40"></div>

        <!-- Hexagonal Pattern -->
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="120" height="120"
            viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none"
            fill-rule="evenodd"%3E%3Cg fill="%23e2e8f0" fill-opacity="0.1"%3E%3Cpath
            d="M60 60l30-17.32v-34.64L60 0 30 8.04v34.64L60 60zm30 52.32L60 120l-30-7.68V77.68L60 60l30 17.32v34.64z"
            /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-60"></div>

        <!-- Floating Geometric Elements -->
        <div
            class="absolute top-16 right-16 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-indigo-500/20 transform rotate-45 rounded-lg animate-pulse">
        </div>
        <div class="absolute bottom-16 left-16 w-32 h-32 bg-gradient-to-br from-purple-400/20 to-pink-500/20 transform rotate-12 rounded-lg animate-pulse"
            style="animation-delay: 2s;"></div>

        <!-- Animated Dots -->
        <div class="absolute top-1/3 left-1/6 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
        <div class="absolute top-2/3 right-1/6 w-3 h-3 bg-purple-400 rounded-full animate-ping"
            style="animation-delay: 1s;"></div>
        <div class="absolute bottom-1/3 left-1/3 w-2 h-2 bg-indigo-400 rounded-full animate-ping"
            style="animation-delay: 2s;"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center mb-20 fade-in-up">
                <h2 class="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    {{ __('marketing.features_title') }}
                </h2>
                <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                    {{ __('marketing.features_subtitle') }}
                </p>
                <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full mt-8"></div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach ($features as $index => $feature)
                    <div class="text-center fade-in-up stagger-animation" style="--stagger: {{ $index }}">
                        <div
                            class="modern-card p-8 group hover:bg-gradient-to-br hover:from-white hover:to-indigo-50 transition-all duration-500">
                            <!-- Icon Container -->
                            <div class="relative mb-8">
                                <div
                                    class="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                                    <span class="text-4xl">{{ $feature['icon'] }}</span>
                                </div>
                                <!-- Floating badge -->
                                <div
                                    class="absolute -top-2 -right-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                                    NEW
                                </div>
                            </div>

                            <!-- Metric -->
                            <div
                                class="text-4xl font-display font-bold gradient-text mb-4 group-hover:scale-110 transition-transform duration-300">
                                {{ $feature['metric'] }}
                            </div>

                            <!-- Title -->
                            <h3
                                class="font-display text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors">
                                {{ __($feature['title']) }}
                            </h3>

                            <!-- Description -->
                            <p class="pt-4 text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors">
                                {{ __($feature['description']) }}
                            </p>

                            <!-- Progress bar animation -->
                            <div class="mt-6 w-full bg-gray-200 rounded-full h-1 overflow-hidden">
                                <div
                                    class="h-full bg-gradient-to-r from-blue-500 to-purple-600 rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out">
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </section>

    <!-- Continue with more sections... -->
    @include('marketing.partials.testimonials')
    @include('marketing.partials.pricing')
    @include('marketing.partials.content-types')
    {{-- @include('marketing.partials.faq_2') --}}
    @include('marketing.partials.faq')
    @include('marketing.partials.newsletter')
    @include('marketing.partials.cta')

    <!-- Footer -->
    @include('marketing.partials.footer')

    <!-- Exit Intent Popup -->
    @include('marketing.partials.exit-intent')

    <!-- Scripts -->
    <script>
        // Modern scroll animations and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for fade-in animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // Observe all fade-in elements
            document.querySelectorAll('.fade-in-up').forEach(el => {
                observer.observe(el);
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Modern button hover effects
            document.querySelectorAll('.modern-btn').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });

                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Card tilt effect
            document.querySelectorAll('.modern-card').forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;

                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;

                    this.style.transform =
                        `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform =
                        'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
                });
            });

            // Track conversions
            function trackConversion(action) {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'conversion', {
                        'action': action,
                        'language': '{{ app()->getLocale() }}'
                    });
                }
            }

            // Track CTA clicks
            document.querySelectorAll('[data-track="cta"]').forEach(button => {
                button.addEventListener('click', () => trackConversion('cta_click'));
            });

            // Navbar background on scroll
            const navbar = document.querySelector('nav');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 100) {
                    navbar.classList.add('backdrop-blur-md', 'bg-white/90');
                } else {
                    navbar.classList.remove('backdrop-blur-md', 'bg-white/90');
                }
            });
        });
    </script>
</body>

</html>
