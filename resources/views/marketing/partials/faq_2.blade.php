<!-- FAQ Section -->
<section class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {{ __('marketing.faq_title') }}
            </h2>
            <p class="text-xl text-gray-600">
                {{ __('marketing.faq_subtitle') }}
            </p>
        </div>

        <div class="space-y-6" x-data="{ openFaq: null }">
            @forelse($faqs->take(5) as $index => $faq)
                @php
                    $translation = $faq->translations->first();
                @endphp

                @if ($translation)
                    <div class="bg-gray-50 rounded-lg">
                        <button @click="openFaq = openFaq === {{ $index + 1 }} ? null : {{ $index + 1 }}"
                            class="w-full text-left px-6 py-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    {{ $translation->question }}
                                </h3>
                                <svg class="w-5 h-5 text-gray-500 transform transition-transform"
                                    :class="{ 'rotate-180': openFaq === {{ $index + 1 }} }" fill="none"
                                    stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7">
                                    </path>
                                </svg>
                            </div>
                        </button>
                        <div x-show="openFaq === {{ $index + 1 }}"
                            x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="opacity-0 transform scale-95"
                            x-transition:enter-end="opacity-100 transform scale-100"
                            x-transition:leave="transition ease-in duration-150"
                            x-transition:leave-start="opacity-100 transform scale-100"
                            x-transition:leave-end="opacity-0 transform scale-95" class="px-6 pb-4">
                            <p class="pt-4 text-gray-600 leading-relaxed">
                                {{ $translation->answer }}
                            </p>
                        </div>
                    </div>
                @endif
            @empty
                <div class="text-center py-12">
                    <p class="text-gray-500">{{ __('messages.no_faqs_available') }}</p>
                </div>
            @endforelse
        </div>

        <div class="text-center mt-12">
            <p class="text-gray-600 mb-6">
                Still have questions? We're here to help!
            </p>
            <a href="{{ route('contact') }}"
                class="bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold py-3 px-6 rounded-lg transition-colors">
                {{ __('messages.contact') }}
            </a>
        </div>
    </div>
</section>
