<!-- FAQ Section -->
<section class="py-20 bg-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {{ __('marketing.faq_title') }}
            </h2>
            <p class="text-xl text-gray-600">
                {{ __('marketing.faq_subtitle') }}
            </p>
        </div>
        <!-- FAQ Section -->
        {{-- <section class="py-20 bg-gradient-to-br from-white via-blue-50 to-fuchsia-50"> --}}
        <section class="">
            <div class="max-w-4xl mx-auto px-4">
                <div class="space-y-4">
                    @php
                        $colors = ['blue', 'fuchsia', 'purple', 'green', 'indigo', 'pink', 'yellow', 'teal'];
                    @endphp

                    @forelse($faqs as $index => $faq)
                        @php
                            $translation = $faq->translations->first();
                            $color = $colors[$index % count($colors)];
                        @endphp

                        @if ($translation)
                            <div x-data="{ open: false }"
                                class="bg-white rounded-xl shadow-lg border-l-4 border-{{ $color }}-400">
                                <button @click="open = !open"
                                    class="w-full text-left p-8 focus:outline-none rounded-xl">
                                    <div class="flex justify-between items-center">
                                        <h3 class="font-bold text-lg text-{{ $color }}-700">
                                            {{ $translation->question }}</h3>
                                        <svg class="w-5 h-5 text-{{ $color }}-700 transition-transform duration-200"
                                            :class="{ 'rotate-180': open }" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7">
                                            </path>
                                        </svg>
                                    </div>
                                </button>
                                <div x-show="open" x-transition:enter="transition ease-out duration-300"
                                    x-transition:enter-start="opacity-0 transform scale-95"
                                    x-transition:enter-end="opacity-100 transform scale-100"
                                    x-transition:leave="transition ease-in duration-200"
                                    x-transition:leave-start="opacity-100 transform scale-100"
                                    x-transition:leave-end="opacity-0 transform scale-95" class="px-8 pb-8">
                                    <p class="text-slate-600">{{ $translation->answer }}</p>
                                </div>
                            </div>
                        @endif
                    @empty
                        <div class="text-center py-12">
                            <p class="text-gray-500">{{ __('messages.no_faqs_available') }}</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </section>


        <div class="text-center mt-12">
            <p class="text-gray-600 mb-6">
                {{ __('marketing.faq_help_text') }}
            </p>
            <a href="{{ \App\Helpers\LocalizedUrlHelper::route('contact') }}"
                class="bg-gray-100 hover:bg-gray-200 text-gray-900 font-semibold py-3 px-6 rounded-lg transition-colors">
                {{ __('messages.contact') }}
            </a>
        </div>
    </div>
</section>
