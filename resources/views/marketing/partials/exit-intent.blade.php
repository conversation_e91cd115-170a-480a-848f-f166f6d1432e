<!-- Exit Intent Popup -->
@guest
    <div id="exit-intent-popup"
        class="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center hidden"
        x-data="{ show: false }" x-show="show" x-transition:enter="transition ease-out duration-500"
        x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0">

        <div class="modern-card glass-effect border border-white/20 p-10 max-w-lg mx-4 relative overflow-hidden"
            @click.away="show = false" x-transition:enter="transition ease-out duration-500"
            x-transition:enter-start="opacity-0 transform scale-90 rotate-3"
            x-transition:enter-end="opacity-100 transform scale-100 rotate-0"
            x-transition:leave="transition ease-in duration-300"
            x-transition:leave-start="opacity-100 transform scale-100 rotate-0"
            x-transition:leave-end="opacity-0 transform scale-90 rotate-3">

            <!-- Background Gradient -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 opacity-90"></div>

            <!-- Close Button -->
            <button @click="show = false"
                class="absolute top-4 right-4 w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center text-gray-500 hover:text-gray-700 transition-all duration-200 z-10 group">
                <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>

            <!-- Content -->
            <div class="text-center relative z-10">
                <!-- Animated Icon -->
                <div class="relative mb-6">
                    <div
                        class="w-20 h-20 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-2xl animate-pulse">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <!-- Floating particles -->
                    <div class="absolute -top-2 -right-2 w-4 h-4 bg-blue-400 rounded-full animate-bounce"></div>
                    <div class="absolute -bottom-2 -left-2 w-3 h-3 bg-purple-400 rounded-full animate-bounce"
                        style="animation-delay: 0.5s;"></div>
                </div>

                <h3 class="font-display text-3xl font-bold gradient-text mb-4">
                    Wait! Don't Miss Out!
                </h3>
                <p class="text-gray-700 mb-8 text-lg leading-relaxed">
                    Get started with <span class="font-semibold text-blue-600">Content Spark</span> today and create your
                    first professional content in under 60 seconds. No credit card required!
                </p>

                <!-- Benefits List -->
                <div class="mb-8 space-y-3">
                    <div class="flex items-center justify-center text-sm text-gray-600">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Free forever plan available</span>
                    </div>
                    <div class="flex items-center justify-center text-sm text-gray-600">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Setup in 60 seconds</span>
                    </div>
                    <div class="flex items-center justify-center text-sm text-gray-600">
                        <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        <span class="font-medium">Professional AI content</span>
                    </div>
                </div>

                <div class="space-y-4">
                    <a href="{{ route('register') }}" data-track="exit-intent-cta"
                        class="modern-btn w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-6 text-lg shadow-2xl group">
                        <span class="flex items-center justify-center">
                            {{ __('marketing.hero_cta_primary') }}
                            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </span>
                    </a>
                    <button @click="show = false"
                        class="w-full glass-effect text-gray-700 font-medium py-3 px-6 rounded-xl border border-gray-200 hover:bg-gray-50 transition-all duration-200">
                        Maybe Later
                    </button>
                </div>

                <p class="text-gray-500 text-sm mt-6 flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"></path>
                    </svg>
                    {{ __('marketing.cta_no_credit_card') }}
                </p>
            </div>

            <!-- Decorative Elements -->
            <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let exitIntentShown = false;
            const popup = document.getElementById('exit-intent-popup');

            // Check if user has already seen the popup in this session
            if (sessionStorage.getItem('exitIntentShown')) {
                return;
            }

            // Track mouse movement to detect exit intent
            document.addEventListener('mouseleave', function(e) {
                if (e.clientY <= 0 && !exitIntentShown) {
                    showExitIntentPopup();
                }
            });

            // Show popup after 30 seconds if not shown yet
            setTimeout(function() {
                if (!exitIntentShown) {
                    showExitIntentPopup();
                }
            }, 30000);

            function showExitIntentPopup() {
                exitIntentShown = true;
                sessionStorage.setItem('exitIntentShown', 'true');

                // Use Alpine.js to show the popup
                popup.style.display = 'flex';
                popup._x_dataStack[0].show = true;

                // Track the popup show event
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'exit_intent_popup_shown', {
                        'language': '{{ app()->getLocale() }}'
                    });
                }
            }
        });
    </script>
@endguest
