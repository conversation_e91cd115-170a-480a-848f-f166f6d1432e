<!-- Footer -->
<footer class="bg-gray-50 border-t border-gray-200">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
            <!-- Company Info -->
            <div class="col-span-1 md:col-span-2">
                <a href="{{ \App\Helpers\LocalizedUrlHelper::route('home') }}"
                    class="text-2xl font-extrabold text-gradient bg-gradient-to-r from-blue-600 via-purple-500 to-fuchsia-500 bg-clip-text text-transparent tracking-tight">
                    Content Spark
                </a>
                <p class="mt-4 text-gray-600 max-w-md">
                    {{ __('marketing.footer_description') }}
                </p>

                <!-- Social Links -->
                <div class="mt-6 flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <span class="sr-only">Twitter</span>
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84">
                            </path>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <span class="sr-only">LinkedIn</span>
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z">
                            </path>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <span class="sr-only">YouTube</span>
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z">
                            </path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Product Links -->
            <div>
                <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                    {{ __('marketing.footer_product') }}</h3>
                <ul class="space-y-3">
                    <li><a href="{{ \App\Helpers\LocalizedUrlHelper::route('pricing') }}"
                            class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('messages.pricing') }}</a>
                    </li>
                    <li><a href="{{ \App\Helpers\LocalizedUrlHelper::route('faq') }}"
                            class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('messages.faq') }}</a>
                    </li>
                    <li><a href="{{ \App\Helpers\LocalizedUrlHelper::route('home') }}#features"
                            class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('marketing.features_link') }}</a>
                    </li>
                    @guest
                        <li><a href="{{ route('register') }}"
                                class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('marketing.hero_cta_primary') }}</a>
                        </li>
                    @else
                        <li><a href="{{ route('dashboard') }}"
                                class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('messages.dashboard') }}</a>
                        </li>
                    @endguest
                </ul>
            </div>

            <!-- Company Links -->
            <div>
                <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                    {{ __('marketing.footer_company') }}</h3>
                <ul class="space-y-3">
                    <li><a href="{{ \App\Helpers\LocalizedUrlHelper::route('about') }}"
                            class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('messages.about') }}</a>
                    </li>
                    <li><a href="{{ \App\Helpers\LocalizedUrlHelper::route('contact') }}"
                            class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('messages.contact') }}</a>
                    </li>
                    <li><a href="{{ \App\Helpers\LocalizedUrlHelper::route('privacy-policy') }}"
                            class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('marketing.privacy_policy') }}</a>
                    </li>
                    <li><a href="{{ \App\Helpers\LocalizedUrlHelper::route('terms-of-service') }}"
                            class="text-gray-600 hover:text-gray-900 transition-colors">{{ __('marketing.terms_of_service') }}</a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Language Switcher in Footer -->
        <div class="mt-8 pt-8 border-t border-gray-200 flex flex-col md:flex-row justify-between items-center">
            <div class="flex items-center space-x-4 mb-4 md:mb-0">
                <span class="text-gray-600 text-sm">{{ __('marketing.footer_language') }}</span>
                <x-language-switcher class="text-sm" />
            </div>

            <p class="text-gray-600 text-sm text-center md:text-right">
                &copy; {{ date('Y') }} Content Spark. {{ __('marketing.footer_copyright') }}
            </p>
        </div>
    </div>
</footer>
