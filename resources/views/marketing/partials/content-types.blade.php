<!-- Content Types Showcase -->
<section class="py-24 bg-gradient-to-br from-white via-slate-50 to-gray-100 relative overflow-hidden">
    <!-- Enhanced Background Design -->
    <div class="absolute inset-0 bg-gradient-to-br from-indigo-50/40 via-purple-50/30 to-pink-50/40"></div>

    <!-- Content Type Icons Background -->
    <div class="absolute top-20 left-20 text-8xl text-blue-100/40 transform rotate-12">📝</div>
    <div class="absolute top-40 right-20 text-6xl text-purple-100/40 transform -rotate-12">📱</div>
    <div class="absolute bottom-40 left-32 text-7xl text-green-100/40 transform rotate-45">🛍️</div>
    <div class="absolute bottom-20 right-32 text-5xl text-pink-100/40 transform -rotate-45">📧</div>

    <!-- Floating Content Elements -->
    <div
        class="absolute top-32 left-1/4 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-indigo-300/20 rounded-full mix-blend-multiply filter blur-2xl animate-pulse">
    </div>
    <div class="absolute bottom-32 right-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/20 to-pink-300/20 rounded-full mix-blend-multiply filter blur-2xl animate-pulse"
        style="animation-delay: 1.5s;"></div>

    <!-- Grid Pattern -->
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%236366f1"
        fill-opacity="0.04"%3E%3Cpath
        d="M50 50c0-13.8 11.2-25 25-25s25 11.2 25 25-11.2 25-25 25-25-11.2-25-25zm-25-25c0-13.8 11.2-25 25-25s25 11.2 25 25-11.2 25-25 25-25-11.2-25-25z"
        /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-60"></div>

    <!-- Decorative Lines -->
    <svg class="absolute inset-0 w-full h-full opacity-10" viewBox="0 0 1000 600" preserveAspectRatio="none">
        <defs>
            <linearGradient id="contentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#6366f1;stop-opacity:0.4" />
                <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:0.4" />
                <stop offset="100%" style="stop-color:#ec4899;stop-opacity:0.4" />
            </linearGradient>
        </defs>
        <path d="M0,200 Q250,150 500,200 T1000,200" stroke="url(#contentGradient)" stroke-width="3" fill="none"
            class="animate-pulse" />
        <path d="M0,400 Q250,450 500,400 T1000,400" stroke="url(#contentGradient)" stroke-width="3" fill="none"
            class="animate-pulse" style="animation-delay: 2s;" />
    </svg>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-20 fade-in-up">
            <h2 class="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                {{ __('marketing.content_types_title') }}
            </h2>
            <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                {{ __('marketing.content_types_subtitle') }}
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-indigo-600 to-purple-600 mx-auto rounded-full mt-8"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Social Media Posts -->
            <div class="fade-in-up stagger-animation" style="--stagger: 0">
                <div
                    class="modern-card p-8 h-full group hover:bg-gradient-to-br hover:from-white hover:to-pink-50 transition-all duration-500 relative overflow-hidden">
                    <div class="relative mb-8">
                        <div
                            class="w-20 h-20 mx-auto bg-gradient-to-br from-pink-400 to-rose-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl group-hover:scale-110 transition-all duration-300">
                            <svg class="w-10 h-10 text-green" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z">
                                </path>
                            </svg>
                        </div>
                        <!-- Floating badge -->
                        <div
                            class="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-pink-500 to-rose-600 rounded-full flex items-center justify-center shadow-lg">
                            <span class="text-white text-xs font-bold">📱</span>
                        </div>
                    </div>
                    <h3
                        class="font-display text-xl font-bold text-gray-900 mb-4 text-center group-hover:text-pink-700 transition-colors">
                        {{ __('marketing.content_type_social') }}
                    </h3>
                    <p
                        class="text-gray-600 text-center mb-6 leading-relaxed group-hover:text-gray-700 transition-colors">
                        Create engaging posts for Instagram, Facebook, Twitter, and LinkedIn that drive engagement and
                        grow
                        your audience.
                    </p>
                    <div class="bg-gradient-to-br from-pink-50 to-rose-50 rounded-xl p-4 border border-pink-100">
                        <div class="text-sm text-pink-600 mb-2 font-semibold">Example:</div>
                        <div class="text-sm text-gray-700 italic leading-relaxed">
                            "🚀 Just discovered the secret to 10x faster content creation! AI is revolutionizing how we
                            write... #ContentMarketing #AI"
                        </div>
                    </div>
                    <!-- Progress bar animation -->
                    <div class="mt-6 w-full bg-gray-200 rounded-full h-1 overflow-hidden">
                        <div
                            class="h-full bg-gradient-to-r from-pink-500 to-rose-600 rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out">
                        </div>
                    </div>
                    <!-- Hover Effect Border -->
                    <div
                        class="absolute inset-0 rounded-2xl bg-gradient-to-r from-pink-400 to-rose-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 -z-10">
                    </div>
                </div>
            </div>

            <!-- Blog Articles -->
            <div class="bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow group">
                <div
                    class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">
                    {{ __('marketing.content_type_blog') }}
                </h3>
                <p class="text-gray-600 text-center mb-6">
                    Generate comprehensive blog posts, articles, and long-form content that educates and converts your
                    readers.
                </p>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-2">Example:</div>
                    <div class="text-sm text-gray-700 italic">
                        "The Ultimate Guide to AI Content Creation: How to Save 90% of Your Writing Time While
                        Maintaining Quality..."
                    </div>
                </div>
            </div>

            <!-- Product Descriptions -->
            <div class="bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow group">
                <div
                    class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">
                    {{ __('marketing.content_type_product') }}
                </h3>
                <p class="text-gray-600 text-center mb-6">
                    Craft compelling product descriptions that highlight benefits and drive sales for your e-commerce
                    store.
                </p>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-2">Example:</div>
                    <div class="text-sm text-gray-700 italic">
                        "Transform your workspace with this ergonomic office chair featuring premium memory foam and
                        360° swivel..."
                    </div>
                </div>
            </div>

            <!-- Email Marketing -->
            <div class="bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow group">
                <div
                    class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                        </path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">
                    {{ __('marketing.content_type_email') }}
                </h3>
                <p class="text-gray-600 text-center mb-6">
                    Create persuasive email campaigns, newsletters, and automated sequences that convert subscribers
                    into customers.
                </p>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-2">Example:</div>
                    <div class="text-sm text-gray-700 italic">
                        "Subject: Your content creation just got 10x easier 🚀 Hi [Name], Struggling with writer's
                        block?..."
                    </div>
                </div>
            </div>

            <!-- SEO Content -->
            <div class="bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow group">
                <div
                    class="bg-yellow-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">
                    {{ __('marketing.content_type_seo') }}
                </h3>
                <p class="text-gray-600 text-center mb-6">
                    Generate SEO-optimized content with proper keyword integration to rank higher in search results.
                </p>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-2">Example:</div>
                    <div class="text-sm text-gray-700 italic">
                        "Meta Description: Discover the best AI content generation tools for 2024. Create high-quality
                        content 10x faster..."
                    </div>
                </div>
            </div>

            <!-- Video Scripts -->
            <div class="bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow group">
                <div
                    class="bg-red-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                    <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z">
                        </path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4 text-center">
                    {{ __('marketing.content_type_video') }}
                </h3>
                <p class="text-gray-600 text-center mb-6">
                    Write engaging video scripts for YouTube, TikTok, and other platforms that capture attention and
                    drive views.
                </p>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-2">Example:</div>
                    <div class="text-sm text-gray-700 italic">
                        "[Hook] What if I told you there's a way to create a week's worth of content in just 30 minutes?
                        [Pause for effect]..."
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-16 fade-in-up">
            <div
                class="modern-card inline-block p-8 bg-gradient-to-br from-white to-indigo-50 border-2 border-indigo-100">
                <h3 class="font-display text-2xl font-bold text-gray-900 mb-4">
                    {{ __('marketing.content_types_cta_title') }}</h3>
                <p class="text-gray-600 mb-6 max-w-md mx-auto">{{ __('marketing.content_types_cta_subtitle') }}</p>
                @guest
                    <a href="{{ route('register') }}" data-track="cta"
                        class="modern-btn bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-4 px-8 text-lg shadow-xl group">
                        <span class="flex items-center justify-center">
                            {{ __('marketing.hero_cta_primary') }}
                            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </span>
                    </a>
                @else
                    <a href="{{ route('dashboard') }}"
                        class="modern-btn bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-4 px-8 text-lg shadow-xl group">
                        <span class="flex items-center justify-center">
                            {{ __('messages.dashboard') }}
                            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                                stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </span>
                    </a>
                @endguest
            </div>
        </div>
    </div>
</section>
