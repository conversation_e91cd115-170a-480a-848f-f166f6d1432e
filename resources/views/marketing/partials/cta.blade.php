<!-- Final CTA Section -->
<section class="py-24 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 relative overflow-hidden">
    <!-- Enhanced Background Design -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-900/60 via-indigo-900/40 to-purple-900/60"></div>

    <!-- Animated Stars Background -->
    <div class="absolute top-20 left-20 w-2 h-2 bg-white rounded-full animate-pulse opacity-60"></div>
    <div class="absolute top-32 right-32 w-1 h-1 bg-blue-300 rounded-full animate-pulse opacity-80"
        style="animation-delay: 1s;"></div>
    <div class="absolute bottom-40 left-40 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-70"
        style="animation-delay: 2s;"></div>
    <div class="absolute bottom-20 right-20 w-1 h-1 bg-indigo-300 rounded-full animate-pulse opacity-60"
        style="animation-delay: 3s;"></div>

    <!-- Floating CTA Elements -->
    <div
        class="absolute top-32 left-1/6 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-indigo-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse">
    </div>
    <div class="absolute bottom-32 right-1/6 w-48 h-48 bg-gradient-to-br from-purple-400/10 to-pink-500/10 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
        style="animation-delay: 2s;"></div>

    <!-- Geometric Pattern -->
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="120" height="120" viewBox="0 0 120 120"
        xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff"
        fill-opacity="0.03"%3E%3Cpath
        d="M60 60l30-17.32v-34.64L60 0 30 8.04v34.64L60 60zm30 52.32L60 120l-30-7.68V77.68L60 60l30 17.32v34.64z"
        /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-40"></div>

    <!-- Decorative Lines -->
    <svg class="absolute inset-0 w-full h-full opacity-20" viewBox="0 0 1000 400" preserveAspectRatio="none">
        <defs>
            <linearGradient id="ctaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.6" />
                <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:0.6" />
                <stop offset="100%" style="stop-color:#6366f1;stop-opacity:0.6" />
            </linearGradient>
        </defs>
        <path d="M0,100 Q500,50 1000,100" stroke="url(#ctaGradient)" stroke-width="2" fill="none"
            class="animate-pulse" />
        <path d="M0,300 Q500,350 1000,300" stroke="url(#ctaGradient)" stroke-width="2" fill="none"
            class="animate-pulse" style="animation-delay: 1s;" />
    </svg>

    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="fade-in-up">
            <h2 class="font-display text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                {{ __('marketing.cta_title') }}
            </h2>
            <p class="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl mx-auto leading-relaxed">
                {{ __('marketing.cta_subtitle') }}
            </p>
        </div>

        @guest
            <div class="flex flex-col sm:flex-row gap-6 justify-center mb-8 fade-in-up" style="--stagger: 1">
                <a href="{{ route('register') }}" data-track="cta"
                    class="modern-btn bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-5 px-10 text-lg shadow-2xl group">
                    <span class="flex items-center justify-center">
                        {{ __('marketing.cta_button') }}
                        <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </span>
                </a>
                <a href="{{ route('pricing') }}"
                    class="modern-btn glass-effect text-white font-bold py-5 px-10 text-lg border border-white/30 hover:bg-white/20 transition-all duration-300 group">
                    <span class="flex items-center justify-center">
                        {{ __('messages.pricing') }}
                        <svg class="w-5 h-5 ml-2 group-hover:scale-110 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                    </span>
                </a>
            </div>

            <!-- Trust Badges -->
            <div class="fade-in-up flex flex-wrap justify-center items-center gap-6 mb-8" style="--stagger: 2">
                <div class="modern-card px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20">
                    <p class="text-gray-300 text-sm font-medium flex items-center">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        {{ __('marketing.trust_no_credit_card') }}
                    </p>
                </div>
                <div class="modern-card px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20">
                    <p class="text-gray-300 text-sm font-medium flex items-center">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        {{ __('marketing.trust_free_forever') }}
                    </p>
                </div>
                <div class="modern-card px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20">
                    <p class="text-gray-300 text-sm font-medium flex items-center">
                        <svg class="w-4 h-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        {{ __('marketing.trust_quick_setup') }}
                    </p>
                </div>
            </div>
        @else
            <div class="fade-in-up">
                <a href="{{ route('dashboard') }}"
                    class="modern-btn bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-5 px-10 text-lg shadow-2xl group inline-block">
                    <span class="flex items-center justify-center">
                        {{ __('messages.dashboard') }}
                        <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none"
                            stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                        </svg>
                    </span>
                </a>
            </div>
        @endguest
    </div>
</section>
