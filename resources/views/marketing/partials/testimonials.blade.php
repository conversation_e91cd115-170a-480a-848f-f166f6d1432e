<!-- Testimonials Section -->
<section class="py-24 bg-gradient-to-br from-purple-50 via-white to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="100" height="100" viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23667eea" fill-opacity="0.03"%3E%3Cpolygon
        points="50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40"/%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-20 fade-in-up">
            <h2 class="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                {{ __('marketing.testimonials_title') }}
            </h2>
            <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                {{ __('marketing.testimonials_subtitle') }}
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-purple-600 to-indigo-600 mx-auto rounded-full mt-8"></div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            @foreach ($testimonials as $index => $testimonial)
                <div class="fade-in-up stagger-animation" style="--stagger: {{ $index }}">
                    <div
                        class="modern-card p-8 h-full group hover:bg-gradient-to-br hover:from-white hover:to-purple-50 transition-all duration-500 relative overflow-hidden">
                        <!-- Quote Icon -->
                        <div class="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity">
                            <svg class="w-16 h-16 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z" />
                            </svg>
                        </div>

                        <!-- Rating Stars -->
                        <div class="flex mb-6">
                            @for ($i = 0; $i < $testimonial['rating']; $i++)
                                <svg class="w-6 h-6 text-yellow-400 drop-shadow-sm" fill="currentColor"
                                    viewBox="0 0 20 20">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z">
                                    </path>
                                </svg>
                            @endfor
                        </div>

                        <!-- Testimonial Content -->
                        <blockquote
                            class="text-gray-700 mb-8 leading-relaxed text-lg font-medium group-hover:text-gray-800 transition-colors">
                            "{{ __($testimonial['content']) }}"
                        </blockquote>

                        <!-- Author Info -->
                        <div class="flex items-center">
                            <div class="relative">
                                <img class="w-14 h-14 rounded-full object-cover mr-4 ring-4 ring-white shadow-lg group-hover:ring-purple-100 transition-all duration-300"
                                    src="{{ $testimonial['avatar'] }}" alt="{{ $testimonial['name'] }}" loading="lazy">
                                <!-- Verified Badge -->
                                <div
                                    class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <div
                                    class="font-display font-bold text-gray-900 text-lg group-hover:text-purple-700 transition-colors">
                                    {{ $testimonial['name'] }}</div>
                                <div class="text-sm text-gray-600 font-medium">{{ $testimonial['role'] }}</div>
                                <div
                                    class="text-sm font-semibold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent">
                                    {{ $testimonial['company'] }}</div>
                            </div>
                        </div>

                        <!-- Hover Effect Border -->
                        <div
                            class="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-400 to-indigo-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 -z-10">
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Trust Badges -->
        <div class="mt-20 fade-in-up">
            <div class="text-center mb-12">
                <h3 class="font-display text-2xl font-bold text-gray-900 mb-4">
                    {{ __('marketing.testimonials_trusted_title') }}</h3>
                <p class="text-gray-600">{{ __('marketing.testimonials_trusted_subtitle') }}</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div
                        class="modern-card w-20 h-20 flex items-center justify-center mx-auto mb-4 bg-gradient-to-br from-green-400 to-emerald-500 group-hover:scale-110 transition-all duration-300">
                        <svg class="w-10 h-10 text-blue-500 drop-shadow-lg" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                            </path>
                        </svg>
                    </div>
                    <div class="font-display font-bold text-gray-900 group-hover:text-green-600 transition-colors">
                        {{ __('marketing.trust_secure') }}</div>
                    <div class="text-sm text-gray-600 mt-1">{{ __('marketing.trust_secure_desc') }}</div>
                </div>

                <div class="text-center group">
                    <div
                        class="modern-card w-20 h-20 flex items-center justify-center mx-auto mb-4 bg-gradient-to-br from-blue-400 to-indigo-500 group-hover:scale-110 transition-all duration-300">
                        <svg class="w-10 h-10 text-blue-500 drop-shadow-lg" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="font-display font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {{ __('marketing.trust_gdpr') }}</div>
                    <div class="text-sm text-gray-600 mt-1">{{ __('marketing.trust_gdpr_desc') }}</div>
                </div>

                <div class="text-center group">
                    <div
                        class="modern-card w-20 h-20 flex items-center justify-center mx-auto mb-4 bg-gradient-to-br from-purple-400 to-violet-500 group-hover:scale-110 transition-all duration-300">
                        <svg class="w-10 h-10 text-blue-500 drop-shadow-lg" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="font-display font-bold text-gray-900 group-hover:text-purple-600 transition-colors">
                        {{ __('marketing.trust_uptime') }}</div>
                    <div class="text-sm text-gray-600 mt-1">{{ __('marketing.trust_uptime_desc') }}</div>
                </div>

                <div class="text-center group">
                    <div
                        class="modern-card w-20 h-20 flex items-center justify-center mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-orange-500 group-hover:scale-110 transition-all duration-300">
                        <svg class="w-10 h-10 text-blue-500 drop-shadow-lg" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z">
                            </path>
                        </svg>
                    </div>
                    <div class="font-display font-bold text-gray-900 group-hover:text-yellow-600 transition-colors">
                        {{ __('marketing.trust_support') }}</div>
                    <div class="text-sm text-gray-600 mt-1">{{ __('marketing.trust_support_desc') }}</div>
                </div>
            </div>
        </div>
</section>
