<!-- Pricing Section -->
<section id="pricing" class="py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Enhanced Background Design -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-50/60 via-indigo-50/40 to-purple-50/60"></div>

    <!-- Advanced Pattern -->
    <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80"
        xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%234f46e5"
        fill-opacity="0.06"%3E%3Cpath
        d="M40 40c0-11 9-20 20-20s20 9 20 20-9 20-20 20-20-9-20-20zm-20-20c0-11 9-20 20-20s20 9 20 20-9 20-20 20-20-9-20-20z"
        /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>

    <!-- Floating Pricing Elements -->
    <div
        class="absolute top-20 left-10 w-40 h-40 bg-gradient-to-br from-blue-300/20 to-indigo-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse">
    </div>
    <div class="absolute bottom-20 right-10 w-48 h-48 bg-gradient-to-br from-purple-300/20 to-pink-400/20 rounded-full mix-blend-multiply filter blur-3xl animate-pulse"
        style="animation-delay: 2s;"></div>

    <!-- Currency Symbols Background -->
    <div class="absolute top-1/4 left-1/6 text-6xl text-blue-200/30 font-bold animate-pulse">$</div>
    <div class="absolute top-1/3 right-1/6 text-5xl text-purple-200/30 font-bold animate-pulse"
        style="animation-delay: 1s;">€</div>
    <div class="absolute bottom-1/3 left-1/4 text-4xl text-indigo-200/30 font-bold animate-pulse"
        style="animation-delay: 2s;">£</div>

    <!-- Decorative Lines -->
    <svg class="absolute inset-0 w-full h-full opacity-20" viewBox="0 0 1000 800" preserveAspectRatio="none">
        <defs>
            <linearGradient id="pricingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
                <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:0.3" />
                <stop offset="100%" style="stop-color:#6366f1;stop-opacity:0.3" />
            </linearGradient>
        </defs>
        <path d="M0,150 Q500,100 1000,150" stroke="url(#pricingGradient)" stroke-width="2" fill="none"
            class="animate-pulse" />
        <path d="M0,650 Q500,700 1000,650" stroke="url(#pricingGradient)" stroke-width="2" fill="none"
            class="animate-pulse" style="animation-delay: 1s;" />
    </svg>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-20 fade-in-up">
            <h2 class="font-display text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                {{ __('marketing.pricing_title') }}
            </h2>
            <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto mb-12 leading-relaxed">
                {{ __('marketing.pricing_subtitle') }}
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto rounded-full mb-12"></div>

            <!-- Billing Toggle -->
            <div class="flex items-center justify-center mb-8" x-data="{ annual: false }">
                <div class="modern-card p-2 bg-white/80 backdrop-blur-sm">
                    <div class="flex items-center space-x-4">
                        <span class="text-gray-700 font-medium"
                            :class="{ 'text-blue-600 font-bold': !annual }">{{ __('marketing.pricing_monthly') }}</span>
                        <button @click="annual = !annual"
                            class="relative inline-flex h-8 w-14 items-center rounded-full bg-gradient-to-r from-gray-200 to-gray-300 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-inner"
                            :class="{ 'from-blue-500 to-purple-600 shadow-lg': annual }">
                            <span
                                class="inline-block h-6 w-6 transform rounded-full bg-white transition-all duration-300 shadow-lg"
                                :class="{ 'translate-x-7 shadow-xl': annual, 'translate-x-1': !annual }"></span>
                        </button>
                        <span class="text-gray-700 font-medium"
                            :class="{ 'text-blue-600 font-bold': annual }">{{ __('marketing.pricing_annual') }}</span>
                        <span
                            class="bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-bold px-3 py-1.5 rounded-full shadow-lg">
                            {{ __('marketing.pricing_save') }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            @foreach ($plans as $index => $plan)
                <div class="fade-in-up stagger-animation relative group" style="--stagger: {{ $index }}">
                    <div
                        class="modern-card h-full p-8 relative overflow-hidden transition-all duration-500 {{ $index === 1 ? 'bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-500 shadow-2xl scale-105' : 'hover:bg-gradient-to-br hover:from-white hover:to-gray-50' }}">
                        @if ($index === 1)
                            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                                <span
                                    class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                                    {{ __('marketing.pricing_most_popular') }}
                                </span>
                            </div>
                            <!-- Glow effect for popular plan -->
                            <div
                                class="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 opacity-10 rounded-2xl">
                            </div>
                        @endif

                        <!-- Plan Icon -->
                        <div class="text-center mb-6">
                            <div
                                class="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br {{ $index === 0 ? 'from-gray-400 to-gray-600' : ($index === 1 ? 'from-blue-500 to-purple-600' : 'from-indigo-500 to-purple-700') }} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                @if ($index === 0)
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                @elseif ($index === 1)
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z">
                                        </path>
                                    </svg>
                                @else
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z">
                                        </path>
                                    </svg>
                                @endif
                            </div>
                            <h3
                                class="font-display text-2xl font-bold text-gray-900 mb-2 group-hover:text-blue-700 transition-colors">
                                {{ $plan->name }}</h3>
                        </div>

                        <div class="text-center mb-8" x-data="{ annual: false }">
                            <div x-show="!annual" class="transition-all duration-300">
                                <span
                                    class="font-display text-5xl font-bold gradient-text">${{ $plan->monthly_price }}</span>
                                <span
                                    class="text-gray-600 text-lg font-medium">{{ __('marketing.pricing_per_month') }}</span>
                                <div class="text-sm text-gray-500 mt-2 font-medium">
                                    {{ __('marketing.pricing_billed_monthly') }}
                                </div>
                            </div>
                            <div x-show="annual" style="display: none;" class="transition-all duration-300">
                                <span
                                    class="font-display text-5xl font-bold gradient-text">${{ number_format($plan->monthly_price * 12 * 0.8, 0) }}</span>
                                <span class="text-gray-600 text-lg font-medium">/year</span>
                                <div class="text-sm text-gray-500 mt-2 font-medium">
                                    {{ __('marketing.pricing_billed_annually') }}
                                </div>
                                <div class="text-xs text-green-600 font-bold mt-1">Save 20%!</div>
                            </div>
                        </div>

                        <div class="space-y-6 mb-10">
                            @if ($plan->monthly_generations === -1)
                                <div class="flex items-center group">
                                    <div
                                        class="w-6 h-6 mx-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span
                                        class="text-gray-700 font-medium group-hover:text-gray-900 transition-colors">{{ __('marketing.pricing_unlimited') }}</span>
                                </div>
                            @else
                                <div class="flex items-center group">
                                    <div
                                        class="w-6 h-6 mx-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd"
                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span
                                        class="text-gray-700 font-medium group-hover:text-gray-900 transition-colors">{{ number_format($plan->monthly_generations) }}
                                        {{ __('marketing.pricing_generations') }}</span>
                                </div>
                            @endif

                            <div class="flex items-center group">
                                <div
                                    class="w-6 h-6 mx-4 bg-gradient-to-r from-blue-400 to-indigo-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span
                                    class="text-gray-700 font-medium group-hover:text-gray-900 transition-colors">{{ __('marketing.pricing_all_features') }}</span>
                            </div>

                            <div class="flex items-center group">
                                <div
                                    class="w-6 h-6 mx-4 bg-gradient-to-r from-purple-400 to-violet-500 rounded-full flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                                    <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 font-medium group-hover:text-gray-900 transition-colors">
                                    @if ($plan->monthly_price > 0)
                                        {{ __('marketing.pricing_priority_support') }}
                                    @else
                                        {{ __('marketing.pricing_support') }}
                                    @endif
                                </span>
                            </div>
                        </div>

                        @guest
                            @if ($plan->monthly_price > 0)
                                <form action="{{ route('pricing.checkout', $plan) }}" method="POST">
                                    @csrf
                                    <button type="submit" data-track="cta"
                                        class="modern-btn w-full {{ $index === 1 ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-xl' : 'bg-gradient-to-r from-gray-700 to-gray-900 hover:from-gray-800 hover:to-black shadow-lg' }} text-white font-bold py-4 px-6 text-lg group">
                                        <span class="flex items-center justify-center">
                                            {{ __('marketing.pricing_get_started') }}
                                            <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"
                                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                            </svg>
                                        </span>
                                    </button>
                                </form>
                            @else
                                <a href="{{ route('register') }}" data-track="cta"
                                    class="modern-btn w-full inline-block bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-6 text-lg text-center shadow-xl group">
                                    <span class="flex items-center justify-center">
                                        {{ __('marketing.pricing_start_free') }}
                                        <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"
                                            fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                        </svg>
                                    </span>
                                </a>
                            @endif
                        @else
                            <a href="{{ route('dashboard') }}"
                                class="modern-btn w-full inline-block bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-6 text-lg text-center shadow-xl group">
                                <span class="flex items-center justify-center">
                                    {{ __('messages.dashboard') }}
                                    <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform"
                                        fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                    </svg>
                                </span>
                            </a>
                        @endguest

                        <!-- Hover Effect Border -->
                        <div
                            class="absolute inset-0 rounded-2xl bg-gradient-to-r {{ $index === 1 ? 'from-blue-400 to-purple-400' : 'from-gray-400 to-gray-600' }} opacity-0 group-hover:opacity-20 transition-opacity duration-300 -z-10">
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="text-center mt-16 fade-in-up">
            <div class="modern-card inline-block px-8 py-4 bg-white/80 backdrop-blur-sm">
                <p class="text-gray-700 font-medium flex items-center">
                    <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clip-rule="evenodd"></path>
                    </svg>
                    {{ __('marketing.cta_no_credit_card') }}
                </p>
            </div>
        </div>
    </div>
</section>
