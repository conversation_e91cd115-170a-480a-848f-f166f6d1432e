<x-guest-layout>
    @php
        $pageSlug = 'contact-us';
        $page = \App\Models\Page::where('slug', $pageSlug)->first();
        $translation = $page?->translation(app()->getLocale());
    @endphp

    <x-slot name="head">
        @include('partials.seo-meta', [
            'page' => $page,
            'translation' => $translation,
            'schemaType' => 'contactpage',
            'ogType' => 'website',
        ])
    </x-slot>

    <!-- Hero Section -->
    <div class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 py-20">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <!-- Geometric Shapes -->
            <div class="absolute top-16 left-16 w-24 h-24 bg-white/10 rounded-full animate-pulse"></div>
            <div class="absolute top-20 right-32 w-18 h-18 bg-yellow-300/20 rounded-lg rotate-45 animate-bounce"></div>
            <div class="absolute bottom-24 left-1/3 w-14 h-14 bg-pink-300/20 rounded-full animate-ping"></div>
            <div class="absolute bottom-16 right-1/4 w-20 h-20 bg-blue-300/10 rounded-lg rotate-12 animate-pulse"></div>

            <!-- Communication Icons -->
            <div class="absolute top-1/3 left-1/4 w-8 h-8 bg-white/20 rounded-lg animate-float">
                <svg class="w-full h-full p-1 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
            </div>
            <div class="absolute top-1/2 right-1/3 w-6 h-6 bg-white/20 rounded-lg animate-float-delayed">
                <svg class="w-full h-full p-1 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
            </div>

            <!-- Wave Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0"
                    style="background-image: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,255,255,0.1) 10px, rgba(255,255,255,0.1) 20px);">
                </div>
            </div>

            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
                    {{ __('messages.contact') }}
                </h1>
                <p class="text-xl md:text-2xl text-emerald-100 mb-8 max-w-3xl mx-auto drop-shadow-md">
                    Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
                </p>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <div class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Contact Form -->
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Send us a message</h2>

                    <form class="space-y-6" action="#" method="POST">
                        @csrf
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('messages.name') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="name" id="name" required
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                placeholder="Your full name">
                        </div>

                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                {{ __('messages.email') }} <span class="text-red-500">*</span>
                            </label>
                            <input type="email" name="email" id="email" required
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                placeholder="<EMAIL>">
                        </div>

                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                                Subject <span class="text-red-500">*</span>
                            </label>
                            <select name="subject" id="subject" required
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="support">Technical Support</option>
                                <option value="billing">Billing Question</option>
                                <option value="feature">Feature Request</option>
                                <option value="partnership">Partnership</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                                Message <span class="text-red-500">*</span>
                            </label>
                            <textarea name="message" id="message" rows="6" required
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                                placeholder="Tell us how we can help you..."></textarea>
                        </div>

                        <div>
                            <button type="submit"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                                Send Message
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Contact Information -->
                <div>
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Get in touch</h2>

                    <div class="space-y-8">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="bg-blue-100 rounded-lg p-3">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">Email</h3>
                                <p class="text-gray-600"><EMAIL></p>
                                <p class="text-sm text-gray-500 mt-1">We'll respond within 24 hours</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="bg-green-100 rounded-lg p-3">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z">
                                        </path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">Live Chat</h3>
                                <p class="text-gray-600">Available 24/7</p>
                                <p class="text-sm text-gray-500 mt-1">Click the chat icon in the bottom right</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="bg-purple-100 rounded-lg p-3">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                        </path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-medium text-gray-900">Help Center</h3>
                                <p class="text-gray-600">Find answers to common questions</p>
                                <a href="{{ route('faq') }}"
                                    class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                                    Visit FAQ →
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Response Time -->
                    <div class="mt-8 bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Response Times</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">General Inquiries</span>
                                <span class="font-medium text-gray-900">24 hours</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Technical Support</span>
                                <span class="font-medium text-gray-900">12 hours</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Billing Issues</span>
                                <span class="font-medium text-gray-900">6 hours</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Urgent Issues</span>
                                <span class="font-medium text-gray-900">2 hours</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="py-16 bg-gray-50">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                    Frequently Asked Questions
                </h2>
                <p class="text-xl text-gray-600">
                    Quick answers to common questions
                </p>
            </div>

            <div class="space-y-6">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                        How quickly will I receive a response?
                    </h3>
                    <p class="text-gray-600">
                        We aim to respond to all inquiries within 24 hours. For technical support and billing issues, we
                        typically respond much faster - often within a few hours.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                        Do you offer phone support?
                    </h3>
                    <p class="text-gray-600">
                        Currently, we provide support through email and live chat. Our team is available 24/7 through
                        these channels to assist you with any questions or issues.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                        Can I schedule a demo or consultation?
                    </h3>
                    <p class="text-gray-600">
                        Yes! For enterprise customers or those interested in custom solutions, we offer personalized
                        demos and consultations. Please mention this in your message and we'll arrange a call.
                    </p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                        What information should I include in my support request?
                    </h3>
                    <p class="text-gray-600">
                        Please include as much detail as possible: your account email, the specific issue you're
                        experiencing, steps you've already tried, and any error messages you've seen. This helps us
                        resolve your issue faster.
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-guest-layout>
