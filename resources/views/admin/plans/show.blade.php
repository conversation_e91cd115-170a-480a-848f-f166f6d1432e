@extends('layouts.admin')

@section('title', 'View Plan')

@section('content')
<div class="p-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Plan Details</h1>
            <p class="text-gray-600">View plan information and subscription statistics</p>
        </div>
        <div class="flex gap-2">
            <a href="{{ route('admin.plans.edit', $plan) }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-edit mr-2"></i>Edit Plan
            </a>
            <a href="{{ route('admin.plans.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>Back to Plans
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- Plan Information -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="border-b pb-4 mb-6">
                    <div class="flex items-start justify-between">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900 flex items-center gap-2">
                                {{ $plan->name }}
                                @if($plan->is_featured)
                                    <span class="px-2 py-1 text-xs font-semibold text-white bg-blue-500 rounded">Featured</span>
                                @endif
                                @if($plan->is_active)
                                    <span class="px-2 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded">Active</span>
                                @else
                                    <span class="px-2 py-1 text-xs font-semibold text-red-800 bg-red-100 rounded">Inactive</span>
                                @endif
                            </h2>
                            <p class="text-gray-600 mt-1">{{ $plan->slug }}</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-gray-900">
                                ${{ number_format($plan->monthly_price / 100, 2) }}<span class="text-sm font-normal text-gray-500">/month</span>
                            </div>
                            <div class="text-lg text-gray-600">
                                ${{ number_format($plan->annual_price / 100, 2) }}<span class="text-sm text-gray-500">/year</span>
                            </div>
                            @php
                                $monthlyTotal = ($plan->monthly_price / 100) * 12;
                                $annualPrice = $plan->annual_price / 100;
                                $savings = $monthlyTotal - $annualPrice;
                                $savingsPercent = $monthlyTotal > 0 ? ($savings / $monthlyTotal * 100) : 0;
                            @endphp
                            @if($savings > 0)
                                <div class="text-sm text-green-600 font-medium">
                                    Save ${{ number_format($savings, 2) }} ({{ number_format($savingsPercent, 1) }}%) annually
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Description -->
                @if($plan->description)
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">Description</h3>
                    <p class="text-gray-900">{{ $plan->description }}</p>
                </div>
                @endif

                <!-- Generation Limit -->
                <div class="mb-6">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">Generation Limit</h3>
                    <p class="text-gray-900">
                        @if($plan->monthly_generations_limit == -1)
                            <span class="text-green-600 font-medium">Unlimited</span> generations per month
                        @else
                            <span class="font-medium">{{ number_format($plan->monthly_generations_limit) }}</span> generations per month
                        @endif
                    </p>
                </div>

                <!-- Features -->
                @if($plan->features && count($plan->features) > 0)
                <div>
                    <h3 class="text-sm font-medium text-gray-500 mb-3">Features</h3>
                    <ul class="space-y-2">
                        @foreach($plan->features as $feature)
                            <li class="flex items-center text-gray-900">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                {{ $feature }}
                            </li>
                        @endforeach
                    </ul>
                </div>
                @endif
            </div>

            <!-- Recent Subscribers -->
            @if($recentSubscribers && $recentSubscribers->count() > 0)
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Subscribers</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    User
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Subscribed
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($recentSubscribers as $subscription)
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700">
                                                    {{ substr($subscription->user->name, 0, 1) }}
                                                </span>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">{{ $subscription->user->name }}</p>
                                                <p class="text-sm text-gray-500">{{ $subscription->user->email }}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        @php
                                            $statusColors = [
                                                'active' => 'bg-green-100 text-green-800',
                                                'past_due' => 'bg-yellow-100 text-yellow-800',
                                                'canceled' => 'bg-red-100 text-red-800',
                                                'unpaid' => 'bg-red-100 text-red-800',
                                            ];
                                        @endphp
                                        <span class="px-2 py-1 text-xs font-semibold rounded {{ $statusColors[$subscription->stripe_status] ?? 'bg-gray-100 text-gray-800' }}">
                                            {{ ucfirst($subscription->stripe_status) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $subscription->created_at->format('M j, Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <a href="{{ route('admin.users.show', $subscription->user) }}" 
                                           class="text-blue-600 hover:text-blue-900">View User</a>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            <!-- Subscription Statistics -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription Statistics</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Active Subscriptions</span>
                        <span class="text-lg font-semibold text-green-600">{{ $subscriptionStats['active'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Past Due</span>
                        <span class="text-lg font-semibold text-yellow-600">{{ $subscriptionStats['past_due'] }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Canceled</span>
                        <span class="text-lg font-semibold text-red-600">{{ $subscriptionStats['canceled'] }}</span>
                    </div>
                    <hr class="my-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-gray-700">Total Subscribers</span>
                        <span class="text-xl font-bold text-gray-900">
                            {{ $subscriptionStats['active'] + $subscriptionStats['past_due'] + $subscriptionStats['canceled'] }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Revenue Information -->
            @php
                $monthlyRevenue = $subscriptionStats['active'] * ($plan->monthly_price / 100);
                $annualRevenue = $monthlyRevenue * 12;
            @endphp
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Revenue Projection</h3>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Monthly Revenue</span>
                        <span class="text-lg font-semibold text-green-600">${{ number_format($monthlyRevenue, 2) }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Annual Projection</span>
                        <span class="text-lg font-semibold text-green-600">${{ number_format($annualRevenue, 2) }}</span>
                    </div>
                </div>
                <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                    <p class="text-xs text-blue-700">
                        <i class="fas fa-info-circle mr-1"></i>
                        Based on current active subscriptions
                    </p>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <!-- Toggle Status -->
                    <form action="{{ route('admin.plans.toggle-active', $plan) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" 
                                class="w-full px-4 py-2 text-sm rounded transition duration-200 {{ $plan->is_active ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' : 'bg-green-100 text-green-800 hover:bg-green-200' }}">
                            <i class="fas fa-toggle-{{ $plan->is_active ? 'off' : 'on' }} mr-2"></i>
                            {{ $plan->is_active ? 'Deactivate Plan' : 'Activate Plan' }}
                        </button>
                    </form>

                    <!-- Toggle Featured -->
                    <form action="{{ route('admin.plans.toggle-featured', $plan) }}" method="POST" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" 
                                class="w-full px-4 py-2 text-sm rounded transition duration-200 {{ $plan->is_featured ? 'bg-blue-100 text-blue-800 hover:bg-blue-200' : 'bg-gray-100 text-gray-800 hover:bg-gray-200' }}">
                            <i class="fas fa-star mr-2"></i>
                            {{ $plan->is_featured ? 'Unfeature Plan' : 'Feature Plan' }}
                        </button>
                    </form>
                    
                    <a href="{{ route('admin.plans.edit', $plan) }}" 
                       class="w-full bg-blue-100 text-blue-800 hover:bg-blue-200 px-4 py-2 rounded text-sm transition duration-200 text-center block">
                        <i class="fas fa-edit mr-2"></i>Edit Plan
                    </a>
                    
                    @if($subscriptionStats['active'] == 0)
                    <form action="{{ route('admin.plans.destroy', $plan) }}" 
                          method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this plan? This action cannot be undone.')"
                          class="w-full">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full bg-red-100 text-red-800 hover:bg-red-200 px-4 py-2 rounded text-sm transition duration-200">
                            <i class="fas fa-trash mr-2"></i>Delete Plan
                        </button>
                    </form>
                    @else
                    <div class="w-full bg-gray-100 text-gray-500 px-4 py-2 rounded text-sm text-center cursor-not-allowed">
                        <i class="fas fa-lock mr-2"></i>Cannot Delete (Has Active Subscriptions)
                    </div>
                    @endif
                </div>
            </div>

            <!-- Plan Metadata -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Plan Metadata</h3>
                <div class="space-y-3 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Created:</span>
                        <span class="text-gray-900">{{ $plan->created_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Last Updated:</span>
                        <span class="text-gray-900">{{ $plan->updated_at->format('M j, Y') }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Plan ID:</span>
                        <span class="text-gray-900 font-mono">#{{ $plan->id }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
