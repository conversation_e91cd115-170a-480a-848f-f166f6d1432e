@extends('layouts.admin')

@section('title', 'Plans Management')

@section('content')
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold text-gray-900">Plans Management</h1>
            <a href="{{ route('admin.plans.create') }}"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>Add New Plan
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-list text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Plans</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['total'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Plans</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['active'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-star text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Featured Plans</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['featured'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-users text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Subscribers</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['total_subscribers'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-green-100">Monthly Revenue</p>
                        <p class="text-3xl font-bold">${{ number_format($revenue['monthly'] / 100, 2) }}</p>
                    </div>
                    <div class="text-green-200">
                        <i class="fas fa-dollar-sign text-4xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-blue-100">Annual Revenue (Projected)</p>
                        <p class="text-3xl font-bold">${{ number_format($revenue['annual'] / 100, 2) }}</p>
                    </div>
                    <div class="text-blue-200">
                        <i class="fas fa-chart-line text-4xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Actions -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6 border-b">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <div class="flex flex-wrap items-center gap-4">
                        <!-- Active Filter -->
                        <div>
                            <label for="active-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select id="active-filter" class="form-select border-gray-300 rounded-md shadow-sm">
                                <option value="">All Plans</option>
                                <option value="1">Active Only</option>
                                <option value="0">Inactive Only</option>
                            </select>
                        </div>

                        <!-- Featured Filter -->
                        <div>
                            <label for="featured-filter"
                                class="block text-sm font-medium text-gray-700 mb-1">Featured</label>
                            <select id="featured-filter" class="form-select border-gray-300 rounded-md shadow-sm">
                                <option value="">All Plans</option>
                                <option value="1">Featured Only</option>
                                <option value="0">Not Featured</option>
                            </select>
                        </div>
                    </div>

                    <!-- Export Button -->
                    <div class="flex items-center gap-2">
                        <button onclick="exportRevenue()"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-200">
                            <i class="fas fa-download mr-2"></i>Export Revenue
                        </button>
                    </div>
                </div>
            </div>

            <!-- DataTable -->
            <div class="overflow-x-auto">
                <table id="plans-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Monthly Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Annual Price</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Generations</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Subscribers</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Monthly Revenue</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- DataTable will populate this -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                let table = $('#plans-table').DataTable({
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('admin.plans.index') }}",
                        data: function(d) {
                            d.is_active = $('#active-filter').val();
                            d.is_featured = $('#featured-filter').val();
                        }
                    },
                    columns: [{
                            data: 'name_with_status',
                            name: 'name'
                        },
                        {
                            data: 'formatted_monthly_price',
                            name: 'monthly_price'
                        },
                        {
                            data: 'formatted_annual_price',
                            name: 'annual_price'
                        },
                        {
                            data: 'generations_limit',
                            name: 'monthly_generations_limit'
                        },
                        {
                            data: 'subscriptions_count',
                            name: 'subscriptions_count'
                        },
                        {
                            data: 'formatted_monthly_revenue',
                            name: 'monthly_revenue',
                            orderable: false
                        },
                        {
                            data: 'status_badges',
                            name: 'status',
                            orderable: false,
                            searchable: false
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false,
                            width: '150px'
                        }
                    ],
                    order: [
                        [1, 'asc']
                    ],
                    pageLength: 25,
                    responsive: true,
                    dom: 'rtip',
                    language: {
                        processing: 'Loading...',
                        emptyTable: 'No plans found',
                        info: 'Showing _START_ to _END_ of _TOTAL_ plans',
                        infoEmpty: 'Showing 0 to 0 of 0 plans',
                        infoFiltered: '(filtered from _MAX_ total plans)'
                    }
                });

                // Filter functionality
                $('#active-filter, #featured-filter').on('change', function() {
                    table.draw();
                });

                // Toggle functionality
                $(document).on('click', '.toggle-status', function(e) {
                    e.preventDefault();

                    let url = $(this).data('url');

                    $.ajax({
                        url: url,
                        method: 'PATCH',
                        data: {
                            _token: "{{ csrf_token() }}"
                        },
                        success: function(response) {
                            if (response.success) {
                                table.draw(false);
                            }
                        },
                        error: function(xhr) {
                            alert('Error: ' + (xhr.responseJSON?.message ||
                            'Something went wrong'));
                        }
                    });
                });

                // Delete functionality
                $(document).on('click', '.delete-item', function(e) {
                    e.preventDefault();

                    if (confirm('Are you sure you want to delete this plan? This action cannot be undone.')) {
                        let form = $(this).closest('form');
                        form.submit();
                    }
                });
            });

            function exportRevenue() {
                window.location.href = '{{ route('admin.plans.index') }}?export=revenue';
            }
        </script>
    @endpush
@endsection
