@extends('layouts.admin')

@section('title', 'Create Plan')

@section('content')
<div class="p-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Create New Plan</h1>
            <p class="text-gray-600">Add a new subscription plan to your pricing</p>
        </div>
        <a href="{{ route('admin.plans.index') }}" 
           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200">
            <i class="fas fa-arrow-left mr-2"></i>Back to Plans
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-lg">
        <form action="{{ route('admin.plans.store') }}" method="POST" class="p-6">
            @csrf
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Plan Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Plan Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}" 
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('name') border-red-500 @enderror"
                                   placeholder="e.g., Premium Plan">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Slug -->
                        <div>
                            <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                                Slug <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="slug" 
                                   name="slug" 
                                   value="{{ old('slug') }}" 
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('slug') border-red-500 @enderror"
                                   placeholder="premium-plan">
                            @error('slug')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">URL-friendly version (lowercase, hyphens)</p>
                        </div>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description
                        </label>
                        <textarea id="description" 
                                  name="description" 
                                  rows="3" 
                                  class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('description') border-red-500 @enderror"
                                  placeholder="Brief description of what this plan offers">{{ old('description') }}</textarea>
                        @error('description')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Pricing -->
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Pricing</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Monthly Price -->
                            <div>
                                <label for="monthly_price" class="block text-sm font-medium text-gray-700 mb-2">
                                    Monthly Price (USD) <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-2 text-gray-500">$</span>
                                    <input type="number" 
                                           id="monthly_price" 
                                           name="monthly_price" 
                                           value="{{ old('monthly_price') }}" 
                                           step="0.01"
                                           min="0"
                                           class="w-full border border-gray-300 rounded-lg pl-8 pr-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('monthly_price') border-red-500 @enderror"
                                           placeholder="9.99">
                                </div>
                                @error('monthly_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Annual Price -->
                            <div>
                                <label for="annual_price" class="block text-sm font-medium text-gray-700 mb-2">
                                    Annual Price (USD) <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-2 text-gray-500">$</span>
                                    <input type="number" 
                                           id="annual_price" 
                                           name="annual_price" 
                                           value="{{ old('annual_price') }}" 
                                           step="0.01"
                                           min="0"
                                           class="w-full border border-gray-300 rounded-lg pl-8 pr-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('annual_price') border-red-500 @enderror"
                                           placeholder="99.99">
                                </div>
                                @error('annual_price')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">Annual pricing (usually discounted)</p>
                            </div>
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Features</h3>
                        
                        <!-- Generations Limit -->
                        <div class="mb-4">
                            <label for="monthly_generations_limit" class="block text-sm font-medium text-gray-700 mb-2">
                                Monthly Generations Limit <span class="text-red-500">*</span>
                            </label>
                            <input type="number" 
                                   id="monthly_generations_limit" 
                                   name="monthly_generations_limit" 
                                   value="{{ old('monthly_generations_limit') }}" 
                                   min="-1"
                                   class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('monthly_generations_limit') border-red-500 @enderror"
                                   placeholder="100">
                            @error('monthly_generations_limit')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-1 text-sm text-gray-500">Use -1 for unlimited generations</p>
                        </div>

                        <!-- Feature List -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Additional Features</label>
                            <div id="features-container" class="space-y-2">
                                <div class="flex items-center gap-2">
                                    <input type="text" 
                                           name="features[]" 
                                           class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                           placeholder="e.g., Priority support">
                                    <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" onclick="addFeature()" class="mt-2 text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-plus mr-1"></i>Add Feature
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Plan Settings -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Plan Settings</h3>
                        
                        <!-- Status -->
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" 
                                       name="is_active" 
                                       value="1" 
                                       {{ old('is_active', true) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Active Plan</span>
                            </label>
                            <p class="mt-1 text-sm text-gray-500">Inactive plans won't be shown to customers</p>
                        </div>

                        <!-- Featured -->
                        <div class="mb-4">
                            <label class="flex items-center">
                                <input type="checkbox" 
                                       name="is_featured" 
                                       value="1" 
                                       {{ old('is_featured') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <span class="ml-2 text-sm text-gray-700">Featured Plan</span>
                            </label>
                            <p class="mt-1 text-sm text-gray-500">Featured plans are highlighted to customers</p>
                        </div>

                        <!-- Actions -->
                        <div class="flex flex-col gap-2 pt-4 border-t">
                            <button type="submit" 
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                                <i class="fas fa-save mr-2"></i>Create Plan
                            </button>
                            
                            <a href="{{ route('admin.plans.index') }}" 
                               class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200 text-center">
                                <i class="fas fa-times mr-2"></i>Cancel
                            </a>
                        </div>
                    </div>

                    <!-- Help -->
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-900 mb-2">
                            <i class="fas fa-info-circle mr-1"></i>Help
                        </h4>
                        <ul class="text-sm text-blue-700 space-y-1">
                            <li>• Plan names should be descriptive and unique</li>
                            <li>• Slugs are used in URLs and must be unique</li>
                            <li>• Annual pricing is usually discounted</li>
                            <li>• Use -1 for unlimited generations</li>
                            <li>• Featured plans get special highlighting</li>
                        </ul>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Auto-generate slug from name
    $('#name').on('input', function() {
        let name = $(this).val();
        let slug = name.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single
            .trim('-'); // Remove leading/trailing hyphens
        
        $('#slug').val(slug);
    });

    // Auto-calculate annual price suggestion
    $('#monthly_price').on('input', function() {
        let monthlyPrice = parseFloat($(this).val()) || 0;
        let suggestedAnnual = (monthlyPrice * 12 * 0.83).toFixed(2); // 17% discount
        
        if (monthlyPrice > 0 && $('#annual_price').val() === '') {
            $('#annual_price').val(suggestedAnnual);
        }
    });
});

function addFeature() {
    const container = document.getElementById('features-container');
    const div = document.createElement('div');
    div.className = 'flex items-center gap-2';
    div.innerHTML = `
        <input type="text" 
               name="features[]" 
               class="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
               placeholder="Feature description">
        <button type="button" onclick="removeFeature(this)" class="text-red-600 hover:text-red-800">
            <i class="fas fa-times"></i>
        </button>
    `;
    container.appendChild(div);
}

function removeFeature(button) {
    button.closest('div').remove();
}
</script>
@endpush
@endsection
