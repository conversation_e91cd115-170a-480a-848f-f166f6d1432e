@extends('layouts.admin')

@section('title', 'Edit Plan')

@section('content')
    <div class="p-6">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900">Edit Plan</h1>
                <p class="text-gray-600">Update plan details and features</p>
            </div>
            <div class="flex gap-2">
                <a href="{{ route('admin.plans.show', $plan) }}"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-eye mr-2"></i>View Plan
                </a>
                <a href="{{ route('admin.plans.index') }}"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Plans
                </a>
            </div>
        </div>

        <form action="{{ route('admin.plans.update', $plan) }}" method="POST" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-lg font-semibold mb-4">Basic Information</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Plan Name *</label>
                        <input type="text" id="name" name="name" value="{{ old('name', $plan->name) }}" required
                            placeholder="e.g., Basic Plan"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        @error('name')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                            URL Slug *
                            <span class="text-gray-500 text-xs">(auto-generated if empty)</span>
                        </label>
                        <input type="text" id="slug" name="slug" value="{{ old('slug', $plan->slug) }}" required
                            placeholder="e.g., basic-plan"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        @error('slug')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea id="description" name="description" rows="3" placeholder="Brief description of this plan..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">{{ old('description', $plan->description) }}</textarea>
                        @error('description')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Pricing -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-lg font-semibold mb-4">Pricing</h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label for="monthly_price" class="block text-sm font-medium text-gray-700 mb-2">Monthly Price ($)
                            *</label>
                        <input type="number" id="monthly_price" name="monthly_price"
                            value="{{ old('monthly_price', $plan->monthly_price / 100) }}" required min="0"
                            step="0.01" placeholder="9.99"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        @error('monthly_price')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="annual_price" class="block text-sm font-medium text-gray-700 mb-2">Annual Price ($)
                            *</label>
                        <input type="number" id="annual_price" name="annual_price"
                            value="{{ old('annual_price', $plan->annual_price / 100) }}" required min="0"
                            step="0.01" placeholder="99.99"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        @error('annual_price')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="monthly_generations_limit" class="block text-sm font-medium text-gray-700 mb-2">
                            Monthly Generation Limit *
                            <span class="text-gray-500 text-xs">(-1 for unlimited)</span>
                        </label>
                        <input type="number" id="monthly_generations_limit" name="monthly_generations_limit"
                            value="{{ old('monthly_generations_limit', $plan->monthly_generations_limit) }}" required
                            min="-1" placeholder="100"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        @error('monthly_generations_limit')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Pricing Preview -->
                <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-600">
                        <span class="font-medium">Annual Savings:</span>
                        <span id="savings-amount">$0.00</span>
                        (<span id="savings-percent">0%</span>)
                    </p>
                </div>
            </div>

            <!-- Features -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold">Features</h2>
                    <button type="button" onclick="addFeature()"
                        class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition duration-200">
                        <i class="fas fa-plus mr-1"></i>Add Feature
                    </button>
                </div>

                <div id="features-container" class="space-y-3">
                    @if (old('features', $plan->features) && count(old('features', $plan->features)) > 0)
                        @foreach (old('features', $plan->features) as $index => $feature)
                            <div class="feature-item flex items-center gap-3">
                                <input type="text" name="features[]" value="{{ $feature }}"
                                    placeholder="Feature description"
                                    class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="button" onclick="removeFeature(this)"
                                    class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg transition duration-200">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        @endforeach
                    @else
                        <div class="feature-item flex items-center gap-3">
                            <input type="text" name="features[]" placeholder="Feature description"
                                class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <button type="button" onclick="removeFeature(this)"
                                class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg transition duration-200">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    @endif
                </div>

                @error('features')
                    <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                @enderror
            </div>

            <!-- Plan Settings -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-lg font-semibold mb-4">Plan Settings</h2>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <input type="checkbox" id="is_featured" name="is_featured" value="1"
                            {{ old('is_featured', $plan->is_featured) ? 'checked' : '' }}
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_featured" class="ml-2 block text-sm text-gray-900">
                            Featured Plan
                            <span class="text-gray-500 text-xs block">Highlight this plan (recommended badge, etc.)</span>
                        </label>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="is_active" name="is_active" value="1"
                            {{ old('is_active', $plan->is_active) ? 'checked' : '' }}
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="is_active" class="ml-2 block text-sm text-gray-900">
                            Active Plan
                            <span class="text-gray-500 text-xs block">Make this plan available for subscription</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end gap-3">
                <a href="{{ route('admin.plans.index') }}"
                    class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition duration-200">
                    Cancel
                </a>
                <button type="submit"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-save mr-2"></i>Update Plan
                </button>
            </div>
        </form>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Auto-generate slug from name
                const nameInput = document.getElementById('name');
                const slugInput = document.getElementById('slug');

                nameInput.addEventListener('input', function() {
                    const slug = this.value
                        .toLowerCase()
                        .replace(/[^a-z0-9 -]/g, '')
                        .replace(/\s+/g, '-')
                        .replace(/-+/g, '-')
                        .trim('-');
                    if (!slugInput.value || slugInput.value === nameInput.dataset.originalSlug) {
                        slugInput.value = slug;
                    }
                });

                // Store original slug to check if user manually changed it
                nameInput.dataset.originalSlug = slugInput.value;

                // Calculate savings
                function calculateSavings() {
                    const monthlyPrice = parseFloat(document.getElementById('monthly_price').value) || 0;
                    const annualPrice = parseFloat(document.getElementById('annual_price').value) || 0;

                    const monthlyTotal = monthlyPrice * 12;
                    const savings = monthlyTotal - annualPrice;
                    const savingsPercent = monthlyTotal > 0 ? (savings / monthlyTotal * 100) : 0;

                    document.getElementById('savings-amount').textContent = '$' + savings.toFixed(2);
                    document.getElementById('savings-percent').textContent = savingsPercent.toFixed(1) + '%';
                }

                document.getElementById('monthly_price').addEventListener('input', calculateSavings);
                document.getElementById('annual_price').addEventListener('input', calculateSavings);

                // Initial calculation
                calculateSavings();
            });

            function addFeature() {
                const container = document.getElementById('features-container');
                const featureItem = document.createElement('div');
                featureItem.className = 'feature-item flex items-center gap-3';
                featureItem.innerHTML = `
        <input type="text" 
               name="features[]" 
               placeholder="Feature description"
               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        <button type="button" 
                onclick="removeFeature(this)" 
                class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg transition duration-200">
            <i class="fas fa-trash"></i>
        </button>
    `;
                container.appendChild(featureItem);
            }

            function removeFeature(button) {
                const container = document.getElementById('features-container');
                if (container.children.length > 1) {
                    button.closest('.feature-item').remove();
                }
            }
        </script>
    @endpush
@endsection
