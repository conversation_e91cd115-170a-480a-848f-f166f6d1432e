@extends('layouts.admin')

@section('title', 'Admin')

@section('content')

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('admin.content-types.update', $contentType) }}">
                        @csrf
                        @method('PUT')

                        <!-- Name -->
                        <div class="mb-6">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Content Type Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name', $contentType->name) }}"
                                   class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('name') border-red-500 @enderror"
                                   placeholder="e.g., Social Media Post"
                                   required>
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-6">
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                Description
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      rows="3"
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('description') border-red-500 @enderror"
                                      placeholder="Brief description of what this content type generates...">{{ old('description', $contentType->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Category -->
                        <div class="mb-6">
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                Category
                            </label>
                            <select id="category" 
                                    name="category"
                                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('category') border-red-500 @enderror">
                                <option value="">Select a category</option>
                                <option value="Social Media" {{ old('category', $contentType->category) === 'Social Media' ? 'selected' : '' }}>Social Media</option>
                                <option value="Marketing" {{ old('category', $contentType->category) === 'Marketing' ? 'selected' : '' }}>Marketing</option>
                                <option value="Content Creation" {{ old('category', $contentType->category) === 'Content Creation' ? 'selected' : '' }}>Content Creation</option>
                                <option value="SEO" {{ old('category', $contentType->category) === 'SEO' ? 'selected' : '' }}>SEO</option>
                                <option value="E-commerce" {{ old('category', $contentType->category) === 'E-commerce' ? 'selected' : '' }}>E-commerce</option>
                                <option value="Video Content" {{ old('category', $contentType->category) === 'Video Content' ? 'selected' : '' }}>Video Content</option>
                                <option value="Website Content" {{ old('category', $contentType->category) === 'Website Content' ? 'selected' : '' }}>Website Content</option>
                            </select>
                            @error('category')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Prompt Template -->
                        <div class="mb-6">
                            <label for="prompt_template" class="block text-sm font-medium text-gray-700 mb-2">
                                Prompt Template <span class="text-red-500">*</span>
                            </label>
                            <div class="mb-2">
                                <p class="text-sm text-gray-600">
                                    Use placeholders: <code class="bg-gray-100 px-1 rounded">{keywords}</code> for user keywords and 
                                    <code class="bg-gray-100 px-1 rounded">{language}</code> for selected language.
                                </p>
                            </div>
                            <textarea id="prompt_template" 
                                      name="prompt_template" 
                                      rows="6"
                                      class="w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 @error('prompt_template') border-red-500 @enderror"
                                      placeholder="Create an engaging {keywords} in {language}. Make it compelling and include relevant details..."
                                      required>{{ old('prompt_template', $contentType->prompt_template) }}</textarea>
                            @error('prompt_template')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Active Status -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="is_active" 
                                       name="is_active" 
                                       value="1"
                                       {{ old('is_active', $contentType->is_active) ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                                <label for="is_active" class="ml-2 block text-sm text-gray-700">
                                    Active (users can select this content type)
                                </label>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4">
                            <a href="{{ route('admin.content-types.index') }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Content Type
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded-lg border">
                        <div class="text-2xl font-bold text-blue-600">{{ $contentType->generations()->count() }}</div>
                        <div class="text-sm text-gray-600">Total Generations</div>
                    </div>
                    <div class="bg-white p-4 rounded-lg border">
                        <div class="text-2xl font-bold text-green-600">{{ $contentType->generations()->whereMonth('created_at', now()->month)->count() }}</div>
                        <div class="text-sm text-gray-600">This Month</div>
                    </div>
                    <div class="bg-white p-4 rounded-lg border">
                        <div class="text-2xl font-bold text-purple-600">{{ $contentType->generations()->whereDate('created_at', today())->count() }}</div>
                        <div class="text-sm text-gray-600">Today</div>
                    </div>
                </div>
                @if($contentType->generations()->count() > 0)
                    <div class="mt-4 text-sm text-yellow-600">
                        <strong>Note:</strong> This content type has been used for generations and cannot be deleted. You can deactivate it instead.
                    </div>
                @endif
            </div>

            <!-- Help Section -->
            <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-medium text-blue-900 mb-4">Prompt Template Guidelines</h3>
                <div class="space-y-2 text-sm text-blue-800">
                    <p><strong>Available Placeholders:</strong></p>
                    <ul class="list-disc list-inside space-y-1 ml-4">
                        <li><code class="bg-blue-100 px-1 rounded">{keywords}</code> - User-provided keywords or topic</li>
                        <li><code class="bg-blue-100 px-1 rounded">{language}</code> - Selected language for content generation</li>
                    </ul>
                    <p class="mt-4"><strong>Best Practices:</strong></p>
                    <ul class="list-disc list-inside space-y-1 ml-4">
                        <li>Be specific about the desired output format</li>
                        <li>Include context about tone and style</li>
                        <li>Specify any requirements (length, structure, etc.)</li>
                        <li>Use clear, actionable language</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
@endsection
