@extends('layouts.admin')

@section('title', __('admin.content_types'))

@section('breadcrumbs')
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ route('admin.index') }}"
                    class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                    <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                        <path
                            d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z">
                        </path>
                    </svg>
                    {{ __('admin.dashboard') }}
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ __('admin.content_types') }}</span>
                </div>
            </li>
        </ol>
    </nav>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ __('admin.content_type_management') }}</h1>
                <p class="mt-2 text-gray-600">Manage content types for your platform</p>
            </div>
            <div class="flex gap-2">
                <a href="{{ route('admin.content-types.create') }}" class="modern-button inline-flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {{ __('admin.create_content_type') }}
                </a>
                <button id="bulk-delete-btn"
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg hidden">
                    Bulk Delete
                </button>
            </div>
        </div>
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Content Types</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $stats['total'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Active</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $stats['active'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Inactive</p>
                            <p class="text-2xl font-semibold text-gray-900">{{ $stats['inactive'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="h-8 w-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Generations</p>
                            <p class="text-2xl font-semibold text-gray-900">
                                {{ number_format($stats['total_generations']) }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
            <div class="p-6">
                <div class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <label for="status-filter" class="block text-sm font-medium text-gray-700">Status</label>
                        <select id="status-filter"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div class="flex-1 min-w-64">
                        <label for="category-filter" class="block text-sm font-medium text-gray-700">Category</label>
                        <select id="category-filter"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                            <option value="">All Categories</option>
                            @foreach ($categories as $category)
                                <option value="{{ $category }}">{{ $category }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Types DataTable -->
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6">
                <form id="bulk-delete-form" method="POST" action="{{ route('admin.content-types.bulk-destroy') }}"
                    class="mb-4">
                    @csrf
                    <input type="hidden" name="ids" id="bulk-ids">
                </form>

                <table id="content-types-table" class="min-w-full divide-y divide-gray-200 w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th width="20px">
                                <input type="checkbox" id="select-all"
                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Category
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Generations
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
    </div>
    </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                const table = $('#content-types-table').DataTable({
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    ajax: {
                        url: "{{ route('admin.content-types.index') }}",
                        data: function(d) {
                            d.status = $('#status-filter').val();
                            d.category = $('#category-filter').val();
                        }
                    },
                    columns: [{
                            data: null,
                            orderable: false,
                            searchable: false,
                            render: function(data) {
                                return `<input type="checkbox" class="content-type-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" value="${data.id}">`;
                            }
                        },
                        {
                            data: 'name',
                            name: 'name',
                            render: function(data, type, row) {
                                return `
                                <div class="flex items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">${data}</div>
                                        <div class="text-sm text-gray-500">${row.description ? row.description.substring(0, 50) + (row.description.length > 50 ? '...' : '') : ''}</div>
                                    </div>
                                </div>
                            `;
                            }
                        },
                        {
                            data: 'category_badge',
                            name: 'category',
                            orderable: true
                        },
                        {
                            data: 'status',
                            name: 'is_active',
                            orderable: true
                        },
                        {
                            data: 'generations_count',
                            name: 'generations_count',
                            orderable: true
                        },
                        {
                            data: 'created_at',
                            name: 'created_at',
                            render: function(data) {
                                const date = new Date(data);
                                const month = date.toLocaleString('default', {
                                    month: 'short'
                                });
                                return `${month} ${date.getDate()}, ${date.getFullYear()}`;
                            }
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                    order: [
                        [1, 'asc']
                    ]
                });

                // Filters
                $('#status-filter, #category-filter').change(function() {
                    table.draw();
                });

                // Select all checkbox
                $('#select-all').change(function() {
                    $('.content-type-checkbox').prop('checked', $(this).prop('checked'));
                    updateBulkDeleteButton();
                });

                // Individual checkbox change
                $(document).on('change', '.content-type-checkbox', function() {
                    updateBulkDeleteButton();
                });

                function updateBulkDeleteButton() {
                    const checkedBoxes = $('.content-type-checkbox:checked');
                    if (checkedBoxes.length > 0) {
                        $('#bulk-delete-btn').removeClass('hidden');
                    } else {
                        $('#bulk-delete-btn').addClass('hidden');
                    }
                }

                // Bulk delete
                $('#bulk-delete-btn').click(function() {
                    if (confirm('Are you sure you want to delete the selected content types?')) {
                        const ids = $('.content-type-checkbox:checked').map(function() {
                            return $(this).val();
                        }).get();

                        $('#bulk-ids').val(JSON.stringify(ids));
                        $('#bulk-delete-form').submit();
                    }
                });
            });
        </script>
    @endpush
