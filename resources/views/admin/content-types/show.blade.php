@extends('layouts.admin')

@section('title', 'Admin')

@section('content')

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Content Type Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contentType->name }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Slug</label>
                                    <p class="mt-1 text-sm text-gray-900 font-mono bg-gray-100 px-2 py-1 rounded">{{ $contentType->slug }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Category</label>
                                    <p class="mt-1">
                                        @if($contentType->category)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                {{ $contentType->category }}
                                            </span>
                                        @else
                                            <span class="text-gray-400">No category assigned</span>
                                        @endif
                                    </p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Status</label>
                                    <p class="mt-1">
                                        @if($contentType->is_active)
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                Active
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                Inactive
                                            </span>
                                        @endif
                                    </p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Created</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contentType->created_at->format('F j, Y \a\t g:i A') }}</p>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Last Updated</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contentType->updated_at->format('F j, Y \a\t g:i A') }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Usage Statistics -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">{{ number_format($stats['total_generations']) }}</div>
                                    <div class="text-sm text-blue-600">Total Generations</div>
                                </div>
                                
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">{{ number_format($stats['this_month']) }}</div>
                                    <div class="text-sm text-green-600">This Month</div>
                                </div>
                                
                                <div class="bg-purple-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-purple-600">{{ number_format($stats['this_week']) }}</div>
                                    <div class="text-sm text-purple-600">This Week</div>
                                </div>
                                
                                <div class="bg-yellow-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-yellow-600">{{ number_format($stats['today']) }}</div>
                                    <div class="text-sm text-yellow-600">Today</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Description -->
            @if($contentType->description)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Description</h3>
                        <p class="text-gray-700">{{ $contentType->description }}</p>
                    </div>
                </div>
            @endif

            <!-- Prompt Template -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Prompt Template</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <pre class="text-sm text-gray-800 whitespace-pre-wrap">{{ $contentType->prompt_template }}</pre>
                    </div>
                    
                    <!-- Template Preview -->
                    <div class="mt-4">
                        <h4 class="text-md font-medium text-gray-700 mb-2">Preview with Sample Data</h4>
                        <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                            <pre class="text-sm text-blue-800 whitespace-pre-wrap">{{ str_replace(['{keywords}', '{language}'], ['artificial intelligence', 'English'], $contentType->prompt_template) }}</pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Generations -->
            @if($contentType->generations->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Generations</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keywords</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Language</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($contentType->generations as $generation)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $generation->user->name }}</div>
                                                <div class="text-sm text-gray-500">{{ $generation->user->email }}</div>
                                            </td>
                                            <td class="px-6 py-4">
                                                <div class="text-sm text-gray-900">{{ Str::limit($generation->keywords, 50) }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                    {{ $generation->language }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $generation->created_at->format('M j, Y g:i A') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <a href="{{ route('admin.generations.show', $generation) }}" 
                                                   class="text-indigo-600 hover:text-indigo-900">View</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        
                        @if($stats['total_generations'] > 10)
                            <div class="mt-4 text-center">
                                <a href="{{ route('admin.generations.index', ['content_type' => $contentType->id]) }}" 
                                   class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    View all {{ number_format($stats['total_generations']) }} generations →
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No generations yet</h3>
                        <p class="mt-1 text-sm text-gray-500">This content type hasn't been used for any generations yet.</p>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
