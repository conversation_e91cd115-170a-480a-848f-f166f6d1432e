@extends('layouts.admin')

@section('title', 'Admin')

@section('content')

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <x-admin.card>
                <div class="space-y-6">
                    <!-- Header with avatar and basic info -->
                    <div class="flex items-start space-x-4">
                        @if ($testimonial->avatar)
                            <img src="{{ $testimonial->avatar }}" alt="{{ $testimonial->name }}"
                                class="w-16 h-16 rounded-full object-cover">
                        @else
                            <div class="w-16 h-16 rounded-full bg-gray-300 flex items-center justify-center">
                                <svg class="w-8 h-8 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                        @endif

                        <div class="flex-1">
                            <h3 class="text-2xl font-bold text-gray-900">{{ $testimonial->name }}</h3>
                            @if ($testimonial->title)
                                <p class="text-lg text-gray-600">{{ $testimonial->title }}</p>
                            @endif
                            @if ($testimonial->company)
                                <p class="text-md text-gray-500">{{ $testimonial->company }}</p>
                            @endif

                            <!-- Rating -->
                            @if ($testimonial->rating)
                                <div class="flex items-center mt-2">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= $testimonial->rating)
                                            <span class="text-yellow-400 text-xl">★</span>
                                        @else
                                            <span class="text-gray-300 text-xl">★</span>
                                        @endif
                                    @endfor
                                    <span class="ml-2 text-sm text-gray-600">({{ $testimonial->rating }}/5)</span>
                                </div>
                            @endif
                        </div>

                        <!-- Status badges -->
                        <div class="flex flex-col space-y-2">
                            @if ($testimonial->is_active)
                                <span
                                    class="px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                            @else
                                <span
                                    class="px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                            @endif

                            @if ($testimonial->is_featured)
                                <span
                                    class="px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">Featured</span>
                            @endif
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Testimonial Content</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-gray-700 leading-relaxed">{{ $testimonial->content }}</p>
                        </div>
                    </div>

                    <!-- Metadata -->
                    <div class="border-t pt-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-3">Details</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Published At:</span>
                                <p class="text-sm text-gray-900">
                                    {{ $testimonial->published_at ? $testimonial->published_at->format('M j, Y g:i A') : 'Not published' }}
                                </p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Created At:</span>
                                <p class="text-sm text-gray-900">{{ $testimonial->created_at->format('M j, Y g:i A') }}
                                </p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Last Updated:</span>
                                <p class="text-sm text-gray-900">{{ $testimonial->updated_at->format('M j, Y g:i A') }}
                                </p>
                            </div>
                            @if ($testimonial->avatar)
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Avatar URL:</span>
                                    <p class="text-sm text-gray-900 truncate">
                                        <a href="{{ $testimonial->avatar }}" target="_blank"
                                            class="text-blue-600 hover:text-blue-900">
                                            {{ $testimonial->avatar }}
                                        </a>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="border-t pt-6">
                        <div class="flex justify-between">
                            <div class="flex space-x-3">
                                <form method="POST"
                                    action="{{ route('admin.testimonials.toggle-status', $testimonial) }}"
                                    class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit"
                                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded"
                                        onclick="return confirm('Are you sure you want to {{ $testimonial->is_active ? 'deactivate' : 'activate' }} this testimonial?')">
                                        {{ $testimonial->is_active ? 'Deactivate' : 'Activate' }}
                                    </button>
                                </form>

                                <form method="POST"
                                    action="{{ route('admin.testimonials.toggle-featured', $testimonial) }}"
                                    class="inline">
                                    @csrf
                                    @method('PATCH')
                                    <button type="submit"
                                        class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
                                        onclick="return confirm('Are you sure you want to {{ $testimonial->is_featured ? 'unfeature' : 'feature' }} this testimonial?')">
                                        {{ $testimonial->is_featured ? 'Unfeature' : 'Feature' }}
                                    </button>
                                </form>
                            </div>

                            <form method="POST" action="{{ route('admin.testimonials.destroy', $testimonial) }}"
                                class="inline">
                                @csrf
                                @method('DELETE')
                                <button type="submit"
                                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                                    onclick="return confirm('Are you sure you want to delete this testimonial? This action cannot be undone.')">
                                    Delete
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </x-admin.card>
        </div>
    </div>
@endsection
