@extends('layouts.admin')

@section('title', 'Admin')

@section('content')

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <x-admin.card>
                <form method="POST" action="{{ route('admin.testimonials.store') }}">
                    @csrf

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <x-admin.input label="Name" name="name" type="text" :value="old('name')" required />
                        </div>

                        <!-- Title -->
                        <div>
                            <x-admin.input label="Title" name="title" type="text" :value="old('title')" />
                        </div>

                        <!-- Company -->
                        <div>
                            <x-admin.input label="Company" name="company" type="text" :value="old('company')" />
                        </div>

                        <!-- Rating -->
                        <div>
                            <x-admin.select label="Rating" name="rating" :value="old('rating')" :options="[
                                '' => 'No Rating',
                                '1' => '1 Star',
                                '2' => '2 Stars',
                                '3' => '3 Stars',
                                '4' => '4 Stars',
                                '5' => '5 Stars',
                            ]" />
                        </div>

                        <!-- Avatar URL -->
                        <div class="md:col-span-2">
                            <x-admin.input label="Avatar URL" name="avatar" type="url" :value="old('avatar')"
                                placeholder="https://example.com/avatar.jpg" />
                        </div>

                        <!-- Content -->
                        <div class="md:col-span-2">
                            <x-admin.textarea label="Content" name="content" :value="old('content')" rows="5"
                                required />
                        </div>

                        <!-- Published At -->
                        <div>
                            <x-admin.input label="Published At" name="published_at" type="datetime-local"
                                :value="old('published_at', now()->format('Y-m-d\TH:i'))" />
                        </div>

                        <!-- Checkboxes -->
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_active" id="is_active" value="1"
                                    {{ old('is_active', true) ? 'checked' : '' }}
                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <label for="is_active" class="ml-2 block text-sm text-gray-900">Active</label>
                            </div>

                            <div class="flex items-center">
                                <input type="checkbox" name="is_featured" id="is_featured" value="1"
                                    {{ old('is_featured') ? 'checked' : '' }}
                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <label for="is_featured" class="ml-2 block text-sm text-gray-900">Featured</label>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3 mt-6">
                        <a href="{{ route('admin.testimonials.index') }}"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </a>
                        <x-admin.button type="submit">
                            Create Testimonial
                        </x-admin.button>
                    </div>
                </form>
            </x-admin.card>
        </div>
    </div>
@endsection
