@extends('layouts.admin')

@section('title', 'Edit Page')

@section('content')
    <div class="p-6">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-semibold text-gray-900">Edit Page</h1>
                <p class="text-gray-600">Update page information and content</p>
            </div>
            <div class="flex gap-2">
                <a href="{{ route('admin.pages.show', $page) }}"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-eye mr-2"></i>View Page
                </a>
                <a href="{{ route('admin.pages.index') }}"
                    class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Pages
                </a>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-lg">
            <form action="{{ route('admin.pages.update', $page) }}" method="POST" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Main Content -->
                    <div class="lg:col-span-2 space-y-6">
                        <!-- Title -->
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="title" name="title"
                                value="{{ old('title', $translation->title) }}"
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('title') border-red-500 @enderror"
                                placeholder="Enter page title">
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Content -->
                        <div>
                            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                Content
                            </label>
                            <textarea id="content" name="content" rows="12"
                                class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('content') border-red-500 @enderror"
                                placeholder="Enter page content">{{ old('content', $translation->content) }}</textarea>
                            @error('content')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- SEO Section -->
                        <div class="border-t pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Settings</h3>

                            <div class="space-y-4">
                                <!-- Meta Description -->
                                <div>
                                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">
                                        Meta Description
                                    </label>
                                    <textarea id="meta_description" name="meta_description" rows="3"
                                        class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('meta_description') border-red-500 @enderror"
                                        placeholder="Enter meta description (recommended: 150-160 characters)" maxlength="255">{{ old('meta_description', $translation->meta_description) }}</textarea>
                                    @error('meta_description')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-sm text-gray-500">
                                        <span id="meta-description-count">0</span>/255 characters
                                    </p>
                                </div>

                                <!-- Meta Keywords -->
                                <div>
                                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                        Meta Keywords
                                    </label>
                                    <input type="text" id="meta_keywords" name="meta_keywords"
                                        value="{{ old('meta_keywords', $translation->meta_keywords) }}"
                                        class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('meta_keywords') border-red-500 @enderror"
                                        placeholder="keyword1, keyword2, keyword3">
                                    @error('meta_keywords')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                    <p class="mt-1 text-sm text-gray-500">Separate keywords with commas</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Publish Settings -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Publish Settings</h3>

                            <!-- Slug -->
                            <div class="mb-4">
                                <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                                    Slug <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="slug" name="slug" value="{{ old('slug', $page->slug) }}"
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('slug') border-red-500 @enderror"
                                    placeholder="page-url-slug">
                                @error('slug')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                                <p class="mt-1 text-sm text-gray-500">URL-friendly version of the title</p>
                            </div>

                            <!-- Status -->
                            <div class="mb-4">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                    Status <span class="text-red-500">*</span>
                                </label>
                                <select id="status" name="status"
                                    class="w-full border border-gray-300 rounded-lg px-4 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent @error('status') border-red-500 @enderror">
                                    <option value="draft"
                                        {{ old('status', $page->status) === 'draft' ? 'selected' : '' }}>Draft</option>
                                    <option value="published"
                                        {{ old('status', $page->status) === 'published' ? 'selected' : '' }}>Published
                                    </option>
                                </select>
                                @error('status')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Actions -->
                            <div class="flex flex-col gap-2 pt-4 border-t">
                                <button type="submit"
                                    class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                                    <i class="fas fa-save mr-2"></i>Update Page
                                </button>

                                <a href="{{ route('admin.pages.index') }}"
                                    class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200 text-center">
                                    <i class="fas fa-times mr-2"></i>Cancel
                                </a>
                            </div>
                        </div>

                        <!-- Page Info -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">
                                <i class="fas fa-info-circle mr-1"></i>Page Information
                            </h4>
                            <div class="text-sm text-gray-600 space-y-2">
                                <div>
                                    <span class="font-medium">Created:</span>
                                    {{ $page->created_at->format('M d, Y g:i A') }}
                                </div>
                                <div>
                                    <span class="font-medium">Updated:</span>
                                    {{ $page->updated_at->format('M d, Y g:i A') }}
                                </div>
                                <div>
                                    <span class="font-medium">Current Status:</span>
                                    <span
                                        class="px-2 py-1 text-xs font-semibold text-white rounded {{ $page->status === 'published' ? 'bg-green-500' : 'bg-yellow-500' }}">
                                        {{ ucfirst($page->status) }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Help -->
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-blue-900 mb-2">
                                <i class="fas fa-lightbulb mr-1"></i>Tips
                            </h4>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• Keep titles descriptive and under 60 characters</li>
                                <li>• Meta descriptions should be 150-160 characters</li>
                                <li>• Avoid changing slugs of published pages</li>
                                <li>• Use headings (H1, H2, H3) for better structure</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                // Character counter for meta description
                $('#meta_description').on('input', function() {
                    let length = $(this).val().length;
                    $('#meta-description-count').text(length);

                    if (length > 160) {
                        $('#meta-description-count').addClass('text-red-500');
                    } else {
                        $('#meta-description-count').removeClass('text-red-500');
                    }
                });

                // Initialize character count
                $('#meta_description').trigger('input');

                // Warn about slug changes for published pages
                @if ($page->status === 'published')
                    let originalSlug = $('#slug').val();
                    $('#slug').on('change', function() {
                        if ($(this).val() !== originalSlug) {
                            alert(
                                'Warning: Changing the slug of a published page may break existing links and affect SEO.');
                        }
                    });
                @endif
            });
        </script>
    @endpush
@endsection
