@extends('layouts.admin')

@section('title', 'View Page')

@section('content')
<div class="p-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h1 class="text-2xl font-semibold text-gray-900">Page Details</h1>
            <p class="text-gray-600">View page information and content</p>
        </div>
        <div class="flex gap-2">
            <a href="{{ route('pages.show', $page->slug) }}" 
               target="_blank"
               class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-external-link-alt mr-2"></i>View Live
            </a>
            <a href="{{ route('admin.pages.edit', $page) }}" 
               class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-edit mr-2"></i>Edit Page
            </a>
            <a href="{{ route('admin.pages.index') }}" 
               class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-arrow-left mr-2"></i>Back to Pages
            </a>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <!-- Page Header -->
                <div class="border-b pb-4 mb-6">
                    <div class="flex items-start justify-between">
                        <div>
                            <h2 class="text-xl font-semibold text-gray-900">
                                {{ $page->translations->first()->title ?? 'No Title' }}
                            </h2>
                            <div class="flex items-center gap-4 mt-2">
                                <span class="px-3 py-1 text-sm font-semibold text-white rounded {{ $page->status === 'published' ? 'bg-green-500' : 'bg-yellow-500' }}">
                                    {{ ucfirst($page->status) }}
                                </span>
                                <span class="text-sm text-gray-500">
                                    <code class="bg-gray-100 px-2 py-1 rounded">{{ $page->slug }}</code>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="flex gap-2">
                            <button onclick="toggleStatus('{{ $page->id }}')" 
                                    class="px-3 py-1 text-sm rounded transition duration-200 {{ $page->status === 'published' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' : 'bg-green-100 text-green-800 hover:bg-green-200' }}">
                                {{ $page->status === 'published' ? 'Make Draft' : 'Publish' }}
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Page Content -->
                <div class="prose max-w-none">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Content</h3>
                    @if($page->translations->first() && $page->translations->first()->content)
                        <div class="bg-gray-50 rounded-lg p-4 border">
                            {!! nl2br(e($page->translations->first()->content)) !!}
                        </div>
                    @else
                        <div class="bg-gray-50 rounded-lg p-4 border text-gray-500 italic">
                            No content available
                        </div>
                    @endif
                </div>

                <!-- SEO Information -->
                @php
                    $translation = $page->translations->first();
                @endphp
                @if($translation && ($translation->meta_description || $translation->meta_keywords))
                <div class="border-t pt-6 mt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Information</h3>
                    
                    @if($translation->meta_description)
                    <div class="mb-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Meta Description</h4>
                        <div class="bg-gray-50 rounded-lg p-3 border">
                            <p class="text-sm text-gray-600">{{ $translation->meta_description }}</p>
                            <p class="text-xs text-gray-500 mt-1">{{ strlen($translation->meta_description) }} characters</p>
                        </div>
                    </div>
                    @endif

                    @if($translation->meta_keywords)
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">Meta Keywords</h4>
                        <div class="bg-gray-50 rounded-lg p-3 border">
                            <div class="flex flex-wrap gap-2">
                                @foreach(explode(',', $translation->meta_keywords) as $keyword)
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">{{ trim($keyword) }}</span>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Page Statistics -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Page Statistics</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Status</span>
                        <span class="px-2 py-1 text-xs font-semibold text-white rounded {{ $page->status === 'published' ? 'bg-green-500' : 'bg-yellow-500' }}">
                            {{ ucfirst($page->status) }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Created</span>
                        <span class="text-sm text-gray-900">{{ $page->created_at->format('M d, Y') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Last Updated</span>
                        <span class="text-sm text-gray-900">{{ $page->updated_at->format('M d, Y') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Slug</span>
                        <code class="text-sm bg-gray-100 px-2 py-1 rounded">{{ $page->slug }}</code>
                    </div>

                    @if($translation)
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">Content Length</span>
                        <span class="text-sm text-gray-900">{{ strlen($translation->content ?? '') }} chars</span>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <button onclick="toggleStatus('{{ $page->id }}')" 
                            class="w-full px-4 py-2 text-sm rounded transition duration-200 {{ $page->status === 'published' ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' : 'bg-green-100 text-green-800 hover:bg-green-200' }}">
                        <i class="fas fa-toggle-{{ $page->status === 'published' ? 'off' : 'on' }} mr-2"></i>
                        {{ $page->status === 'published' ? 'Make Draft' : 'Publish Page' }}
                    </button>
                    
                    <a href="{{ route('admin.pages.edit', $page) }}" 
                       class="w-full bg-blue-100 text-blue-800 hover:bg-blue-200 px-4 py-2 rounded text-sm transition duration-200 text-center block">
                        <i class="fas fa-edit mr-2"></i>Edit Page
                    </a>
                    
                    @if($page->status === 'published')
                    <a href="{{ route('pages.show', $page->slug) }}" 
                       target="_blank"
                       class="w-full bg-green-100 text-green-800 hover:bg-green-200 px-4 py-2 rounded text-sm transition duration-200 text-center block">
                        <i class="fas fa-external-link-alt mr-2"></i>View Live Page
                    </a>
                    @endif
                    
                    <form action="{{ route('admin.pages.destroy', $page) }}" 
                          method="POST" 
                          onsubmit="return confirm('Are you sure you want to delete this page? This action cannot be undone.')"
                          class="w-full">
                        @csrf
                        @method('DELETE')
                        <button type="submit" 
                                class="w-full bg-red-100 text-red-800 hover:bg-red-200 px-4 py-2 rounded text-sm transition duration-200">
                            <i class="fas fa-trash mr-2"></i>Delete Page
                        </button>
                    </form>
                </div>
            </div>

            <!-- Page URL Preview -->
            @if($page->status === 'published')
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">URL Preview</h3>
                
                <div class="bg-gray-50 rounded-lg p-3 border">
                    <p class="text-sm text-gray-600 mb-1">Public URL:</p>
                    <a href="{{ route('pages.show', $page->slug) }}" 
                       target="_blank" 
                       class="text-blue-600 hover:text-blue-800 text-sm break-all">
                        {{ route('pages.show', $page->slug) }}
                    </a>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

@push('scripts')
<script>
function toggleStatus(pageId) {
    if (confirm('Are you sure you want to change the status of this page?')) {
        $.ajax({
            url: `/admin/pages/${pageId}/toggle-status`,
            method: 'PATCH',
            data: {
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                }
            },
            error: function(xhr) {
                alert('Error: ' + (xhr.responseJSON?.message || 'Something went wrong'));
            }
        });
    }
}
</script>
@endpush
@endsection
