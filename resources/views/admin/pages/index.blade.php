@extends('layouts.admin')

@section('title', 'Pages Management')

@section('content')
    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-semibold text-gray-900">Pages Management</h1>
            <a href="{{ route('admin.pages.create') }}"
                class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition duration-200">
                <i class="fas fa-plus mr-2"></i>Add New Page
            </a>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-file-alt text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Pages</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['total'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check-circle text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Published</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['published'] }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                        <i class="fas fa-clock text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Draft</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $stats['draft'] }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters and Actions -->
        <div class="bg-white rounded-lg shadow mb-6">
            <div class="p-6 border-b">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <div class="flex flex-wrap items-center gap-4">
                        <!-- Status Filter -->
                        <div>
                            <label for="status-filter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select id="status-filter" class="form-select border-gray-300 rounded-md shadow-sm">
                                <option value="">All Statuses</option>
                                <option value="published">Published</option>
                                <option value="draft">Draft</option>
                            </select>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="flex items-center gap-2">
                        <button id="bulk-delete-btn"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition duration-200 opacity-50 cursor-not-allowed"
                            disabled>
                            <i class="fas fa-trash mr-2"></i>Delete Selected
                        </button>
                    </div>
                </div>
            </div>

            <!-- DataTable -->
            <div class="overflow-x-auto">
                <table id="pages-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- DataTable will populate this -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            $(document).ready(function() {
                let table = $('#pages-table').DataTable({
                    processing: true,
                    serverSide: true,
                    ajax: {
                        url: "{{ route('admin.pages.index') }}",
                        data: function(d) {
                            d.status = $('#status-filter').val();
                        }
                    },
                    columns: [{
                            data: 'checkbox',
                            name: 'checkbox',
                            orderable: false,
                            searchable: false,
                            width: '50px'
                        },
                        {
                            data: 'title',
                            name: 'title'
                        },
                        {
                            data: 'slug',
                            name: 'slug'
                        },
                        {
                            data: 'status_label',
                            name: 'status'
                        },
                        {
                            data: 'created_at_formatted',
                            name: 'created_at'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false,
                            width: '150px'
                        }
                    ],
                    order: [
                        [4, 'desc']
                    ],
                    pageLength: 25,
                    responsive: true,
                    dom: 'rtip',
                    language: {
                        processing: 'Loading...',
                        emptyTable: 'No pages found',
                        info: 'Showing _START_ to _END_ of _TOTAL_ pages',
                        infoEmpty: 'Showing 0 to 0 of 0 pages',
                        infoFiltered: '(filtered from _MAX_ total pages)'
                    }
                });

                // Filter functionality
                $('#status-filter').on('change', function() {
                    table.draw();
                });

                // Select all functionality
                $('#select-all').on('change', function() {
                    $('.page-checkbox').prop('checked', this.checked);
                    toggleBulkActions();
                });

                // Individual checkbox functionality
                $(document).on('change', '.page-checkbox', function() {
                    toggleBulkActions();

                    // Update select all checkbox
                    let totalCheckboxes = $('.page-checkbox').length;
                    let checkedCheckboxes = $('.page-checkbox:checked').length;

                    $('#select-all').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes <
                        totalCheckboxes);
                    $('#select-all').prop('checked', checkedCheckboxes === totalCheckboxes);
                });

                // Toggle bulk action buttons
                function toggleBulkActions() {
                    let checkedCount = $('.page-checkbox:checked').length;

                    if (checkedCount > 0) {
                        $('#bulk-delete-btn').removeClass('opacity-50 cursor-not-allowed').prop('disabled', false);
                    } else {
                        $('#bulk-delete-btn').addClass('opacity-50 cursor-not-allowed').prop('disabled', true);
                    }
                }

                // Bulk delete functionality
                $('#bulk-delete-btn').on('click', function() {
                    let selectedIds = [];
                    $('.page-checkbox:checked').each(function() {
                        selectedIds.push($(this).val());
                    });

                    if (selectedIds.length === 0) {
                        return;
                    }

                    if (confirm(
                            `Are you sure you want to delete ${selectedIds.length} page(s)? This action cannot be undone.`
                            )) {
                        $.ajax({
                            url: "{{ route('admin.pages.bulk-destroy') }}",
                            method: 'POST',
                            data: {
                                ids: selectedIds,
                                _token: "{{ csrf_token() }}"
                            },
                            success: function(response) {
                                if (response.success) {
                                    table.draw(false);
                                    $('#select-all').prop('checked', false);
                                    toggleBulkActions();

                                    // Show success message
                                    alert(response.message);
                                }
                            },
                            error: function(xhr) {
                                alert('Error: ' + (xhr.responseJSON?.message ||
                                    'Something went wrong'));
                            }
                        });
                    }
                });

                // Toggle status functionality
                $(document).on('click', '.toggle-status', function(e) {
                    e.preventDefault();

                    let url = $(this).data('url');
                    let button = $(this);

                    $.ajax({
                        url: url,
                        method: 'PATCH',
                        data: {
                            _token: "{{ csrf_token() }}"
                        },
                        success: function(response) {
                            if (response.success) {
                                table.draw(false);
                            }
                        },
                        error: function(xhr) {
                            alert('Error: ' + (xhr.responseJSON?.message ||
                            'Something went wrong'));
                        }
                    });
                });

                // Delete functionality
                $(document).on('click', '.delete-item', function(e) {
                    e.preventDefault();

                    if (confirm('Are you sure you want to delete this page? This action cannot be undone.')) {
                        let form = $(this).closest('form');
                        form.submit();
                    }
                });
            });
        </script>
    @endpush
@endsection
