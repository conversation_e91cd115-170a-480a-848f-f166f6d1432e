@extends('layouts.admin')

@section('title', __('admin.edit') . ' FAQ')

@section('content')
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ __('admin.edit') }} FAQ</h1>
                <p class="mt-2 text-gray-600">{{ __('admin.edit') }} FAQ #{{ $faq->id }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.faqs.show', $faq) }}" class="btn-secondary">
                    {{ __('admin.view') }}
                </a>
                <a href="{{ route('admin.faqs.index') }}" class="btn-secondary">
                    {{ __('admin.back') }}
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <form action="{{ route('admin.faqs.update', $faq) }}" method="POST" class="p-6">
                @csrf
                @method('PUT')

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Sort Order -->
                    <div>
                        <label for="sort_order" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ __('admin.order') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="number" name="sort_order" id="sort_order" 
                            value="{{ old('sort_order', $faq->sort_order) }}" min="0"
                            class="w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('sort_order') border-red-300 @enderror">
                        @error('sort_order')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="is_active" value="1" class="rounded text-blue-600"
                                {{ old('is_active', $faq->is_active) ? 'checked' : '' }}>
                            <span class="text-gray-700 font-medium">{{ __('admin.active') }}</span>
                        </label>
                    </div>
                </div>

                <!-- Translations -->
                <div class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900">{{ __('admin.faq_translations') }}</h3>
                    
                    @foreach($languages as $index => $language)
                        @php
                            $translation = $faq->translations->where('locale', $language)->first();
                        @endphp
                        <div class="border border-gray-200 rounded-lg p-4">
                            <h4 class="text-md font-medium text-gray-800 mb-4">
                                {{ strtoupper($language) }} - {{ __('app.' . $language) }}
                                @if($translation)
                                    <span class="ml-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
                                        {{ __('admin.translated') }}
                                    </span>
                                @else
                                    <span class="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
                                        {{ __('admin.not_translated') }}
                                    </span>
                                @endif
                            </h4>
                            
                            <input type="hidden" name="translations[{{ $index }}][locale]" value="{{ $language }}">
                            
                            <div class="grid grid-cols-1 gap-4">
                                <!-- Question -->
                                <div>
                                    <label for="question_{{ $language }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ __('admin.faq_question') }} <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" 
                                        name="translations[{{ $index }}][question]" 
                                        id="question_{{ $language }}"
                                        value="{{ old('translations.' . $index . '.question', $translation?->question) }}"
                                        maxlength="500"
                                        class="w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('translations.' . $index . '.question') border-red-300 @enderror">
                                    @error('translations.' . $index . '.question')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Answer -->
                                <div>
                                    <label for="answer_{{ $language }}" class="block text-sm font-medium text-gray-700 mb-1">
                                        {{ __('admin.faq_answer') }} <span class="text-red-500">*</span>
                                    </label>
                                    <textarea name="translations[{{ $index }}][answer]" 
                                        id="answer_{{ $language }}"
                                        rows="4"
                                        class="w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('translations.' . $index . '.answer') border-red-300 @enderror">{{ old('translations.' . $index . '.answer', $translation?->answer) }}</textarea>
                                    @error('translations.' . $index . '.answer')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Actions -->
                <div class="mt-8 flex justify-end space-x-3">
                    <a href="{{ route('admin.faqs.index') }}" class="btn-secondary">
                        {{ __('admin.cancel') }}
                    </a>
                    <button type="submit" class="btn-primary">
                        {{ __('admin.save') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
