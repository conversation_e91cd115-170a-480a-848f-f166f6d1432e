@extends('layouts.admin')

@section('title', 'FAQ #' . $faq->id)

@section('content')
    <div class="space-y-6">
        <!-- Header -->
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">FAQ #{{ $faq->id }}</h1>
                <p class="mt-2 text-gray-600">{{ __('admin.view') }} FAQ {{ __('admin.details') }}</p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ route('admin.faqs.edit', $faq) }}" class="btn-primary">
                    {{ __('admin.edit') }}
                </a>
                <a href="{{ route('admin.faqs.index') }}" class="btn-secondary">
                    {{ __('admin.back') }}
                </a>
            </div>
        </div>

        <!-- FAQ Details -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <!-- Sort Order -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">{{ __('admin.order') }}</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $faq->sort_order }}</p>
                    </div>

                    <!-- Status -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">{{ __('admin.status') }}</label>
                        @if($faq->is_active)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {{ __('admin.active') }}
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                {{ __('admin.inactive') }}
                            </span>
                        @endif
                    </div>

                    <!-- Created Date -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">{{ __('admin.created') }}</label>
                        <p class="text-lg font-semibold text-gray-900">{{ $faq->created_at->format('M d, Y') }}</p>
                    </div>
                </div>

                <!-- Translations -->
                <div class="space-y-6">
                    <h3 class="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
                        {{ __('admin.faq_translations') }}
                    </h3>
                    
                    @if($faq->translations->count() > 0)
                        <div class="grid grid-cols-1 gap-6">
                            @foreach($faq->translations as $translation)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-4">
                                        <h4 class="text-md font-medium text-gray-800">
                                            {{ strtoupper($translation->locale) }} - {{ __('app.' . $translation->locale) }}
                                        </h4>
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                            {{ $translation->locale }}
                                        </span>
                                    </div>
                                    
                                    <div class="space-y-4">
                                        <!-- Question -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 mb-2">
                                                {{ __('admin.faq_question') }}
                                            </label>
                                            <div class="bg-gray-50 rounded-md p-3">
                                                <p class="text-gray-900">{{ $translation->question }}</p>
                                            </div>
                                        </div>

                                        <!-- Answer -->
                                        <div>
                                            <label class="block text-sm font-medium text-gray-500 mb-2">
                                                {{ __('admin.faq_answer') }}
                                            </label>
                                            <div class="bg-gray-50 rounded-md p-3">
                                                <div class="text-gray-900 whitespace-pre-wrap">{{ $translation->answer }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <div class="text-gray-400 text-lg mb-2">📝</div>
                            <p class="text-gray-500">{{ __('admin.no_translations_found') }}</p>
                            <a href="{{ route('admin.faqs.edit', $faq) }}" class="mt-2 inline-flex items-center text-blue-600 hover:text-blue-500">
                                {{ __('admin.add_translations') }}
                            </a>
                        </div>
                    @endif
                </div>

                <!-- Metadata -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">{{ __('admin.metadata') }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium text-gray-500">{{ __('admin.created') }}:</span>
                            <span class="text-gray-900">{{ $faq->created_at->format('F j, Y \a\t g:i A') }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">{{ __('admin.updated') }}:</span>
                            <span class="text-gray-900">{{ $faq->updated_at->format('F j, Y \a\t g:i A') }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">{{ __('admin.total') }} {{ __('admin.translations') }}:</span>
                            <span class="text-gray-900">{{ $faq->translations->count() }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-500">{{ __('admin.languages') }}:</span>
                            <span class="text-gray-900">
                                @if($faq->translations->count() > 0)
                                    {{ $faq->translations->pluck('locale')->join(', ') }}
                                @else
                                    {{ __('admin.none') }}
                                @endif
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-between items-center">
            <div class="flex space-x-3">
                <a href="{{ route('admin.faqs.edit', $faq) }}" class="btn-primary">
                    {{ __('admin.edit') }}
                </a>
                <button type="button" onclick="confirmDelete()" class="btn-danger">
                    {{ __('admin.delete') }}
                </button>
            </div>
            
            <a href="{{ route('admin.faqs.index') }}" class="btn-secondary">
                {{ __('admin.back_to_list') }}
            </a>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg font-medium text-gray-900">{{ __('admin.confirm_delete') }}</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        {{ __('admin.are_you_sure_delete_faq') }}
                    </p>
                </div>
                <div class="items-center px-4 py-3">
                    <form id="deleteForm" action="{{ route('admin.faqs.destroy', $faq) }}" method="POST" class="inline">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-300 mr-2">
                            {{ __('admin.delete') }}
                        </button>
                    </form>
                    <button onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        {{ __('admin.cancel') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function confirmDelete() {
            document.getElementById('deleteModal').classList.remove('hidden');
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });
    </script>
@endpush
