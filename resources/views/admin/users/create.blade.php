@extends('layouts.admin')

@section('title', 'Create User')

@section('content')
    <x-admin.breadcrumbs :items="[
        ['label' => 'Users', 'route' => 'admin.users.index'],
        ['label' => 'Create', 'route' => 'admin.users.create'],
    ]" />

    <div class="mb-4">
        <h1 class="text-2xl font-semibold">Create New User</h1>
    </div>

    <x-admin.card>
        <form action="{{ route('admin.users.store') }}" method="POST">
            @csrf

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                    <x-admin.input label="Name" name="name" required :value="old('name')" />
                </div>

                <div>
                    <x-admin.input label="Email" name="email" type="email" required :value="old('email')" />
                </div>

                <div>
                    <x-admin.input label="Password" name="password" type="password" required autocomplete="new-password" />
                </div>

                <div>
                    <x-admin.input label="Confirm Password" name="password_confirmation" type="password" required
                        autocomplete="new-password" />
                </div>

                <div>
                    <label for="preferred_language" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ __('admin.preferred_language') }}
                    </label>
                    <select name="preferred_language" id="preferred_language"
                        class="w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 @error('preferred_language') border-red-300 @enderror">
                        <option value="">{{ __('admin.select_language') }}</option>
                        <option value="en" {{ old('preferred_language') == 'en' ? 'selected' : '' }}>English</option>
                        <option value="es" {{ old('preferred_language') == 'es' ? 'selected' : '' }}>Español</option>
                        <option value="fr" {{ old('preferred_language') == 'fr' ? 'selected' : '' }}>Français</option>
                        <option value="de" {{ old('preferred_language') == 'de' ? 'selected' : '' }}>Deutsch</option>
                        <option value="ar" {{ old('preferred_language') == 'ar' ? 'selected' : '' }}>العربية</option>
                        <option value="pt" {{ old('preferred_language') == 'pt' ? 'selected' : '' }}>Português</option>
                        <option value="ru" {{ old('preferred_language') == 'ru' ? 'selected' : '' }}>Русский</option>
                    </select>
                    @error('preferred_language')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div class="md:col-span-2">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" name="is_admin" value="1" class="rounded text-blue-600"
                            {{ old('is_admin') ? 'checked' : '' }}>
                        <span class="text-gray-700 font-medium">{{ __('admin.admin_user') }}</span>
                    </label>
                </div>

                <div class="md:col-span-2 mb-4">
                    <label
                        class="block text-sm font-medium text-gray-700 mb-1">{{ __('admin.notification_preferences') }}</label>
                    <div class="space-y-2">
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="notification_preferences[email]" value="1"
                                class="rounded text-blue-600"
                                {{ old('notification_preferences.email', true) ? 'checked' : '' }}>
                            <span class="text-gray-700">{{ __('admin.email_notifications') }}</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="notification_preferences[in_app]" value="1"
                                class="rounded text-blue-600"
                                {{ old('notification_preferences.in_app', true) ? 'checked' : '' }}>
                            <span class="text-gray-700">{{ __('admin.in_app_notifications') }}</span>
                        </label>
                    </div>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Roles</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 border p-3 rounded-md">
                        @foreach ($roles as $role)
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" name="roles[]" value="{{ $role->id }}"
                                    class="rounded text-blue-600"
                                    {{ in_array($role->id, old('roles', [])) ? 'checked' : '' }}>
                                <span class="text-gray-700">{{ $role->name }}</span>
                                @if ($role->is_default)
                                    <span class="px-2 py-0.5 text-xs text-white bg-green-500 rounded">Default</span>
                                @endif
                            </label>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <a href="{{ route('admin.users.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    Create User
                </button>
            </div>
        </form>
    </x-admin.card>
@endsection
