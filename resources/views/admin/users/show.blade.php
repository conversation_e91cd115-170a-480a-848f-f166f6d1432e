<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Admin - User Details') }}
            </h2>
            <a href="{{ route('admin.users.index') }}" class="text-gray-600 hover:text-gray-800">
                ← Back to Users
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- User Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">User Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="flex items-center space-x-4 mb-4">
                                <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                                    <span class="text-xl font-medium text-gray-700">
                                        {{ substr($user->name, 0, 1) }}
                                    </span>
                                </div>
                                <div>
                                    <h4 class="text-xl font-semibold text-gray-900">{{ $user->name }}</h4>
                                    <p class="text-gray-600">{{ $user->email }}</p>
                                </div>
                            </div>
                            
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Joined:</span>
                                    <span class="text-gray-900">{{ $user->created_at->format('M d, Y g:i A') }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Language:</span>
                                    <span class="text-gray-900">
                                        @switch($user->preferred_language)
                                            @case('ar') العربية @break
                                            @case('en') English @break
                                            @case('fr') Français @break
                                            @default {{ $user->preferred_language }}
                                        @endswitch
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-500">Admin:</span>
                                    <span class="text-gray-900">{{ $user->isAdmin() ? 'Yes' : 'No' }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Subscription Information -->
                        <div>
                            <h5 class="font-medium text-gray-900 mb-3">Current Subscription</h5>
                            @if($user->subscription)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="font-medium text-gray-900">{{ $user->subscription->plan->name }}</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            @switch($user->subscription->status)
                                                @case('active') bg-green-100 text-green-800 @break
                                                @case('past_due') bg-yellow-100 text-yellow-800 @break
                                                @case('canceled') bg-red-100 text-red-800 @break
                                                @default bg-gray-100 text-gray-800
                                            @endswitch">
                                            {{ ucfirst($user->subscription->status) }}
                                        </span>
                                    </div>
                                    <div class="space-y-1 text-sm text-gray-600">
                                        <div>Price: ${{ $user->subscription->plan->monthly_price }}/month</div>
                                        <div>Generations: {{ $user->subscription->generations_used }} / 
                                            {{ $user->subscription->plan->monthly_generations_limit === -1 ? 'Unlimited' : $user->subscription->plan->monthly_generations_limit }}
                                        </div>
                                        @if($user->subscription->starts_at)
                                            <div>Started: {{ $user->subscription->starts_at->format('M d, Y') }}</div>
                                        @endif
                                        @if($user->subscription->ends_at)
                                            <div>Ends: {{ $user->subscription->ends_at->format('M d, Y') }}</div>
                                        @endif
                                        @if($user->subscription->stripe_subscription_id)
                                            <div>Stripe ID: {{ $user->subscription->stripe_subscription_id }}</div>
                                        @endif
                                    </div>
                                </div>
                            @else
                                <div class="text-gray-500">No active subscription</div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Generation History -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Generation History</h3>
                    
                    @if($user->generations->count() > 0)
                        <div class="space-y-4">
                            @foreach($user->generations->take(10) as $generation)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <h4 class="font-medium text-gray-900">{{ $generation->contentType->name }}</h4>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    @switch($generation->language)
                                                        @case('ar') العربية @break
                                                        @case('en') English @break
                                                        @case('fr') Français @break
                                                        @default {{ $generation->language }}
                                                    @endswitch
                                                </span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-2">
                                                <strong>Keywords:</strong> {{ $generation->keywords }}
                                            </p>
                                            <div class="bg-gray-50 rounded p-3">
                                                <p class="text-sm text-gray-700 line-clamp-3">
                                                    {{ Str::limit($generation->result, 200) }}
                                                </p>
                                            </div>
                                        </div>
                                        <div class="ml-4 text-right">
                                            <p class="text-sm text-gray-500">{{ $generation->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                            
                            @if($user->generations->count() > 10)
                                <div class="text-center">
                                    <p class="text-gray-500">Showing 10 of {{ $user->generations->count() }} generations</p>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <p class="text-gray-500">No generations found.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Usage Statistics -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Usage Statistics</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600">{{ $user->generations->count() }}</div>
                            <div class="text-sm text-gray-500">Total Generations</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600">
                                {{ $user->generations->where('created_at', '>=', now()->startOfMonth())->count() }}
                            </div>
                            <div class="text-sm text-gray-500">This Month</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600">
                                {{ $user->generations->where('created_at', '>=', now()->subDays(7))->count() }}
                            </div>
                            <div class="text-sm text-gray-500">Last 7 Days</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
