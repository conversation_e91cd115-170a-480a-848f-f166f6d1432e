@extends('layouts.admin')

@section('title', __('admin.users'))

@section('content')
    <x-admin.breadcrumbs :items="[['label' => __('admin.users'), 'route' => 'admin.users.index']]" />

    <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-semibold">{{ __('admin.user_management') }}</h1>
        <a href="{{ route('admin.users.create') }}" class="btn-primary">
            <i class="fas fa-plus mr-1"></i> {{ __('admin.add_new_user') }}
        </a>
    </div>

    <x-admin.card>
        <!-- Search and Filters -->
        <div class="mb-6">
            <form method="GET" action="{{ route('admin.users.index') }}" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Search -->
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                            placeholder="Name or email..."
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>

                    <!-- Plan Filter -->
                    <div>
                        <label for="plan" class="block text-sm font-medium text-gray-700">Plan</label>
                        <select name="plan" id="plan"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Plans</option>
                            @foreach ($plans as $plan)
                                <option value="{{ $plan->id }}" {{ request('plan') == $plan->id ? 'selected' : '' }}>
                                    {{ $plan->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" id="status"
                            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">All Statuses</option>
                            @foreach ($statuses as $status)
                                <option value="{{ $status }}" {{ request('status') == $status ? 'selected' : '' }}>
                                    {{ ucfirst($status) }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Submit -->
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700">
                            Filter
                        </button>
                    </div>
                </div>

                @if (request()->hasAny(['search', 'plan', 'status']))
                    <div class="flex justify-end">
                        <a href="{{ route('admin.users.index') }}" class="text-gray-600 hover:text-gray-800">
                            Clear Filters
                        </a>
                    </div>
                @endif
            </form>
        </div>

        <div class="mt-6 overflow-hidden">
            <table id="users-table" class="w-full stripe hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Admin</th>
                        <th>Roles</th>
                        <th>Subscription</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- DataTables will fill this -->
                </tbody>
            </table>
        </div>
        </div>
    </x-admin.card>

    @push('styles')
        <style>
            .dataTables_wrapper .dataTables_filter {
                margin-bottom: 1rem;
            }
        </style>
    @endpush

    @push('scripts')
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script>
            $(function() {
                $('#users-table').DataTable({
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    ajax: "{{ route('admin.users.index') }}",
                    columns: [{
                            data: 'id',
                            name: 'id'
                        },
                        {
                            data: 'name',
                            name: 'name'
                        },
                        {
                            data: 'email',
                            name: 'email'
                        },
                        {
                            data: 'is_admin',
                            name: 'is_admin',
                            render: function(data) {
                                return data ?
                                    '<span class="px-2 py-1 text-xs font-semibold text-white bg-purple-500 rounded">Yes</span>' :
                                    '<span class="px-2 py-1 text-xs font-semibold text-white bg-gray-500 rounded">No</span>';
                            }
                        },
                        {
                            data: 'roles_list',
                            name: 'roles_list'
                        },
                        {
                            data: 'subscription_name',
                            name: 'subscription_name'
                        },
                        {
                            data: 'subscription_status',
                            name: 'subscription_status'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                    order: [
                        [0, 'asc']
                    ],
                });
            });
        </script>
    @endpush
@endsection
