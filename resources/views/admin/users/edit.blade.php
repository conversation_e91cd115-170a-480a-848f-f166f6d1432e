@extends('layouts.admin')

@section('title', 'Edit User')

@section('content')
    <x-admin.breadcrumbs :items="[
        ['label' => 'Users', 'route' => 'admin.users.index'],
        ['label' => $user->name, 'route' => 'admin.users.show', 'params' => ['user' => $user->id]],
        ['label' => 'Edit', 'route' => 'admin.users.edit', 'params' => ['user' => $user->id]],
    ]" />

    <div class="mb-4">
        <h1 class="text-2xl font-semibold">Edit User: {{ $user->name }}</h1>
    </div>

    <x-admin.card>
        <form action="{{ route('admin.users.update', $user) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                    <x-admin.input label="Name" name="name" required :value="old('name', $user->name)" />
                </div>

                <div>
                    <x-admin.input label="Email" name="email" type="email" required :value="old('email', $user->email)" />
                </div>

                <div>
                    <x-admin.input label="Password" name="password" type="password" autocomplete="new-password"
                        help="Leave blank to keep current password" />
                </div>

                <div>
                    <x-admin.input label="Confirm Password" name="password_confirmation" type="password"
                        autocomplete="new-password" />
                </div>

                <div>
                    <x-admin.select label="Preferred Language" name="preferred_language">
                        <option value="">Select Language</option>
                        <option value="en"
                            {{ old('preferred_language', $user->preferred_language) == 'en' ? 'selected' : '' }}>English
                        </option>
                        <option value="es"
                            {{ old('preferred_language', $user->preferred_language) == 'es' ? 'selected' : '' }}>Spanish
                        </option>
                        <option value="fr"
                            {{ old('preferred_language', $user->preferred_language) == 'fr' ? 'selected' : '' }}>French
                        </option>
                        <option value="de"
                            {{ old('preferred_language', $user->preferred_language) == 'de' ? 'selected' : '' }}>German
                        </option>
                    </x-admin.select>
                </div>

                <div class="md:col-span-2">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" name="is_admin" value="1" class="rounded text-blue-600"
                            {{ old('is_admin', $user->is_admin) ? 'checked' : '' }}>
                        <span class="text-gray-700 font-medium">Admin User</span>
                    </label>
                </div>

                <div class="md:col-span-2 mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Notification Preferences</label>
                    <div class="space-y-2">
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="notification_preferences[email]" value="1"
                                class="rounded text-blue-600"
                                {{ old('notification_preferences.email', $user->notification_preferences['email'] ?? true) ? 'checked' : '' }}>
                            <span class="text-gray-700">Email notifications</span>
                        </label>
                        <label class="flex items-center space-x-2 cursor-pointer">
                            <input type="checkbox" name="notification_preferences[in_app]" value="1"
                                class="rounded text-blue-600"
                                {{ old('notification_preferences.in_app', $user->notification_preferences['in_app'] ?? true) ? 'checked' : '' }}>
                            <span class="text-gray-700">In-app notifications</span>
                        </label>
                    </div>
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Roles</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-3 border p-3 rounded-md">
                        @foreach ($roles as $role)
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" name="roles[]" value="{{ $role->id }}"
                                    class="rounded text-blue-600"
                                    {{ in_array($role->id, old('roles', $userRoles)) ? 'checked' : '' }}>
                                <span class="text-gray-700">{{ $role->name }}</span>
                                @if ($role->is_default)
                                    <span class="px-2 py-0.5 text-xs text-white bg-green-500 rounded">Default</span>
                                @endif
                            </label>
                        @endforeach
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <a href="{{ route('admin.users.show', $user) }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    Update User
                </button>
            </div>
        </form>
    </x-admin.card>
@endsection
