@extends('layouts.admin')

@section('title', $role->name)

@section('content')
    <x-admin.breadcrumbs :items="[
        ['label' => 'Roles', 'route' => 'admin.roles.index'],
        ['label' => $role->name, 'route' => 'admin.roles.show', 'params' => ['role' => $role->id]],
    ]" />

    <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-semibold">Role Details: {{ $role->name }}</h1>
        <div class="flex space-x-2">
            <a href="{{ route('admin.roles.edit', $role) }}" class="btn-secondary">
                <i class="fas fa-edit mr-1"></i> Edit
            </a>
            <form action="{{ route('admin.roles.destroy', $role) }}" method="POST" class="inline-block"
                onsubmit="return confirm('Are you sure you want to delete this role?');">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn-danger">
                    <i class="fas fa-trash mr-1"></i> Delete
                </button>
            </form>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-1">
            <x-admin.card>
                <h2 class="text-lg font-semibold mb-4">Role Information</h2>

                <div class="space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Name</h3>
                        <p class="mt-1">{{ $role->name }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Slug</h3>
                        <p class="mt-1">{{ $role->slug }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Description</h3>
                        <p class="mt-1">{{ $role->description ?? 'No description' }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Default</h3>
                        <p class="mt-1">
                            @if ($role->is_default)
                                <span class="px-2 py-1 text-xs font-semibold text-white bg-green-500 rounded">Yes</span>
                            @else
                                <span class="px-2 py-1 text-xs font-semibold text-white bg-gray-500 rounded">No</span>
                            @endif
                        </p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Created</h3>
                        <p class="mt-1">{{ $role->created_at->format('M d, Y H:i') }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Last Updated</h3>
                        <p class="mt-1">{{ $role->updated_at->format('M d, Y H:i') }}</p>
                    </div>
                </div>
            </x-admin.card>

            <x-admin.card class="mt-6">
                <h2 class="text-lg font-semibold mb-4">Users with this Role ({{ $role->users->count() }})</h2>

                @if ($role->users->count() > 0)
                    <div class="space-y-3">
                        @foreach ($role->users->take(10) as $user)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <img src="https://ui-avatars.com/api/?name={{ urlencode($user->name) }}&color=7F9CF5&background=EBF4FF"
                                        alt="{{ $user->name }}" class="w-8 h-8 rounded-full mr-3">
                                    <div>
                                        <p class="font-medium">{{ $user->name }}</p>
                                        <p class="text-sm text-gray-500">{{ $user->email }}</p>
                                    </div>
                                </div>
                                <a href="{{ route('admin.users.show', $user) }}" class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        @endforeach

                        @if ($role->users->count() > 10)
                            <div class="mt-4 text-center">
                                <p class="text-sm text-gray-600">
                                    + {{ $role->users->count() - 10 }} more users
                                </p>
                            </div>
                        @endif
                    </div>
                @else
                    <p class="text-gray-500">No users have been assigned this role.</p>
                @endif
            </x-admin.card>
        </div>

        <div class="lg:col-span-2">
            <x-admin.card>
                <h2 class="text-lg font-semibold mb-4">Permissions ({{ $role->permissions->count() }})</h2>

                @if ($role->permissions->count() > 0)
                    <div class="space-y-6">
                        @foreach ($role->permissions->groupBy('group') as $group => $groupPermissions)
                            <div>
                                <h3 class="text-md font-semibold mb-2 pb-1 border-b">{{ ucfirst($group) }}</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                                    @foreach ($groupPermissions as $permission)
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-check-circle text-green-500"></i>
                                            <span>{{ $permission->name }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500">This role has no permissions assigned.</p>
                @endif
            </x-admin.card>
        </div>
    </div>
@endsection
