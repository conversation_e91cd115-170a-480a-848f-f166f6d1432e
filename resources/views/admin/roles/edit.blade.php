@extends('layouts.admin')

@section('title', 'Edit Role')

@section('content')
    <x-admin.breadcrumbs :items="[
        ['label' => 'Roles', 'route' => 'admin.roles.index'],
        ['label' => $role->name, 'route' => 'admin.roles.show', 'params' => ['role' => $role->id]],
        ['label' => 'Edit', 'route' => 'admin.roles.edit', 'params' => ['role' => $role->id]],
    ]" />

    <div class="mb-4">
        <h1 class="text-2xl font-semibold">Edit Role: {{ $role->name }}</h1>
    </div>

    <x-admin.card>
        <form action="{{ route('admin.roles.update', $role) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                    <x-admin.input label="Role Name" name="name" required :value="old('name', $role->name)" placeholder="e.g. Editor" />
                </div>

                <div>
                    <x-admin.input label="Role Slug" name="slug" required :value="old('slug', $role->slug)"
                        placeholder="e.g. editor (no spaces, lowercase)"
                        help="Used in code to identify the role, must be unique and contain only letters, numbers, and dashes." />
                </div>

                <div class="md:col-span-2">
                    <x-admin.textarea label="Description" name="description" :value="old('description', $role->description)"
                        placeholder="Describe the purpose of this role" rows="3" />
                </div>

                <div class="md:col-span-2">
                    <label class="flex items-center space-x-2 cursor-pointer">
                        <input type="checkbox" name="is_default" value="1" class="rounded text-blue-600"
                            {{ old('is_default', $role->is_default) ? 'checked' : '' }}>
                        <span class="text-gray-700 font-medium">Set as default role for new users</span>
                    </label>
                </div>
            </div>

            <div class="mt-6">
                <h2 class="text-lg font-semibold mb-3">Permissions</h2>

                <div class="space-y-6">
                    @foreach ($permissions->keys() as $group)
                        <div>
                            <h3 class="text-md font-bold mb-2 pb-1 border-b">{{ ucfirst($group) }}</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                @foreach ($permissions[$group] as $permission)
                                    <label class="flex items-center space-x-2 cursor-pointer">
                                        <input type="checkbox" name="permissions[]" value="{{ $permission->id }}"
                                            class="rounded text-blue-600"
                                            {{ in_array($permission->id, old('permissions', $rolePermissions)) ? 'checked' : '' }}>
                                        <span class="text-gray-700">{{ $permission->name }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <a href="{{ route('admin.roles.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    Update Role
                </button>
            </div>
        </form>
    </x-admin.card>
@endsection
