@extends('layouts.admin')

@section('title', __('admin.roles'))

@section('content')
    <x-admin.breadcrumbs :items="[['label' => __('admin.roles'), 'route' => 'admin.roles.index']]" />

    <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-semibold">{{ __('admin.role_management') }}</h1>
        <a href="{{ route('admin.roles.create') }}" class="btn-primary">
            <i class="fas fa-plus mr-1"></i> {{ __('admin.create_role') }}
        </a>
    </div>

    <x-admin.card>
        <div class="overflow-hidden">
            <table id="roles-table" class="w-full stripe hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Slug</th>
                        <th>Default</th>
                        <th>Users</th>
                        <th>Permissions</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- DataTables will fill this -->
                </tbody>
            </table>
        </div>
    </x-admin.card>

    @push('styles')
        <style>
            .dataTables_wrapper .dataTables_filter {
                margin-bottom: 1rem;
            }
        </style>
    @endpush

    @push('scripts')
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script>
            $(function() {
                $('#roles-table').DataTable({
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    ajax: "{{ route('admin.roles.index') }}",
                    columns: [{
                            data: 'id',
                            name: 'id'
                        },
                        {
                            data: 'name',
                            name: 'name'
                        },
                        {
                            data: 'slug',
                            name: 'slug'
                        },
                        {
                            data: 'is_default',
                            name: 'is_default',
                            render: function(data) {
                                return data ?
                                    '<span class="px-2 py-1 text-xs font-semibold text-white bg-green-500 rounded">Yes</span>' :
                                    '<span class="px-2 py-1 text-xs font-semibold text-white bg-gray-500 rounded">No</span>';
                            }
                        },
                        {
                            data: 'users_count',
                            name: 'users_count'
                        },
                        {
                            data: 'permissions_count',
                            name: 'permissions_count'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                    order: [
                        [0, 'asc']
                    ],
                });
            });
        </script>
    @endpush
@endsection
