@extends('layouts.admin')

@section('title', 'Edit Permission')

@section('content')
    <x-admin.breadcrumbs :items="[
        ['label' => 'Permissions', 'route' => 'admin.permissions.index'],
        [
            'label' => $permission->name,
            'route' => 'admin.permissions.show',
            'params' => ['permission' => $permission->id],
        ],
        ['label' => 'Edit', 'route' => 'admin.permissions.edit', 'params' => ['permission' => $permission->id]],
    ]" />

    <div class="mb-4">
        <h1 class="text-2xl font-semibold">Edit Permission: {{ $permission->name }}</h1>
    </div>

    <x-admin.card>
        <form action="{{ route('admin.permissions.update', $permission) }}" method="POST">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                    <x-admin.input label="Permission Name" name="name" required :value="old('name', $permission->name)"
                        placeholder="e.g. Create Users" />
                </div>

                <div>
                    <x-admin.input label="Permission Slug" name="slug" required :value="old('slug', $permission->slug)"
                        placeholder="e.g. create-users (no spaces, lowercase)"
                        help="Used in code to identify the permission, must be unique and contain only letters, numbers, and dashes." />
                </div>

                <div>
                    <x-admin.select label="Group" name="group" required>
                        <option value="">Select a group</option>
                        @foreach ($groups as $group)
                            <option value="{{ $group }}"
                                {{ old('group', $permission->group) == $group ? 'selected' : '' }}>
                                {{ ucfirst($group) }}
                            </option>
                        @endforeach
                        <option value="other"
                            {{ !in_array(old('group', $permission->group), $groups->toArray()) && old('group') != '' ? 'selected' : '' }}>
                            Other</option>
                    </x-admin.select>
                </div>

                <div class="hidden" id="new-group-container">
                    <x-admin.input label="New Group Name" name="new_group" :value="old('new_group')" placeholder="e.g. reports" />
                </div>

                <div class="md:col-span-2">
                    <x-admin.textarea label="Description" name="description" :value="old('description', $permission->description)"
                        placeholder="Describe what this permission allows" rows="3" />
                </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
                <a href="{{ route('admin.permissions.index') }}" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    Update Permission
                </button>
            </div>
        </form>
    </x-admin.card>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const groupSelect = document.querySelector('select[name="group"]');
                const newGroupContainer = document.getElementById('new-group-container');

                groupSelect.addEventListener('change', function() {
                    if (this.value === 'other') {
                        newGroupContainer.classList.remove('hidden');
                    } else {
                        newGroupContainer.classList.add('hidden');
                    }
                });

                // Initial check
                if (groupSelect.value === 'other') {
                    newGroupContainer.classList.remove('hidden');
                }
            });
        </script>
    @endpush
@endsection
