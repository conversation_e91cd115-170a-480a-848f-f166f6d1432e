@extends('layouts.admin')

@section('title', __('admin.permissions'))

@section('content')
    <x-admin.breadcrumbs :items="[['label' => __('admin.permissions'), 'route' => 'admin.permissions.index']]" />

    <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-semibold">{{ __('admin.permission_management') }}</h1>
        <a href="{{ route('admin.permissions.create') }}" class="btn-primary">
            <i class="fas fa-plus mr-1"></i> {{ __('admin.create_permission') }}
        </a>
    </div>

    <x-admin.card>
        <div class="overflow-hidden">
            <table id="permissions-table" class="w-full stripe hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Slug</th>
                        <th>Group</th>
                        <th>Roles</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- DataTables will fill this -->
                </tbody>
            </table>
        </div>
    </x-admin.card>

    @push('styles')
        <style>
            .dataTables_wrapper .dataTables_filter {
                margin-bottom: 1rem;
            }
        </style>
    @endpush

    @push('scripts')
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script>
            $(function() {
                $('#permissions-table').DataTable({
                    processing: true,
                    serverSide: true,
                    responsive: true,
                    ajax: "{{ route('admin.permissions.index') }}",
                    columns: [{
                            data: 'id',
                            name: 'id'
                        },
                        {
                            data: 'name',
                            name: 'name'
                        },
                        {
                            data: 'slug',
                            name: 'slug'
                        },
                        {
                            data: 'group',
                            name: 'group'
                        },
                        {
                            data: 'roles_count',
                            name: 'roles_count'
                        },
                        {
                            data: 'action',
                            name: 'action',
                            orderable: false,
                            searchable: false
                        }
                    ],
                    order: [
                        [0, 'asc']
                    ],
                });
            });
        </script>
    @endpush
@endsection
