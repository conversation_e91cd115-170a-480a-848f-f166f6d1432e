@extends('layouts.admin')

@section('title', $permission->name)

@section('content')
    <x-admin.breadcrumbs :items="[
        ['label' => 'Permissions', 'route' => 'admin.permissions.index'],
        [
            'label' => $permission->name,
            'route' => 'admin.permissions.show',
            'params' => ['permission' => $permission->id],
        ],
    ]" />

    <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-semibold">Permission Details: {{ $permission->name }}</h1>
        <div class="flex space-x-2">
            <a href="{{ route('admin.permissions.edit', $permission) }}" class="btn-secondary">
                <i class="fas fa-edit mr-1"></i> Edit
            </a>
            <form action="{{ route('admin.permissions.destroy', $permission) }}" method="POST" class="inline-block"
                onsubmit="return confirm('Are you sure you want to delete this permission?');">
                @csrf
                @method('DELETE')
                <button type="submit" class="btn-danger">
                    <i class="fas fa-trash mr-1"></i> Delete
                </button>
            </form>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div class="lg:col-span-1">
            <x-admin.card>
                <h2 class="text-lg font-semibold mb-4">Permission Information</h2>

                <div class="space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Name</h3>
                        <p class="mt-1">{{ $permission->name }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Slug</h3>
                        <p class="mt-1">{{ $permission->slug }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Group</h3>
                        <p class="mt-1">
                            <span class="px-2 py-1 text-xs font-semibold text-white bg-blue-500 rounded">
                                {{ ucfirst($permission->group) }}
                            </span>
                        </p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Description</h3>
                        <p class="mt-1">{{ $permission->description ?? 'No description' }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Created</h3>
                        <p class="mt-1">{{ $permission->created_at->format('M d, Y H:i') }}</p>
                    </div>

                    <div>
                        <h3 class="text-sm font-medium text-gray-500">Last Updated</h3>
                        <p class="mt-1">{{ $permission->updated_at->format('M d, Y H:i') }}</p>
                    </div>
                </div>
            </x-admin.card>
        </div>

        <div class="lg:col-span-2">
            <x-admin.card>
                <h2 class="text-lg font-semibold mb-4">Roles with this Permission ({{ $permission->roles->count() }})</h2>

                @if ($permission->roles->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Name
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Slug
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Default
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Users
                                    </th>
                                    <th scope="col"
                                        class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach ($permission->roles as $role)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $role->name }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">
                                                {{ $role->slug }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            @if ($role->is_default)
                                                <span
                                                    class="px-2 py-1 text-xs font-semibold text-white bg-green-500 rounded">Yes</span>
                                            @else
                                                <span
                                                    class="px-2 py-1 text-xs font-semibold text-white bg-gray-500 rounded">No</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">
                                                {{ $role->users->count() }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="{{ route('admin.roles.show', $role) }}"
                                                class="text-blue-600 hover:text-blue-900 mr-3">
                                                View
                                            </a>
                                            <a href="{{ route('admin.roles.edit', $role) }}"
                                                class="text-green-600 hover:text-green-900">
                                                Edit
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <p class="text-gray-500">This permission is not assigned to any roles.</p>
                @endif
            </x-admin.card>
        </div>
    </div>
@endsection
