@extends('layouts.admin')

    @section('title', 'Admin Dashboard')

    @section('breadcrumbs')
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <span class="text-gray-700 text-sm font-medium">Dashboard</span>
            </li>
        </ol>
    </nav>
@endsection

@section('content')
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p class="mt-2 text-gray-600">Overview of your Content Spark administration</p>
    </div>

    <!-- Overview Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Users Overview -->
        <div class="stat-card p-6 group">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-3xl font-bold text-gray-900 mt-2">{{ number_format($stats['users']['total']) }}</p>
                    <div class="flex items-center mt-3 text-sm">
                        <div class="flex items-center px-2 py-1 bg-green-100 text-green-700 rounded-full">
                            <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                                    clip-rule="evenodd"></path>
                            </svg>
                            <span class="font-medium">{{ $stats['users']['recent_registrations'] }}</span>
                        </div>
                        <span class="text-gray-500 ml-2">new this week</span>
                    </div>
                </div>
                <div
                    class="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-200">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                </div>
            </div>
            <div class="mt-6">
                <a href="{{ route('admin.users.index') }}" class="modern-button inline-flex items-center text-sm">
                    Manage Users
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- Pages Overview -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Pages</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['pages']['total']) }}</p>
                    <div class="flex items-center mt-2 text-sm">
                        <span class="text-green-600 font-medium">{{ $stats['pages']['published'] }}</span>
                        <span class="text-gray-500 ml-1">published</span>
                        <span class="text-gray-400 mx-1">•</span>
                        <span class="text-yellow-600 font-medium">{{ $stats['pages']['draft'] }}</span>
                        <span class="text-gray-500 ml-1">draft</span>
                    </div>
                </div>
                <div class="p-3 bg-green-100 rounded-full">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ route('admin.pages.index') }}"
                    class="inline-flex items-center text-sm font-medium text-green-600 hover:text-green-500">
                    Manage Pages
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- Plans Overview -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">
                        Subscription Plans</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['plans']['total']) }}</p>
                    <div class="flex items-center mt-2 text-sm">
                        <span class="text-green-600 font-medium">{{ $stats['plans']['active'] }}</span>
                        <span class="text-gray-500 ml-1">active</span>
                        <span class="text-gray-400 mx-1">•</span>
                        <span class="text-purple-600 font-medium">{{ $stats['plans']['featured'] }}</span>
                        <span class="text-gray-500 ml-1">featured</span>
                    </div>
                </div>
                <div class="p-3 bg-purple-100 rounded-full">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ route('admin.plans.index') }}"
                    class="inline-flex items-center text-sm font-medium text-purple-600 hover:text-purple-500">
                    Manage Plans
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- Testimonials Overview -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Testimonials</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($stats['testimonials']['total']) }}</p>
                    <div class="flex items-center mt-2 text-sm">
                        <span class="text-green-600 font-medium">{{ $stats['testimonials']['approved'] }}</span>
                        <span class="text-gray-500 ml-1">approved</span>
                        <span class="text-gray-400 mx-1">•</span>
                        <span class="text-orange-600 font-medium">{{ $stats['testimonials']['pending'] }}</span>
                        <span class="text-gray-500 ml-1">pending</span>
                    </div>
                </div>
                <div class="p-3 bg-orange-100 rounded-full">
                    <svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ route('admin.testimonials.index') }}"
                    class="inline-flex items-center text-sm font-medium text-orange-600 hover:text-orange-500">
                    Manage Testimonials
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>
    </div>

    <!-- Secondary Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Roles & Permissions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    Roles & Permissions</h3>
                <div class="p-2 bg-indigo-100 rounded-lg">
                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Total Roles</span>
                    <span class="font-semibold text-gray-900">{{ $stats['roles']['total'] }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Permissions</span>
                    <span class="font-semibold text-gray-900">{{ $stats['permissions']['total'] }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Permission Groups</span>
                    <span class="font-semibold text-gray-900">{{ $stats['permissions']['groups'] }}</span>
                </div>
            </div>
            <div class="mt-4 flex space-x-2">
                <a href="{{ route('admin.roles.index') }}"
                    class="flex-1 text-center px-3 py-2 bg-indigo-100 text-indigo-700 rounded-lg text-sm font-medium hover:bg-indigo-200 transition-colors">
                    Manage Roles
                </a>
                <a href="{{ route('admin.permissions.index') }}"
                    class="flex-1 text-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                    Permissions
                </a>
            </div>
        </div>

        <!-- Content Generation Stats -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    Content Generation</h3>
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Total Generations</span>
                    <span class="font-semibold text-gray-900">{{ number_format($stats['generations']['total']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">This Month</span>
                    <span
                        class="font-semibold text-green-600">{{ number_format($stats['generations']['this_month']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">This Week</span>
                    <span
                        class="font-semibold text-blue-600">{{ number_format($stats['generations']['this_week']) }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Today</span>
                    <span class="font-semibold text-purple-600">{{ number_format($stats['generations']['today']) }}</span>
                </div>
            </div>
        </div>

        <!-- User Breakdown -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">User Breakdown</h3>
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
            </div>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Admin Users</span>
                    <span class="font-semibold text-red-600">{{ $stats['users']['admins'] }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Regular Users</span>
                    <span class="font-semibold text-gray-900">{{ $stats['users']['regular_users'] }}</span>
                </div>
                <div class="flex justify-between items-center">
                    <span
                        class="text-sm text-gray-600">Active Subscriptions</span>
                    <span class="font-semibold text-green-600">{{ $stats['users']['active_subscriptions'] }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Recent Users -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Users</h3>
                    <a href="{{ route('admin.users.index') }}"
                        class="text-sm text-blue-600 hover:text-blue-500 font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="p-6">
                @if ($stats['recent_activity']['users']->count() > 0)
                    <div class="space-y-4">
                        @foreach ($stats['recent_activity']['users'] as $user)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                        <span
                                            class="text-blue-600 font-semibold text-sm">{{ substr($user->name, 0, 1) }}</span>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">{{ $user->name }}</p>
                                        <p class="text-xs text-gray-500">{{ $user->email }}</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-gray-500">{{ $user->created_at->diffForHumans() }}</p>
                                    @if ($user->is_admin)
                                        <span
                                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Admin
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <p class="text-gray-500 text-sm">No recent users found.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Recent Content Generations -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Recent Generations</h3>
                    <span class="text-sm text-gray-500">Last 5</span>
                </div>
            </div>
            <div class="p-6">
                @if ($stats['recent_activity']['generations']->count() > 0)
                    <div class="space-y-4">
                        @foreach ($stats['recent_activity']['generations'] as $generation)
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">{{ $generation->user->name }}</p>
                                    <p class="text-xs text-gray-500">
                                        {{ $generation->contentType->name ?? 'Unknown Type' }}</p>
                                    <p class="text-xs text-gray-400 mt-1">{{ Str::limit($generation->keywords, 40) }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-xs text-gray-500">{{ $generation->created_at->diffForHumans() }}</p>
                                    <span
                                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ strtoupper($generation->language) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <p class="text-gray-500 text-sm">
                            No recent generations found.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <a href="{{ route('admin.users.create') }}"
                    class="group block p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-gray-900">Add New User</h4>
                            <p class="text-sm text-gray-600">
                                Create a new user account</p>
                        </div>
                    </div>
                </a>

                <a href="{{ route('admin.pages.create') }}"
                    class="group block p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg group-hover:bg-green-200">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-gray-900">Create New Page
                            </h4>
                            <p class="text-sm text-gray-600">
                                Add a new page to the site</p>
                        </div>
                    </div>
                </a>

                <a href="{{ route('admin.testimonials.create') }}"
                    class="group block p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-orange-400 hover:bg-orange-50 transition-colors">
                    <div class="flex items-center">
                        <div class="p-2 bg-orange-100 rounded-lg group-hover:bg-orange-200">
                            <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h4 class="font-medium text-gray-900">Add Testimonial
                            </h4>
                            <p class="text-sm text-gray-600">
                                Create a new testimonial</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
    </div>
@endsection
