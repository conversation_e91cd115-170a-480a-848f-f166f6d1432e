<x-guest-layout>
    @php
        $pageSlug = 'privacy-policy';
        $page = \App\Models\Page::where('slug', $pageSlug)->first();
        $translation = $page?->translation(app()->getLocale());
    @endphp

    <x-slot name="head">
        @include('partials.seo-meta', [
            'page' => $page,
            'translation' => $translation,
            'schemaType' => 'webpage',
            'ogType' => 'website',
        ])
    </x-slot>

    <!-- Hero Section -->
    <div class="relative overflow-hidden bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 py-20">
        <!-- Animated Background Elements -->
        <div class="absolute inset-0">
            <!-- Geometric Shapes -->
            <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
            <div class="absolute top-32 right-20 w-16 h-16 bg-yellow-300/20 rounded-lg rotate-45 animate-bounce"></div>
            <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-pink-300/20 rounded-full animate-ping"></div>
            <div class="absolute bottom-32 right-1/3 w-24 h-24 bg-green-300/10 rounded-lg rotate-12 animate-pulse"></div>

            <!-- Privacy Icons -->
            <div
                class="absolute top-16 left-16 w-20 h-20 bg-white/10 rounded-full animate-pulse flex items-center justify-center">
                <svg class="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" />
                </svg>
            </div>
            <div
                class="absolute top-24 right-24 w-16 h-16 bg-blue-300/20 rounded-lg animate-bounce flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path
                        d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM12 17c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM15.1 8H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z" />
                </svg>
            </div>
            <div
                class="absolute bottom-20 left-1/4 w-18 h-18 bg-green-300/20 rounded-full animate-ping flex items-center justify-center">
                <svg class="w-9 h-9 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>

            <!-- Floating Particles -->
            <div class="absolute top-1/4 left-1/3 w-2 h-2 bg-white/30 rounded-full animate-float"></div>
            <div class="absolute top-1/2 right-1/4 w-3 h-3 bg-blue-200/40 rounded-full animate-float-delayed"></div>
            <div class="absolute bottom-1/3 left-1/2 w-2 h-2 bg-purple-200/30 rounded-full animate-float"></div>

            <!-- Grid Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute inset-0"
                    style="background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.3) 1px, transparent 0); background-size: 20px 20px;">
                </div>
            </div>

            <!-- Gradient Overlay -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
                    {{ $translation?->title ?? 'Privacy Policy' }}
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto drop-shadow-md">
                    {{ $translation?->description ?? 'Learn how Content Spark protects your privacy and handles your data. Our commitment to data security and user privacy.' }}
                </p>
            </div>
        </div>
    </div>

    <!-- Content Section -->
    <div class="py-20 bg-white">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="prose prose-lg max-w-none">
                @if ($translation && $translation->content)
                    {!! $translation->content !!}
                @else
                    <div class="privacy-content">
                        <h2>Privacy Policy</h2>
                        <p><strong>Last updated:</strong> {{ date('F j, Y') }}</p>

                        <h3>Information We Collect</h3>
                        <p>We collect information you provide directly to us, such as when you create an account, use
                            our services, or contact us for support.</p>
                        <ul>
                            <li>Account information (name, email, password)</li>
                            <li>Content you generate using our platform</li>
                            <li>Payment information (processed securely through Stripe)</li>
                            <li>Usage data and analytics</li>
                        </ul>

                        <h3>How We Use Your Information</h3>
                        <ul>
                            <li>To provide and improve our AI content generation services</li>
                            <li>To process payments and manage your account</li>
                            <li>To communicate with you about our services</li>
                            <li>To ensure platform security and prevent fraud</li>
                        </ul>

                        <h3>Data Security</h3>
                        <p>We implement industry-standard security measures to protect your personal information. All
                            data is encrypted in transit and at rest.</p>

                        <h3>Your Rights</h3>
                        <p>You have the right to access, update, or delete your personal information. Contact us at
                            <EMAIL> for any privacy-related requests.</p>

                        <h3>Contact Us</h3>
                        <p>If you have any questions about this Privacy Policy, please contact us at
                            <EMAIL>.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl font-bold text-white mb-4">
                {{ __('marketing.ready_to_start_title') }}
            </h2>
            <p class="text-xl text-blue-100 mb-8">
                {{ __('marketing.get_started_subtitle') }}
            </p>
            <a href="{{ route('register') }}"
                class="inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors duration-200">
                {{ __('marketing.get_started_title') }}
                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6">
                    </path>
                </svg>
            </a>
        </div>
    </div>
</x-guest-layout>
