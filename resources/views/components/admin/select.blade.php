<div class="mb-4">
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-1">
        {{ $label }}
        @if ($required ?? false)
            <span class="text-red-500">*</span>
        @endif
    </label>
    <select name="{{ $name }}" id="{{ $name }}" {{ $required ?? false ? 'required' : '' }}
        @class([
            'w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50',
            'border-red-300' => $errors->has($name),
        ])>
        @if ($placeholder ?? false)
            <option value="">{{ $placeholder }}</option>
        @endif

        @if (isset($options) && is_array($options))
            @foreach ($options as $optionValue => $optionLabel)
                <option value="{{ $optionValue }}" {{ ($value ?? old($name)) == $optionValue ? 'selected' : '' }}>
                    {{ $optionLabel }}
                </option>
            @endforeach
        @else
            {{ $slot }}
        @endif
    </select>

    @error($name)
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
</div>
