@php
    // Support both 'items' and 'links' parameters for backward compatibility
    $breadcrumbItems = $items ?? ($links ?? []);
@endphp

<nav class="flex" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <!-- Dashboard Home -->
        <li class="inline-flex items-center">
            <a href="{{ route('admin.index') }}"
                class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors duration-200">
                <svg class="w-3 h-3 mr-2.5" fill="currentColor" viewBox="0 0 20 20">
                    <path
                        d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z">
                    </path>
                </svg>
                {{ __('admin.dashboard') }}
            </a>
        </li>

        <!-- Dynamic Breadcrumb Items -->
        @foreach ($breadcrumbItems as $item)
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"></path>
                    </svg>
                    @if (!$loop->last && isset($item['route']))
                        @if (isset($item['params']))
                            <a href="{{ route($item['route'], $item['params']) }}"
                                class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 transition-colors duration-200">
                                {{ $item['label'] }}
                            </a>
                        @else
                            <a href="{{ route($item['route']) }}"
                                class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 transition-colors duration-200">
                                {{ $item['label'] }}
                            </a>
                        @endif
                    @else
                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ $item['label'] }}</span>
                    @endif
                </div>
            </li>
        @endforeach

        <!-- Support for old format (associative array) -->
        @if (isset($links) && is_array($links) && !isset($items))
            @foreach ($links as $title => $url)
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                        @if (!$loop->last)
                            <a href="{{ $url }}"
                                class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 transition-colors duration-200">
                                {{ $title }}
                            </a>
                        @else
                            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{{ $title }}</span>
                        @endif
                    </div>
                </li>
            @endforeach
        @endif
    </ol>
</nav>
