<div class="mb-4">
    <label for="{{ $name }}" class="block text-sm font-medium text-gray-700 mb-1">
        {{ $label }}
        @if ($required ?? false)
            <span class="text-red-500">*</span>
        @endif
    </label>
    <textarea name="{{ $name }}" id="{{ $name }}" placeholder="{{ $placeholder ?? '' }}"
        {{ $required ?? false ? 'required' : '' }} rows="{{ $rows ?? 3 }}" @class([
            'w-full rounded-md shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50',
            'border-red-300' => $errors->has($name),
        ])>{{ $value ?? old($name) }}</textarea>

    @error($name)
        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
    @enderror
</div>
