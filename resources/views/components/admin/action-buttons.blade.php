@php
    // Support both 'resource' and 'route' parameters for backward compatibility
    $routePrefix = $resource ?? ($route ?? '');
@endphp

<div class="flex items-center space-x-2">
    @if (isset($showView) && $showView !== false)
        <a href="{{ route($routePrefix . '.show', $model) }}"
            class="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-200"
            title="View">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                </path>
            </svg>
        </a>
    @endif

    <a href="{{ route($routePrefix . '.edit', $model) }}"
        class="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-200"
        title="Edit">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
            </path>
        </svg>
    </a>

    @if (isset($toggleRoute) && isset($toggleField))
        <button onclick="toggleStatus('{{ route($toggleRoute, $model) }}', '{{ $toggleField }}', this)"
            class="p-2 text-yellow-600 hover:text-yellow-800 hover:bg-yellow-50 rounded-lg transition-all duration-200"
            title="Toggle {{ ucfirst($toggleField) }}">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
            </svg>
        </button>
    @endif

    <form action="{{ route($routePrefix . '.destroy', $model) }}" method="POST" class="inline-block delete-form">
        @csrf
        @method('DELETE')
        <button type="submit"
            class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-200"
            title="Delete"
            onclick="return confirm('Are you sure you want to delete this item? This action cannot be undone.')">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                </path>
            </svg>
        </button>
    </form>
</div>

@push('scripts')
    <script>
        function toggleStatus(url, field, button) {
            fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Refresh the DataTable if it exists
                        if (typeof window.dataTable !== 'undefined') {
                            window.dataTable.draw(false);
                        } else if ($.fn.DataTable) {
                            $('.dataTable').DataTable().draw(false);
                        }

                        // Show success message
                        if (data.message) {
                            alert(data.message);
                        }
                    } else {
                        alert('Error: ' + (data.message || 'Something went wrong'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error: Something went wrong');
                });
        }
    </script>
@endpush
