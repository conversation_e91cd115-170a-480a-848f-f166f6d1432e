@props(['class' => ''])

<div class="relative {{ $class }}" x-data="{ open: false }">
    <button @click="open = !open" @click.away="open = false"
        class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
            </path>
        </svg>
        <span>
            @switch(app()->getLocale())
                @case('ar')
                    العربية
                @break

                @case('fr')
                    Français
                @break

                @case('ru')
                    Русский
                @break

                @case('es')
                    Español
                @break

                @case('pt')
                    Português
                @break

                @default
                    English
            @endswitch
        </span>
        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none"
            stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
    </button>

    <div x-show="open" x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100"
        x-transition:leave-end="transform opacity-0 scale-95"
        class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
        <div class="py-1">
            @foreach (\App\Helpers\LocalizedUrlHelper::getSupportedLocales() as $locale)
                <a href="{{ \App\Helpers\LocalizedUrlHelper::currentPageInLocale($locale) }}"
                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 {{ app()->getLocale() === $locale ? 'bg-gray-50 font-medium' : '' }}">
                    <span class="mr-3">{{ \App\Helpers\LocalizedUrlHelper::getLocaleFlag($locale) }}</span>
                    {{ \App\Helpers\LocalizedUrlHelper::getLocaleDisplayName($locale) }}
                    @if (app()->getLocale() === $locale)
                        <svg class="ml-auto w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </a>
            @endforeach

        </div>
    </div>
</div>
