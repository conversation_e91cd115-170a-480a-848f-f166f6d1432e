<?php

/**
 * Test script to verify Content Spark features implementation
 */

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\Plan;
use App\Models\Subscription;
use App\Models\ContentType;

echo "=== Content Spark Features Test ===\n\n";

// Test 1: Plan Billing Frequency Enhancement
echo "1. Testing Plan Billing Frequency Enhancement...\n";
$plans = Plan::all();
foreach ($plans as $plan) {
    echo "   - {$plan->name}:\n";
    echo "     * Monthly: \${$plan->monthly_price}\n";
    echo "     * Annual: \${$plan->annual_price}\n";
    if (!$plan->isFree()) {
        echo "     * Annual Savings: \${$plan->getAnnualSavings()} ({$plan->getAnnualSavingsPercentage()}%)\n";
    }
    echo "\n";
}

// Test 2: Subscription Billing Frequency
echo "2. Testing Subscription Billing Frequency...\n";
$subscriptions = Subscription::with('plan')->take(3)->get();
foreach ($subscriptions as $subscription) {
    echo "   - User {$subscription->user_id}: {$subscription->plan->name} ({$subscription->billing_frequency})\n";
    echo "     * Price: \${$subscription->stripe_price}\n";
    echo "     * Ends: {$subscription->ends_at}\n";
}
echo "\n";

// Test 3: Content Types with Categories
echo "3. Testing Content Types with Categories...\n";
$contentTypes = ContentType::all();
$categories = $contentTypes->pluck('category')->unique()->filter();

echo "   Categories found: " . $categories->implode(', ') . "\n\n";

foreach ($categories as $category) {
    echo "   {$category}:\n";
    $typesInCategory = $contentTypes->where('category', $category);
    foreach ($typesInCategory as $type) {
        $status = $type->is_active ? 'Active' : 'Inactive';
        echo "     - {$type->name} ({$status})\n";
    }
    echo "\n";
}

// Test 4: New Content Types
echo "4. Testing New Content Types...\n";
$newContentTypes = [
    'Website Content Generator',
    'SEO Content Optimizer', 
    'YouTube Captions',
    'Instagram Captions',
    'Coming Soon Placeholder'
];

foreach ($newContentTypes as $typeName) {
    $contentType = ContentType::where('name', $typeName)->first();
    if ($contentType) {
        echo "   ✓ {$typeName} - Found\n";
        echo "     * Category: {$contentType->category}\n";
        echo "     * Status: " . ($contentType->is_active ? 'Active' : 'Inactive') . "\n";
        echo "     * Prompt: " . substr($contentType->prompt_template, 0, 100) . "...\n";
    } else {
        echo "   ✗ {$typeName} - Not Found\n";
    }
    echo "\n";
}

// Test 5: Admin Controller Routes
echo "5. Testing Admin Controller Routes...\n";
$routes = [
    'admin.content-types.index',
    'admin.content-types.create', 
    'admin.content-types.store',
    'admin.content-types.show',
    'admin.content-types.edit',
    'admin.content-types.update',
    'admin.content-types.destroy',
    'admin.content-types.toggle-status'
];

foreach ($routes as $routeName) {
    try {
        $url = route($routeName, ['contentType' => 1]);
        echo "   ✓ {$routeName} - Route exists\n";
    } catch (Exception $e) {
        echo "   ✗ {$routeName} - Route missing\n";
    }
}
echo "\n";

// Test 6: Database Schema
echo "6. Testing Database Schema...\n";
try {
    // Test subscription billing_frequency column
    $subscription = Subscription::first();
    if ($subscription && isset($subscription->billing_frequency)) {
        echo "   ✓ Subscription billing_frequency column exists\n";
    } else {
        echo "   ✗ Subscription billing_frequency column missing\n";
    }

    // Test content_types category column
    $contentType = ContentType::first();
    if ($contentType && property_exists($contentType, 'category')) {
        echo "   ✓ ContentType category column exists\n";
    } else {
        echo "   ✗ ContentType category column missing\n";
    }
} catch (Exception $e) {
    echo "   ✗ Database schema error: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 7: Model Methods
echo "7. Testing Model Methods...\n";
$plan = Plan::where('monthly_price', '>', 0)->first();
if ($plan) {
    echo "   ✓ Plan::getPrice() - Monthly: \${$plan->getPrice('monthly')}\n";
    echo "   ✓ Plan::getPrice() - Annual: \${$plan->getPrice('annual')}\n";
    echo "   ✓ Plan::getAnnualSavings() - \${$plan->getAnnualSavings()}\n";
    echo "   ✓ Plan::getAnnualSavingsPercentage() - {$plan->getAnnualSavingsPercentage()}%\n";
} else {
    echo "   ✗ No paid plans found for testing\n";
}
echo "\n";

// Test 8: File Structure
echo "8. Testing File Structure...\n";
$files = [
    'app/Http/Controllers/Admin/AdminContentTypeController.php',
    'resources/views/admin/content-types/index.blade.php',
    'resources/views/admin/content-types/create.blade.php',
    'resources/views/admin/content-types/edit.blade.php',
    'resources/views/admin/content-types/show.blade.php',
    'resources/views/components/admin-layout.blade.php',
    'resources/js/pricing-toggle.js',
    'database/migrations/2024_01_03_000001_add_billing_frequency_to_subscriptions.php',
    'database/migrations/2024_01_03_000002_add_category_to_content_types.php'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        echo "   ✓ {$file} - Exists\n";
    } else {
        echo "   ✗ {$file} - Missing\n";
    }
}
echo "\n";

echo "=== Test Complete ===\n";
echo "\nNext Steps:\n";
echo "1. Run migrations: php artisan migrate\n";
echo "2. Seed content types: php artisan db:seed --class=ContentTypeSeeder\n";
echo "3. Build assets: npm run build\n";
echo "4. Test admin panel: /admin/content-types\n";
echo "5. Test pricing toggle on welcome page\n";
echo "6. Test billing frequency in checkout flow\n";
