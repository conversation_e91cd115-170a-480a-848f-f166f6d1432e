
6. **الواجهة الأمامية:**
   - تصميم بسيط باستخدام Tailwind CSS
   - صفحة رئيسية عامة فيها:
     - نبذة عن المشروع
     - مميزات المنصة
     - الخطط والأسعار
     - الأسئلة الشائعة
     - نموذج الاشتراك
   - لوحة مستخدم فيها:
     - نموذج توليد المحتوى
     - سجل التوليدات
     - إدارة الاشتراك

     
7. **لغات الموقع:**
   - واجهة تدعم الإنجليزية والعربية (استخدم Laravel Localization)


ثانيًا: خريطة الموقع (Sitemap)
🏠 صفحة عامة
/ — الصفحة الرئيسية (تعريف بالمشروع + CTA)
/pricing — صفحة الأسعار
/faq — الأسئلة الشائعة
/login — تسجيل الدخول
/register — إنشاء حساب