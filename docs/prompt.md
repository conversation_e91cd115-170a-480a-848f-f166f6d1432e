أريد منك إنشاء مشروع SaaS باستخدام Laravel باسم "Content Spark"، وهو مولد محتوى ذكي متعدد اللغات موجه لمنشئي المحتوى والمسوقين.

🔧 متطلبات المشروع:

1. **الإعداد الأساسي:**
   - Laravel 11
   - Jetstream أو Breeze لنظام الدخول والتسجيل
   - لوحة تحكم للمستخدم ولوحة للإدارة
   - قاعدة بيانات MySQL


📌 المطلوب:
- هيكل الملفات الكامل (Models, Controllers, Views, Routes)
- migration لكل الجداول
- seeders لاختبار النظام
- تنظيم المشروع بما يتوافق مع أفضل ممارسات Laravel
- تعليمات تشغيل المشروع محليًا (README)

## MVP Timeline
The goal is to launch an MVP version in less than a week.
