<?php

require_once 'vendor/autoload.php';

use App\Models\Faq;
use App\Models\FaqTranslation;

// Test creating a FAQ
$faq = Faq::create([
    'sort_order' => 1,
    'is_active' => true,
]);

// Test creating translations
FaqTranslation::create([
    'faq_id' => $faq->id,
    'locale' => 'en',
    'question' => 'How does the AI content generation work?',
    'answer' => 'Our AI uses advanced language models trained on millions of high-quality content examples.',
]);

FaqTranslation::create([
    'faq_id' => $faq->id,
    'locale' => 'ar',
    'question' => 'كيف يعمل إنشاء المحتوى بالذكاء الاصطناعي؟',
    'answer' => 'يستخدم الذكاء الاصطناعي لدينا نماذج لغوية متقدمة مدربة على ملايين أمثلة المحتوى عالي الجودة.',
]);

echo "FAQ created successfully with ID: " . $faq->id . "\n";
echo "English question: " . $faq->getQuestion('en') . "\n";
echo "Arabic question: " . $faq->getQuestion('ar') . "\n";
