<?php

/**
 * Debug script to test webhook functionality
 */

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\Plan;
use App\Models\Subscription;

echo "=== Webhook Debug Test ===\n\n";

// Test 1: Check if plans exist
echo "1. Checking plans...\n";
$plans = Plan::all();
foreach ($plans as $plan) {
    echo "   - {$plan->name} (ID: {$plan->id}, Price: \${$plan->monthly_price})\n";
}

// Test 2: Check if users have subscriptions
echo "\n2. Checking user subscriptions...\n";
$users = User::with('subscription.plan')->take(3)->get();
foreach ($users as $user) {
    if ($user->subscription) {
        echo "   - {$user->name}: {$user->subscription->plan->name} (Status: {$user->subscription->stripe_status})\n";
    } else {
        echo "   - {$user->name}: No subscription\n";
    }
}

// Test 3: Simulate webhook data structure
echo "\n3. Simulating webhook data structure...\n";
$mockSession = (object) [
    'id' => 'cs_test_example',
    'metadata' => (object) [
        'user_id' => '1',
        'plan_id' => '2'
    ],
    'client_reference_id' => '1',
    'subscription' => 'sub_test_example',
    'amount_total' => 999, // $9.99 in cents
];

echo "   Mock session data:\n";
echo "   - Session ID: {$mockSession->id}\n";
echo "   - User ID: {$mockSession->metadata->user_id}\n";
echo "   - Plan ID: {$mockSession->metadata->plan_id}\n";
echo "   - Subscription ID: {$mockSession->subscription}\n";
echo "   - Amount: \$" . ($mockSession->amount_total / 100) . "\n";

// Test 4: Check database schema
echo "\n4. Checking database schema...\n";
try {
    $subscription = Subscription::first();
    if ($subscription) {
        echo "   - Sample subscription columns:\n";
        $attributes = $subscription->getAttributes();
        foreach ($attributes as $key => $value) {
            echo "     * {$key}: " . (is_null($value) ? 'NULL' : $value) . "\n";
        }
    } else {
        echo "   - No subscriptions found in database\n";
    }
} catch (Exception $e) {
    echo "   - Error: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Test Complete ===\n";
echo "\nNext steps:\n";
echo "1. Run the migration: php artisan migrate\n";
echo "2. Test a real Stripe webhook\n";
echo "3. Check the logs: tail -f storage/logs/laravel.log\n";
