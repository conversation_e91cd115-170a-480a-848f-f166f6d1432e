<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\AdminPageController;
use App\Http\Controllers\Admin\AdminRoleController;
use App\Http\Controllers\Api\PlanController as ApiPlanController;
use App\Http\Controllers\MarketingController;
use App\Http\Controllers\ToolController;
use App\Http\Controllers\Admin\AdminContentTypeController;
use App\Http\Controllers\Admin\AdminPermissionController;
use App\Http\Controllers\Admin\AdminPlanController;
use App\Http\Controllers\Admin\AdminTestimonialController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\BillingController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\PricingController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\GenerationController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\WebhookController;
use Illuminate\Support\Facades\Route;

// Public routes
// Default route (English) - Marketing Landing Page
Route::get('/', [MarketingController::class, 'landing'])->name('home');

// Localized Marketing Landing Pages
Route::prefix('{locale}')->where(['locale' => 'en|ar|fr|es|pt|ru'])->group(function () {
    Route::get('/', [MarketingController::class, 'landing'])->name('marketing.landing');
    Route::post('/newsletter', [MarketingController::class, 'newsletter'])->name('marketing.newsletter');

    // Localized marketing pages
    Route::get('/pricing', [PricingController::class, 'plans'])->name('marketing.pricing');
    Route::get('/faq', function () {
        $locale = app()->getLocale();
        $faqs = \App\Models\Faq::active()
            ->ordered()
            ->whereHas('translations', function ($query) use ($locale) {
                $query->where('locale', $locale);
            })
            ->with(['translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->get();
        return view('faq', compact('faqs'));
    })->name('marketing.faq');

    Route::get('/about-us', function () {
        return view('about');
    })->name('marketing.about');

    Route::get('/contact-us', function () {
        return view('contact');
    })->name('marketing.contact');

    Route::get('/privacy-policy', function () {
        return view('privacy-policy');
    })->name('marketing.privacy-policy');

    Route::get('/terms-of-service', function () {
        return view('terms-of-service');
    })->name('marketing.terms-of-service');
});

Route::get('/pricing', [PricingController::class, 'plans'])->name('pricing');

Route::get('/faq', function () {
    $locale = app()->getLocale();
    $faqs = \App\Models\Faq::active()
        ->ordered()
        ->whereHas('translations', function ($query) use ($locale) {
            $query->where('locale', $locale);
        })
        ->with(['translations' => function ($query) use ($locale) {
            $query->where('locale', $locale);
        }])
        ->get();
    return view('faq', compact('faqs'));
})->name('faq');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return view('terms-of-service');
})->name('terms-of-service');

// SEO Routes
Route::get('/sitemap.xml', [\App\Http\Controllers\SitemapController::class, 'index'])->name('sitemap');
Route::get('/robots.txt', [\App\Http\Controllers\SitemapController::class, 'robots'])->name('robots');

// Language switching
Route::get('/language/{locale}', [LanguageController::class, 'switch'])->name('language.switch');

// Content type landing pages
Route::get('/content-types/{contentType}', [PageController::class, 'contentType'])->name('content-types.show');

// Dynamic pages (catch-all route - should be last)
Route::get('/pages/{slug}', [PageController::class, 'show'])->name('pages.show');

// API Routes (public)
Route::prefix('api')->name('api.')->group(function () {
    Route::get('/plans', [ApiPlanController::class, 'index'])->name('plans.index');
    Route::get('/plans/{plan}', [ApiPlanController::class, 'show'])->name('plans.show');
    Route::get('/plans-comparison', [ApiPlanController::class, 'compare'])->name('plans.compare');

    // Language API
    Route::get('/languages', [\App\Http\Controllers\Api\LanguageController::class, 'index'])->name('languages.index');
    Route::get('/languages/current', [\App\Http\Controllers\Api\LanguageController::class, 'current'])->name('languages.current');
});

// Stripe Webhook (no CSRF protection needed)
Route::post('/stripe/webhook', [WebhookController::class, 'handleStripeWebhook'])->withoutMiddleware(['web']);

// Checkout routes (accessible to unauthenticated users for proper redirect flow)
Route::post('/checkout/{plan}', [PricingController::class, 'checkout'])->name('pricing.checkout');
Route::get('/checkout/success', [PricingController::class, 'success'])->name('pricing.success');
Route::get('/checkout/cancel', [PricingController::class, 'cancel'])->name('pricing.cancel');

// Public Tool Landing Pages
Route::get('/tool/content-creation', [ToolController::class, 'contentCreation'])->name('tools.content-creation');
Route::get('/tool/ecommerce', [ToolController::class, 'ecommerce'])->name('tools.ecommerce');
Route::get('/tool/social-media', [ToolController::class, 'socialMedia'])->name('tools.social-media');
Route::get('/tool/marketing', [ToolController::class, 'marketing'])->name('tools.marketing');
Route::get('/tool/seo', [ToolController::class, 'seo'])->name('tools.seo');
Route::get('/tool/website-content', [ToolController::class, 'websiteContent'])->name('tools.website-content');
Route::get('/tool/video-content', [ToolController::class, 'videoContent'])->name('tools.video-content');

// Authenticated user routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Content Generation - Category-specific routes
    Route::get('/generate', [GenerationController::class, 'dashboard'])->name('generations.dashboard');
    Route::get('/generate/content-creation', [GenerationController::class, 'contentCreation'])->name('generations.content-creation');
    Route::get('/generate/ecommerce', [GenerationController::class, 'ecommerce'])->name('generations.ecommerce');
    Route::get('/generate/social-media', [GenerationController::class, 'socialMedia'])->name('generations.social-media');
    Route::get('/generate/marketing', [GenerationController::class, 'marketing'])->name('generations.marketing');
    Route::get('/generate/seo', [GenerationController::class, 'seo'])->name('generations.seo');
    Route::get('/generate/website-content', [GenerationController::class, 'websiteContent'])->name('generations.website-content');
    Route::get('/generate/video-content', [GenerationController::class, 'videoContent'])->name('generations.video-content');
    Route::post('/generate', [GenerationController::class, 'store'])->name('generations.store');
    Route::get('/generations/{generation}', [GenerationController::class, 'show'])->name('generations.show');
    Route::get('/history', [GenerationController::class, 'index'])->name('generations.index');
    Route::post('/generations/{generation}/regenerate', [GenerationController::class, 'regenerate'])->name('generations.regenerate');
    Route::delete('/generations/{generation}', [GenerationController::class, 'destroy'])->name('generations.destroy');
    Route::post('/generations/bulk-delete', [GenerationController::class, 'bulkDestroy'])->name('generations.bulk-destroy');
    Route::get('/generations/export', [GenerationController::class, 'export'])->name('generations.export');

    // Pricing & Subscription
    Route::get('/plans', [PricingController::class, 'index'])->name('pricing.index');

    // Settings
    Route::get('/settings', [SettingsController::class, 'index'])->name('settings.index');
    Route::patch('/settings', [SettingsController::class, 'update'])->name('settings.update');
    Route::put('/settings/notifications', [SettingsController::class, 'updateNotifications'])->name('settings.notifications');
    Route::get('/settings/export-data', [SettingsController::class, 'exportData'])->name('settings.export-data');

    // Billing
    Route::get('/billing', [BillingController::class, 'index'])->name('billing.index');
    Route::post('/billing/portal', [BillingController::class, 'createPortalSession'])->name('billing.portal');
    Route::post('/billing/cancel', [BillingController::class, 'cancelSubscription'])->name('billing.cancel');
    Route::post('/billing/resume', [BillingController::class, 'resumeSubscription'])->name('billing.resume');
    Route::get('/billing/invoice/{invoice}', [BillingController::class, 'downloadInvoice'])->name('billing.invoice');

    // Profile (from Breeze)
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'index'])->name('index');

    // User management
    Route::resource('users', AdminUserController::class);
    Route::post('/users/bulk-delete', [AdminUserController::class, 'bulkDestroy'])->name('users.bulk-destroy');

    // Roles management
    Route::resource('roles', AdminRoleController::class);

    // Permissions management
    Route::resource('permissions', AdminPermissionController::class);

    // Plan management
    Route::resource('plans', AdminPlanController::class);
    Route::patch('plans/{plan}/toggle-active', [AdminPlanController::class, 'toggleActive'])->name('plans.toggle-active');
    Route::patch('plans/{plan}/toggle-featured', [AdminPlanController::class, 'toggleFeatured'])->name('plans.toggle-featured');

    // Pages management
    Route::resource('pages', AdminPageController::class);
    Route::patch('pages/{page}/toggle-status', [AdminPageController::class, 'toggleStatus'])->name('pages.toggle-status');
    Route::post('pages/bulk-destroy', [AdminPageController::class, 'bulkDestroy'])->name('pages.bulk-destroy');

    // Content Types management
    Route::resource('content-types', AdminContentTypeController::class);
    Route::patch('content-types/{contentType}/toggle-status', [AdminContentTypeController::class, 'toggleStatus'])->name('content-types.toggle-status');
    Route::post('content-types/bulk-destroy', [AdminContentTypeController::class, 'bulkDestroy'])->name('content-types.bulk-destroy');

    // Testimonials management
    Route::resource('testimonials', AdminTestimonialController::class);
    Route::patch('testimonials/{testimonial}/toggle-status', [AdminTestimonialController::class, 'toggleStatus'])->name('testimonials.toggle-status');
    Route::patch('testimonials/{testimonial}/toggle-featured', [AdminTestimonialController::class, 'toggleFeatured'])->name('testimonials.toggle-featured');
    Route::post('testimonials/bulk-destroy', [AdminTestimonialController::class, 'bulkDestroy'])->name('testimonials.bulk-destroy');

    // FAQ management
    Route::resource('faqs', AdminFaqController::class);
    Route::patch('faqs/{faq}/toggle-status', [AdminFaqController::class, 'toggleStatus'])->name('faqs.toggle-status');
    Route::post('faqs/bulk-destroy', [AdminFaqController::class, 'bulkDestroy'])->name('faqs.bulk-destroy');
});

require __DIR__ . '/auth.php';
require __DIR__ . '/register_payment.php';
