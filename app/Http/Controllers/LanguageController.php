<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    /**
     * Switch the application language.
     */
    public function switch(Request $request, string $locale): RedirectResponse
    {
        // Validate the locale
        if (!in_array($locale, ['en', 'ar', 'fr', 'ru', 'es', 'pt', 'de', 'it'])) {
            abort(404);
        }

        // Set the application locale
        App::setLocale($locale);

        // Store the locale in session
        Session::put('locale', $locale);

        // Update user preference if authenticated
        if ($request->user()) {
            $request->user()->update(['preferred_language' => $locale]);
        }

        return redirect()->back();
    }
}
