<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Stripe\Stripe;
use Stripe\BillingPortal\Session as BillingPortalSession;

class Billing<PERSON>ontroller extends Controller
{
    protected SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Display billing management page.
     */
    public function index(): View
    {
        $user = auth()->user();
        $subscription = $user->subscription()->with('plan')->first();
        $plans = Plan::orderBy('monthly_price')->get();
        
        // Get usage statistics
        $remainingGenerations = $this->subscriptionService->getRemainingGenerations($user);
        $totalGenerations = $user->generations()->count();
        $thisMonthGenerations = $user->generations()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();

        return view('billing.index', compact(
            'user',
            'subscription', 
            'plans',
            'remainingGenerations',
            'totalGenerations',
            'thisMonthGenerations'
        ));
    }

    /**
     * Create Stripe billing portal session.
     */
    public function createPortalSession(): RedirectResponse
    {
        $user = auth()->user();
        
        if (!$user->subscription || !$user->subscription->stripe_id) {
            return redirect()->route('billing.index')
                ->with('error', 'No active subscription found.');
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $session = BillingPortalSession::create([
                'customer' => $user->stripe_customer_id ?? $this->getOrCreateStripeCustomer($user),
                'return_url' => route('billing.index'),
            ]);

            return redirect($session->url);
        } catch (\Exception $e) {
            \Log::error('Failed to create billing portal session', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('billing.index')
                ->with('error', 'Unable to access billing portal. Please try again.');
        }
    }

    /**
     * Cancel subscription.
     */
    public function cancelSubscription(Request $request): RedirectResponse
    {
        $user = auth()->user();
        $subscription = $user->subscription;

        if (!$subscription || !$subscription->stripe_id) {
            return redirect()->route('billing.index')
                ->with('error', 'No active subscription found.');
        }

        $validated = $request->validate([
            'cancellation_reason' => 'nullable|string|max:500',
            'feedback' => 'nullable|string|max:1000',
        ]);

        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            
            // Cancel at period end to allow user to continue using until billing cycle ends
            \Stripe\Subscription::update($subscription->stripe_id, [
                'cancel_at_period_end' => true,
                'metadata' => [
                    'cancellation_reason' => $validated['cancellation_reason'] ?? '',
                    'feedback' => $validated['feedback'] ?? '',
                ],
            ]);

            // Update local subscription record
            $subscription->update([
                'stripe_status' => 'active', // Still active until period end
                'cancellation_reason' => $validated['cancellation_reason'],
                'cancelled_at' => now(),
            ]);

            return redirect()->route('billing.index')
                ->with('success', 'Your subscription has been cancelled and will remain active until ' . 
                       $subscription->ends_at->format('M j, Y') . '.');
        } catch (\Exception $e) {
            \Log::error('Failed to cancel subscription', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('billing.index')
                ->with('error', 'Unable to cancel subscription. Please try again or contact support.');
        }
    }

    /**
     * Resume cancelled subscription.
     */
    public function resumeSubscription(): RedirectResponse
    {
        $user = auth()->user();
        $subscription = $user->subscription;

        if (!$subscription || !$subscription->stripe_id) {
            return redirect()->route('billing.index')
                ->with('error', 'No subscription found.');
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            
            // Resume subscription by removing cancel_at_period_end
            \Stripe\Subscription::update($subscription->stripe_id, [
                'cancel_at_period_end' => false,
            ]);

            // Update local subscription record
            $subscription->update([
                'cancelled_at' => null,
                'cancellation_reason' => null,
            ]);

            return redirect()->route('billing.index')
                ->with('success', 'Your subscription has been resumed successfully.');
        } catch (\Exception $e) {
            \Log::error('Failed to resume subscription', [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('billing.index')
                ->with('error', 'Unable to resume subscription. Please try again or contact support.');
        }
    }

    /**
     * Download invoice.
     */
    public function downloadInvoice(Request $request, string $invoiceId)
    {
        $user = auth()->user();

        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            
            $invoice = \Stripe\Invoice::retrieve($invoiceId);
            
            // Verify the invoice belongs to this user
            if ($invoice->customer !== ($user->stripe_customer_id ?? '')) {
                abort(403, 'Unauthorized access to invoice.');
            }

            return redirect($invoice->invoice_pdf);
        } catch (\Exception $e) {
            \Log::error('Failed to download invoice', [
                'user_id' => $user->id,
                'invoice_id' => $invoiceId,
                'error' => $e->getMessage(),
            ]);

            return redirect()->route('billing.index')
                ->with('error', 'Unable to download invoice. Please try again.');
        }
    }

    /**
     * Get billing history.
     */
    public function getBillingHistory(): array
    {
        $user = auth()->user();

        if (!$user->stripe_customer_id) {
            return [];
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            
            $invoices = \Stripe\Invoice::all([
                'customer' => $user->stripe_customer_id,
                'limit' => 12,
            ]);

            return $invoices->data;
        } catch (\Exception $e) {
            \Log::error('Failed to retrieve billing history', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    /**
     * Get or create Stripe customer.
     */
    private function getOrCreateStripeCustomer($user): string
    {
        if ($user->stripe_customer_id) {
            return $user->stripe_customer_id;
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));
            
            $customer = \Stripe\Customer::create([
                'email' => $user->email,
                'name' => $user->name,
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);

            $user->update(['stripe_customer_id' => $customer->id]);

            return $customer->id;
        } catch (\Exception $e) {
            \Log::error('Failed to create Stripe customer', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}
