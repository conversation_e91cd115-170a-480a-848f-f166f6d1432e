<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PlanController extends Controller
{
    /**
     * Get all active plans
     */
    public function index(): JsonResponse
    {
        $plans = Plan::where('is_active', true)
            ->orderBy('monthly_price')
            ->get()
            ->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'slug' => $plan->slug,
                    'description' => $plan->description,
                    'monthly_price' => $plan->monthly_price,
                    'annual_price' => $plan->annual_price,
                    'monthly_generations_limit' => $plan->monthly_generations_limit,
                    'is_featured' => $plan->is_featured,
                    'is_free' => $plan->isFree(),
                    'features' => $this->getPlanFeatures($plan),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $plans,
        ]);
    }

    /**
     * Get a specific plan
     */
    public function show(Plan $plan): JsonResponse
    {
        if (!$plan->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Plan not found or inactive',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $plan->id,
                'name' => $plan->name,
                'slug' => $plan->slug,
                'description' => $plan->description,
                'monthly_price' => $plan->monthly_price,
                'annual_price' => $plan->annual_price,
                'monthly_generations_limit' => $plan->monthly_generations_limit,
                'is_featured' => $plan->is_featured,
                'is_free' => $plan->isFree(),
                'features' => $this->getPlanFeatures($plan),
            ],
        ]);
    }

    /**
     * Get plan comparison data
     */
    public function compare(): JsonResponse
    {
        $plans = Plan::where('is_active', true)
            ->orderBy('monthly_price')
            ->get();

        $comparison = [
            'plans' => $plans->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'slug' => $plan->slug,
                    'monthly_price' => $plan->monthly_price,
                    'annual_price' => $plan->annual_price,
                    'is_featured' => $plan->is_featured,
                    'is_free' => $plan->isFree(),
                ];
            }),
            'features' => [
                'generations' => $plans->mapWithKeys(function ($plan) {
                    return [$plan->id => $plan->monthly_generations_limit === -1 ? 'Unlimited' : number_format($plan->monthly_generations_limit)];
                }),
                'content_types' => $plans->mapWithKeys(function ($plan) {
                    return [$plan->id => $plan->isFree() ? 'Basic' : 'All'];
                }),
                'languages' => $plans->mapWithKeys(function ($plan) {
                    return [$plan->id => 'Multi-language'];
                }),
                'support' => $plans->mapWithKeys(function ($plan) {
                    return [$plan->id => $plan->isFree() ? 'Email' : 'Priority'];
                }),
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $comparison,
        ]);
    }

    /**
     * Get plan features based on plan type
     */
    private function getPlanFeatures(Plan $plan): array
    {
        $features = [
            'generations' => $plan->monthly_generations_limit === -1 
                ? 'Unlimited generations' 
                : number_format($plan->monthly_generations_limit) . ' generations/month',
            'content_types' => $plan->isFree() ? 'Basic content types' : 'All content types',
            'languages' => 'Multi-language support',
            'support' => $plan->isFree() ? 'Email support' : 'Priority support',
        ];

        if (!$plan->isFree()) {
            $features['api_access'] = 'API access';
            $features['export'] = 'Export to multiple formats';
            $features['analytics'] = 'Usage analytics';
        }

        if ($plan->monthly_price >= 49) {
            $features['team_access'] = 'Team collaboration';
            $features['custom_integrations'] = 'Custom integrations';
            $features['dedicated_support'] = 'Dedicated support manager';
        }

        return $features;
    }
}
