<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Generation;
use App\Models\Plan;
use App\Models\Page;
use App\Models\ContentType;
use App\Models\Testimonial;
use App\Models\Faq;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class AdminApiController extends Controller
{
    /**
     * Get admin dashboard statistics.
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'users' => [
                'total' => User::count(),
                'admins' => User::where('is_admin', true)->count(),
                'regular' => User::where('is_admin', false)->count(),
                'new_this_week' => User::where('created_at', '>=', now()->subDays(7))->count(),
                'active_subscriptions' => User::whereHas('subscriptions', function ($query) {
                    $query->where('stripe_status', 'active');
                })->count(),
            ],
            'content' => [
                'total_generations' => Generation::count(),
                'this_month' => Generation::whereMonth('created_at', now()->month)->count(),
                'this_week' => Generation::where('created_at', '>=', now()->subDays(7))->count(),
                'today' => Generation::whereDate('created_at', today())->count(),
                'content_types' => ContentType::count(),
            ],
            'pages' => [
                'total' => Page::count(),
                'published' => Page::where('status', 'published')->count(),
                'draft' => Page::where('status', 'draft')->count(),
            ],
            'plans' => [
                'total' => Plan::count(),
                'active' => Plan::where('is_active', true)->count(),
                'featured' => Plan::where('is_featured', true)->count(),
            ],
            'testimonials' => [
                'total' => Testimonial::count(),
                'approved' => Testimonial::where('is_active', true)->count(),
                'pending' => Testimonial::where('is_active', false)->count(),
                'featured' => Testimonial::where('is_featured', true)->count(),
            ],
            'faqs' => [
                'total' => Faq::count(),
                'active' => Faq::where('is_active', true)->count(),
                'inactive' => Faq::where('is_active', false)->count(),
            ],
        ];

        return response()->json($stats);
    }

    /**
     * Search users for admin.
     */
    public function searchUsers(Request $request): JsonResponse
    {
        $query = $request->get('q', '');
        $limit = $request->get('limit', 10);

        $users = User::where('name', 'LIKE', "%{$query}%")
            ->orWhere('email', 'LIKE', "%{$query}%")
            ->limit($limit)
            ->get(['id', 'name', 'email', 'is_admin', 'created_at']);

        return response()->json($users);
    }

    /**
     * Export data for admin.
     */
    public function export(Request $request, string $type): JsonResponse
    {
        $allowedTypes = ['users', 'generations', 'pages', 'plans', 'testimonials', 'faqs'];
        
        if (!in_array($type, $allowedTypes)) {
            return response()->json(['error' => 'Invalid export type'], 400);
        }

        $data = [];
        
        switch ($type) {
            case 'users':
                $data = User::with('roles')->get();
                break;
            case 'generations':
                $data = Generation::with(['user', 'contentType'])->get();
                break;
            case 'pages':
                $data = Page::with('translations')->get();
                break;
            case 'plans':
                $data = Plan::all();
                break;
            case 'testimonials':
                $data = Testimonial::all();
                break;
            case 'faqs':
                $data = Faq::with('translations')->get();
                break;
        }

        return response()->json([
            'type' => $type,
            'count' => $data->count(),
            'data' => $data,
            'exported_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get recent activity for admin dashboard.
     */
    public function recentActivity(): JsonResponse
    {
        $activity = [
            'recent_users' => User::latest()->take(5)->get(['id', 'name', 'email', 'created_at']),
            'recent_generations' => Generation::with(['user:id,name', 'contentType:id,name'])
                ->latest()
                ->take(5)
                ->get(['id', 'user_id', 'content_type_id', 'created_at']),
            'recent_pages' => Page::with('translations')
                ->latest()
                ->take(5)
                ->get(['id', 'slug', 'status', 'created_at']),
        ];

        return response()->json($activity);
    }

    /**
     * Get analytics data for charts.
     */
    public function analytics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);

        $analytics = [
            'user_registrations' => User::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
                ->where('created_at', '>=', $startDate)
                ->groupBy('date')
                ->orderBy('date')
                ->get(),

            'content_generations' => Generation::select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as count')
            )
                ->where('created_at', '>=', $startDate)
                ->groupBy('date')
                ->orderBy('date')
                ->get(),

            'popular_content_types' => Generation::select(
                'content_types.name',
                DB::raw('COUNT(*) as count')
            )
                ->join('content_types', 'generations.content_type_id', '=', 'content_types.id')
                ->where('generations.created_at', '>=', $startDate)
                ->groupBy('content_types.id', 'content_types.name')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
        ];

        return response()->json($analytics);
    }

    /**
     * Get system health status.
     */
    public function health(): JsonResponse
    {
        $health = [
            'database' => $this->checkDatabase(),
            'storage' => $this->checkStorage(),
            'cache' => $this->checkCache(),
            'queue' => $this->checkQueue(),
        ];

        $overallStatus = collect($health)->every(fn($status) => $status['status'] === 'ok') ? 'healthy' : 'issues';

        return response()->json([
            'overall_status' => $overallStatus,
            'checks' => $health,
            'checked_at' => now()->toISOString(),
        ]);
    }

    /**
     * Check database connectivity.
     */
    private function checkDatabase(): array
    {
        try {
            DB::connection()->getPdo();
            return ['status' => 'ok', 'message' => 'Database connection successful'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check storage accessibility.
     */
    private function checkStorage(): array
    {
        try {
            $testFile = storage_path('app/health-check.txt');
            file_put_contents($testFile, 'test');
            unlink($testFile);
            return ['status' => 'ok', 'message' => 'Storage is writable'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Storage check failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check cache functionality.
     */
    private function checkCache(): array
    {
        try {
            cache()->put('health-check', 'test', 60);
            $value = cache()->get('health-check');
            cache()->forget('health-check');
            
            if ($value === 'test') {
                return ['status' => 'ok', 'message' => 'Cache is working'];
            } else {
                return ['status' => 'error', 'message' => 'Cache test failed'];
            }
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Cache check failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check queue status.
     */
    private function checkQueue(): array
    {
        try {
            // Simple queue check - in a real app you might want to dispatch a test job
            return ['status' => 'ok', 'message' => 'Queue system available'];
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => 'Queue check failed: ' . $e->getMessage()];
        }
    }
}
