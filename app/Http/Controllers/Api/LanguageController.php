<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

class LanguageController extends Controller
{
    /**
     * Get all available languages.
     */
    public function index(): JsonResponse
    {
        $languages = [
            'en' => [
                'code' => 'en',
                'name' => 'English',
                'native_name' => 'English',
                'flag' => '🇺🇸',
                'rtl' => false,
            ],
            'ar' => [
                'code' => 'ar',
                'name' => 'Arabic',
                'native_name' => 'العربية',
                'flag' => '🇸🇦',
                'rtl' => true,
            ],
            'fr' => [
                'code' => 'fr',
                'name' => 'French',
                'native_name' => 'Français',
                'flag' => '🇫🇷',
                'rtl' => false,
            ],
            'ru' => [
                'code' => 'ru',
                'name' => 'Russian',
                'native_name' => 'Русский',
                'flag' => '🇷🇺',
                'rtl' => false,
            ],
            'es' => [
                'code' => 'es',
                'name' => 'Spanish',
                'native_name' => 'Español',
                'flag' => '🇪🇸',
                'rtl' => false,
            ],
            'pt' => [
                'code' => 'pt',
                'name' => 'Portuguese',
                'native_name' => 'Português',
                'flag' => '🇵🇹',
                'rtl' => false,
            ],
            'de' => [
                'code' => 'de',
                'name' => 'German',
                'native_name' => 'Deutsch',
                'flag' => '🇩🇪',
                'rtl' => false,
            ],
            'it' => [
                'code' => 'it',
                'name' => 'Italian',
                'native_name' => 'Italiano',
                'flag' => '🇮🇹',
                'rtl' => false,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'languages' => array_values($languages),
                'current_locale' => app()->getLocale(),
            ],
        ]);
    }

    /**
     * Get current language information.
     */
    public function current(): JsonResponse
    {
        $currentLocale = app()->getLocale();
        
        $languages = [
            'en' => ['code' => 'en', 'name' => 'English', 'native_name' => 'English', 'flag' => '🇺🇸', 'rtl' => false],
            'ar' => ['code' => 'ar', 'name' => 'Arabic', 'native_name' => 'العربية', 'flag' => '🇸🇦', 'rtl' => true],
            'fr' => ['code' => 'fr', 'name' => 'French', 'native_name' => 'Français', 'flag' => '🇫🇷', 'rtl' => false],
            'ru' => ['code' => 'ru', 'name' => 'Russian', 'native_name' => 'Русский', 'flag' => '🇷🇺', 'rtl' => false],
            'es' => ['code' => 'es', 'name' => 'Spanish', 'native_name' => 'Español', 'flag' => '🇪🇸', 'rtl' => false],
            'pt' => ['code' => 'pt', 'name' => 'Portuguese', 'native_name' => 'Português', 'flag' => '🇵🇹', 'rtl' => false],
            'de' => ['code' => 'de', 'name' => 'German', 'native_name' => 'Deutsch', 'flag' => '🇩🇪', 'rtl' => false],
            'it' => ['code' => 'it', 'name' => 'Italian', 'native_name' => 'Italiano', 'flag' => '🇮🇹', 'rtl' => false],
        ];

        $currentLanguage = $languages[$currentLocale] ?? $languages['en'];

        return response()->json([
            'success' => true,
            'data' => $currentLanguage,
        ]);
    }
}
