<?php

namespace App\Http\Controllers;

use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\View\View;

class DashboardController extends Controller
{
    protected SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function index(Request $request): View
    {
        $user = $request->user();
        $subscription = $user->subscription()->with('plan')->first();
        $recentGenerations = $user->generations()->with('contentType')->latest()->take(5)->get();
        $remainingGenerations = $this->subscriptionService->getRemainingGenerations($user);
        $totalGenerations = $user->generations()->count();

        return view('dashboard', [
            'user' => $user,
            'subscription' => $subscription,
            'recentGenerations' => $recentGenerations,
            'remainingGenerations' => $remainingGenerations,
            'totalGenerations' => $totalGenerations,
        ]);
    }
}
