<?php

namespace App\Http\Controllers;

use App\Models\ContentType;
use App\Models\Generation;
use App\Services\OpenAiService;
use App\Services\SubscriptionService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class GenerationController extends Controller
{
    use AuthorizesRequests;
    protected OpenAiService $openAiService;
    protected SubscriptionService $subscriptionService;

    public function __construct(
        OpenAiService $openAiService,
        SubscriptionService $subscriptionService
    ) {
        $this->openAiService = $openAiService;
        $this->subscriptionService = $subscriptionService;
    }

    /**
     * Show the generation dashboard with category cards
     */
    public function dashboard(): View
    {
        $user = auth()->user();
        $remainingGenerations = $this->subscriptionService->getRemainingGenerations($user);

        $categories = [
            'Content Creation' => [
                'route' => 'generations.content-creation',
                'icon' => '📝',
                'description' => 'Create blog posts, articles, and written content',
                'tools' => ['Blog Post Outline', 'YouTube Video Ideas']
            ],
            'E-commerce' => [
                'route' => 'generations.ecommerce',
                'icon' => '🛒',
                'description' => 'Generate product descriptions and e-commerce content',
                'tools' => ['Product Description']
            ],
            'Social Media' => [
                'route' => 'generations.social-media',
                'icon' => '📱',
                'description' => 'Create engaging social media posts and captions',
                'tools' => ['Social Media Post', 'Instagram Captions']
            ],
            'Marketing' => [
                'route' => 'generations.marketing',
                'icon' => '📧',
                'description' => 'Generate marketing emails and promotional content',
                'tools' => ['Email Marketing']
            ],
            'SEO' => [
                'route' => 'generations.seo',
                'icon' => '🔍',
                'description' => 'Create SEO-optimized content and meta descriptions',
                'tools' => ['SEO Content Optimizer']
            ],
            'Website Content' => [
                'route' => 'generations.website-content',
                'icon' => '🌐',
                'description' => 'Generate website copy and landing page content',
                'tools' => ['Website Content Generator']
            ],
            'Video Content' => [
                'route' => 'generations.video-content',
                'icon' => '🎥',
                'description' => 'Create video scripts and captions',
                'tools' => ['YouTube Captions']
            ],
        ];

        return view('generations.dashboard', compact('categories', 'remainingGenerations'));
    }

    /**
     * Show category-specific generation page
     */
    private function showCategoryPage(string $category): View
    {
        $contentTypes = ContentType::where('is_active', true)
            ->where('category', $category)
            ->orderBy('name')
            ->get();

        $languages = [
            'ar' => ['name' => 'العربية', 'flag' => '🇸🇦'],
            'en' => ['name' => 'English', 'flag' => '🇺🇸'],
            'fr' => ['name' => 'Français', 'flag' => '🇫🇷'],
            'es' => ['name' => 'Español', 'flag' => '🇪🇸'],
            'pt' => ['name' => 'Português', 'flag' => '🇵🇹'],
            'ru' => ['name' => 'Русский', 'flag' => '🇷🇺'],
        ];

        $user = auth()->user();
        $remainingGenerations = $this->subscriptionService->getRemainingGenerations($user);

        return view('generations.category', [
            'contentTypes' => $contentTypes,
            'languages' => $languages,
            'remainingGenerations' => $remainingGenerations,
            'category' => $category,
        ]);
    }

    public function contentCreation(): View
    {
        return $this->showCategoryPage('Content Creation');
    }

    public function ecommerce(): View
    {
        return $this->showCategoryPage('E-commerce');
    }

    public function socialMedia(): View
    {
        return $this->showCategoryPage('Social Media');
    }

    public function marketing(): View
    {
        return $this->showCategoryPage('Marketing');
    }

    public function seo(): View
    {
        return $this->showCategoryPage('SEO');
    }

    public function websiteContent(): View
    {
        return $this->showCategoryPage('Website Content');
    }

    public function videoContent(): View
    {
        return $this->showCategoryPage('Video Content');
    }

    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'content_type_id' => 'required|exists:content_types,id',
            'language' => 'required|in:ar,en,fr,es,pt,ru',
            'keywords' => 'required|string|min:3|max:500',
        ], [
            'content_type_id.required' => 'Please select a content type.',
            'content_type_id.exists' => 'The selected content type is invalid.',
            'language.required' => 'Please select a language.',
            'language.in' => 'The selected language is not supported.',
            'keywords.required' => 'Please enter keywords or topic.',
            'keywords.min' => 'Keywords must be at least 3 characters.',
            'keywords.max' => 'Keywords cannot exceed 500 characters.',
        ]);

        $user = $request->user();

        // Check if user can generate content
        if (!$this->subscriptionService->canGenerateContent($user)) {
            return redirect()->route('pricing.index')
                ->with('error', 'You have reached your generation limit. Please upgrade your plan to continue generating content.');
        }

        // Verify content type is active
        $contentType = ContentType::where('id', $validated['content_type_id'])
            ->where('is_active', true)
            ->first();

        if (!$contentType) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'The selected content type is not available.');
        }

        try {
            $generation = $this->openAiService->generateContent(
                $user,
                $contentType,
                $validated['language'],
                $validated['keywords']
            );

            $this->subscriptionService->incrementGenerationCount($user);

            return redirect()->route('generations.show', $generation)
                ->with('success', 'Content generated successfully! You can copy, edit, or save it to your history.');
        } catch (\Exception $e) {
            \Log::error('Content generation failed', [
                'user_id' => $user->id,
                'content_type_id' => $validated['content_type_id'],
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to generate content. Please try again. If the problem persists, contact support.');
        }
    }

    public function show(Generation $generation): View
    {
        $this->authorize('view', $generation);

        return view('generations.show', compact('generation'));
    }

    public function index(Request $request): View
    {
        $user = $request->user();
        $query = $user->generations()->with('contentType');

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('keywords', 'like', "%{$search}%")
                    ->orWhere('result', 'like', "%{$search}%")
                    ->orWhereHas('contentType', function ($ct) use ($search) {
                        $ct->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Filter by content type
        if ($request->filled('content_type')) {
            $query->where('content_type_id', $request->get('content_type'));
        }

        // Filter by language
        if ($request->filled('language')) {
            $query->where('language', $request->get('language'));
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $generations = $query->latest()->paginate(15);

        // Get filter options
        $contentTypes = ContentType::where('is_active', true)->orderBy('name')->get();
        $languages = [
            'ar' => 'العربية',
            'en' => 'English',
            'fr' => 'Français',
            'es' => 'Español',
            'pt' => 'Português',
            'ru' => 'Русский',
        ];

        return view('generations.index', compact('generations', 'contentTypes', 'languages'));
    }

    /**
     * Regenerate content based on existing generation
     */
    public function regenerate(Generation $generation): RedirectResponse
    {
        $this->authorize('view', $generation);

        $user = auth()->user();

        // Check if user can generate content
        if (!$this->subscriptionService->canGenerateContent($user)) {
            return redirect()->route('pricing.index')
                ->with('error', 'You have reached your generation limit. Please upgrade your plan to continue generating content.');
        }

        try {
            $newGeneration = $this->openAiService->generateContent(
                $user,
                $generation->contentType,
                $generation->language,
                $generation->keywords
            );

            $this->subscriptionService->incrementGenerationCount($user);

            return redirect()->route('generations.show', $newGeneration)
                ->with('success', 'Content regenerated successfully!');
        } catch (\Exception $e) {
            \Log::error('Content regeneration failed', [
                'user_id' => $user->id,
                'original_generation_id' => $generation->id,
                'error' => $e->getMessage(),
            ]);

            return redirect()->back()
                ->with('error', 'Failed to regenerate content. Please try again.');
        }
    }

    /**
     * Delete a generation
     */
    public function destroy(Generation $generation): RedirectResponse
    {
        $this->authorize('view', $generation);

        $generation->delete();

        return redirect()->route('generations.index')
            ->with('success', 'Generation deleted successfully.');
    }

    /**
     * Bulk delete generations
     */
    public function bulkDestroy(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'generation_ids' => 'required|array',
            'generation_ids.*' => 'exists:generations,id',
        ]);

        $user = auth()->user();
        $deletedCount = $user->generations()
            ->whereIn('id', $validated['generation_ids'])
            ->delete();

        return redirect()->route('generations.index')
            ->with('success', "Successfully deleted {$deletedCount} generation(s).");
    }

    /**
     * Export generations to various formats
     */
    public function export(Request $request)
    {
        $validated = $request->validate([
            'format' => 'required|in:txt,csv,json',
            'generation_ids' => 'nullable|array',
            'generation_ids.*' => 'exists:generations,id',
        ]);

        $user = auth()->user();
        $query = $user->generations()->with('contentType');

        if (!empty($validated['generation_ids'])) {
            $query->whereIn('id', $validated['generation_ids']);
        }

        $generations = $query->latest()->get();

        switch ($validated['format']) {
            case 'txt':
                return $this->exportAsTxt($generations);
            case 'csv':
                return $this->exportAsCsv($generations);
            case 'json':
                return $this->exportAsJson($generations);
        }
    }

    private function exportAsTxt($generations)
    {
        $content = "Content Generations Export\n";
        $content .= "Generated on: " . now()->format('Y-m-d H:i:s') . "\n";
        $content .= str_repeat("=", 50) . "\n\n";

        foreach ($generations as $generation) {
            $content .= "Content Type: {$generation->contentType->name}\n";
            $content .= "Language: {$generation->language}\n";
            $content .= "Keywords: {$generation->keywords}\n";
            $content .= "Created: {$generation->created_at->format('Y-m-d H:i:s')}\n";
            $content .= str_repeat("-", 30) . "\n";
            $content .= $generation->result . "\n";
            $content .= str_repeat("=", 50) . "\n\n";
        }

        return response($content)
            ->header('Content-Type', 'text/plain')
            ->header('Content-Disposition', 'attachment; filename="generations_' . now()->format('Y-m-d') . '.txt"');
    }

    private function exportAsCsv($generations)
    {
        $filename = 'generations_' . now()->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($generations) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Content Type', 'Language', 'Keywords', 'Result', 'Created At']);

            foreach ($generations as $generation) {
                fputcsv($file, [
                    $generation->contentType->name,
                    $generation->language,
                    $generation->keywords,
                    $generation->result,
                    $generation->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function exportAsJson($generations)
    {
        $data = $generations->map(function ($generation) {
            return [
                'id' => $generation->id,
                'content_type' => $generation->contentType->name,
                'language' => $generation->language,
                'keywords' => $generation->keywords,
                'result' => $generation->result,
                'created_at' => $generation->created_at->toISOString(),
            ];
        });

        return response()->json([
            'export_date' => now()->toISOString(),
            'total_generations' => $data->count(),
            'generations' => $data,
        ])->header('Content-Disposition', 'attachment; filename="generations_' . now()->format('Y-m-d') . '.json"');
    }
}
