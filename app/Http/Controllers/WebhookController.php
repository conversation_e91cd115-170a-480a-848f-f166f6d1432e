<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Stripe\Stripe;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;

class WebhookController extends Controller
{
    public function handleStripeWebhook(Request $request): Response
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $payload = $request->getContent();
        $sigHeader = $request->header('Stripe-Signature');
        $endpointSecret = config('services.stripe.webhook.secret');

        try {
            $event = Webhook::constructEvent($payload, $sigHeader, $endpointSecret);
        } catch (\UnexpectedValueException $e) {
            // Invalid payload
            return response('Invalid payload', 400);
        } catch (SignatureVerificationException $e) {
            // Invalid signature
            return response('Invalid signature', 400);
        }

        // Handle the event
        switch ($event->type) {
            case 'checkout.session.completed':
                $this->handleCheckoutSessionCompleted($event->data->object);
                break;
            case 'invoice.payment_succeeded':
                $this->handleInvoicePaymentSucceeded($event->data->object);
                break;
            case 'invoice.payment_failed':
                $this->handleInvoicePaymentFailed($event->data->object);
                break;
            case 'customer.subscription.deleted':
                $this->handleSubscriptionDeleted($event->data->object);
                break;
            default:
                // Unhandled event type
                break;
        }

        return response('Webhook handled', 200);
    }

    private function handleCheckoutSessionCompleted($session)
    {
        // Log the webhook event for debugging
        Log::info('Stripe checkout session completed', [
            'session_id' => $session->id,
            'metadata' => $session->metadata,
            'client_reference_id' => $session->client_reference_id,
            'subscription' => $session->subscription,
        ]);

        $userId = $session->metadata->user_id ?? $session->client_reference_id;
        $planId = $session->metadata->plan_id;
        $billingFrequency = $session->metadata->billing_frequency ?? 'monthly';

        if (!$userId || !$planId) {
            Log::error('Missing user_id or plan_id in webhook', [
                'user_id' => $userId,
                'plan_id' => $planId,
                'billing_frequency' => $billingFrequency,
                'session_id' => $session->id,
            ]);
            return;
        }

        $user = User::find($userId);
        $plan = Plan::find($planId);

        if (!$user || !$plan) {
            Log::error('User or plan not found', [
                'user_id' => $userId,
                'plan_id' => $planId,
                'user_exists' => $user ? 'yes' : 'no',
                'plan_exists' => $plan ? 'yes' : 'no',
            ]);
            return;
        }

        // Update or create subscription
        $subscription = $user->subscription;

        $endDate = $billingFrequency === 'annual' ? now()->addYear() : now()->addMonth();
        $price = $session->amount_total ? ($session->amount_total / 100) : $plan->getPrice($billingFrequency);

        if ($subscription) {
            // Update existing subscription (upgrade/downgrade)
            $subscription->update([
                'plan_id' => $plan->id,
                'stripe_id' => $session->subscription,
                'stripe_status' => 'active',
                'stripe_price' => $price,
                'billing_frequency' => $billingFrequency,
                'starts_at' => now(),
                'ends_at' => $endDate,
                'generations_used' => 0,
            ]);
            Log::info('Updated existing subscription', [
                'subscription_id' => $subscription->id,
                'plan' => $plan->name,
                'billing_frequency' => $billingFrequency
            ]);
        } else {
            // Create new subscription (new user with paid plan)
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'stripe_id' => $session->subscription,
                'stripe_status' => 'active',
                'stripe_price' => $price,
                'billing_frequency' => $billingFrequency,
                'starts_at' => now(),
                'ends_at' => $endDate,
                'generations_used' => 0,
            ]);
            Log::info('Created new subscription', [
                'subscription_id' => $subscription->id,
                'plan' => $plan->name,
                'billing_frequency' => $billingFrequency
            ]);
        }

        // Clear any session data related to plan selection
        if (session()->has('selected_plan_after_registration')) {
            session()->forget('selected_plan_after_registration');
        }
    }

    private function handleInvoicePaymentSucceeded($invoice)
    {
        $subscriptionId = $invoice->subscription;

        if (!$subscriptionId) {
            return;
        }

        $subscription = Subscription::where('stripe_id', $subscriptionId)->first();

        if ($subscription) {
            $endDate = $subscription->billing_frequency === 'annual' ? now()->addYear() : now()->addMonth();

            $subscription->update([
                'stripe_status' => 'active',
                'ends_at' => $endDate,
                'generations_used' => 0, // Reset for new billing period
            ]);
            Log::info('Invoice payment succeeded', [
                'subscription_id' => $subscription->id,
                'billing_frequency' => $subscription->billing_frequency
            ]);
        }
    }

    private function handleInvoicePaymentFailed($invoice)
    {
        $subscriptionId = $invoice->subscription;

        if (!$subscriptionId) {
            return;
        }

        $subscription = Subscription::where('stripe_id', $subscriptionId)->first();

        if ($subscription) {
            $subscription->update([
                'stripe_status' => 'past_due',
            ]);
            Log::warning('Invoice payment failed', ['subscription_id' => $subscription->id]);
        }
    }

    private function handleSubscriptionDeleted($stripeSubscription)
    {
        $subscription = Subscription::where('stripe_id', $stripeSubscription->id)->first();

        if ($subscription) {
            // Move user back to free plan
            $freePlan = Plan::where('slug', 'free')->first();

            if ($freePlan) {
                $subscription->update([
                    'plan_id' => $freePlan->id,
                    'stripe_status' => 'active',
                    'stripe_id' => null,
                    'stripe_price' => null,
                    'starts_at' => now(),
                    'ends_at' => now()->addMonth(),
                    'generations_used' => 0,
                ]);
                Log::info('Subscription cancelled, moved to free plan', ['subscription_id' => $subscription->id]);
            }
        }
    }
}
