<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Services\SubscriptionService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use App\Models\User;
use Illuminate\Support\Facades\Auth;

class PricingController extends Controller
{
    protected SubscriptionService $subscriptionService;

    public function __construct(SubscriptionService $subscriptionService)
    {
        $this->subscriptionService = $subscriptionService;
    }

    public function index(Request $request): View
    {
        $user = $request->user();
        $subscription = $user->subscription;
        $plans = Plan::where('is_active', true)->get();

        return view('pricing.index', compact('subscription', 'plans'));
    }

    public function plans(): View
    {
        $plans = Plan::where('is_active', true)->get();

        return view('pricing.plans', compact('plans'));
    }

    public function checkout(Request $request, Plan $plan)
    {
        $user = $request->user();
        $billingFrequency = $request->get('frequency', 'monthly');

        // Validate billing frequency
        if (!in_array($billingFrequency, ['monthly', 'annual'])) {
            $billingFrequency = 'monthly';
        }

        // If user is not authenticated, store intended plan and redirect to login
        if (!$user) {
            session([
                'intended_plan_checkout' => $plan->id,
                'intended_billing_frequency' => $billingFrequency
            ]);
            return redirect()->route('login')
                ->with('info', 'Please log in to continue with your plan selection.');
        }

        // Don't allow checkout for free plan
        if ($plan->isFree()) {
            return redirect()->route('pricing.index')
                ->with('error', 'You are already on the free plan.');
        }

        // Don't allow checkout if user already has this plan with same frequency
        if (
            $user->subscription &&
            $user->subscription->plan_id === $plan->id &&
            $user->subscription->billing_frequency === $billingFrequency
        ) {
            return redirect()->route('pricing.index')
                ->with('error', 'You are already subscribed to this plan with the same billing frequency.');
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret'));

            $price = $plan->getPrice($billingFrequency);
            $interval = $billingFrequency === 'annual' ? 'year' : 'month';

            $session = Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'usd',
                        'product_data' => [
                            'name' => $plan->name . ' Plan (' . ucfirst($billingFrequency) . ')',
                            'description' => $plan->description,
                        ],
                        'unit_amount' => $price * 100, // Convert to cents
                        'recurring' => [
                            'interval' => $interval,
                        ],
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'subscription',
                'success_url' => route('pricing.success') . '?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url' => route('pricing.cancel') . '?plan=' . $plan->id . '&frequency=' . $billingFrequency,
                'client_reference_id' => $user->id,
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                    'billing_frequency' => $billingFrequency,
                ],
            ]);

            return redirect($session->url);
        } catch (\Exception $e) {
            return redirect()->route('pricing.index')
                ->with('error', 'Unable to create checkout session. Please try again.');
        }
    }

    public function success(Request $request)
    {
        $sessionId = $request->get('session_id');

        if (!$sessionId) {
            return redirect()->route('pricing.index')
                ->with('error', 'Invalid checkout session.');
        }

        // If registration data is pending in session, create the user and assign the paid plan
        if (session()->has('pending_registration') && session()->has('selected_plan_after_registration')) {
            $registrationData = session('pending_registration');
            $selectedPlanId = session('selected_plan_after_registration');
            $user = User::create([
                'name' => $registrationData['name'],
                'email' => $registrationData['email'],
                'password' => $registrationData['password'],
            ]);
            // Assign paid plan subscription
            $plan = Plan::find($selectedPlanId);
            if ($plan) {
                $user->subscription()->create([
                    'plan_id' => $plan->id,
                    'status' => 'active',
                    'starts_at' => now(),
                    'ends_at' => now()->addMonth(),
                    'generations_used' => 0,
                ]);
            }
            Auth::login($user);
            // Clear session
            session()->forget(['pending_registration', 'selected_plan_after_registration']);
            return redirect()->route('dashboard')->with('success', 'Registration and payment successful! Welcome to Content Spark.');
        }

        // Refresh user's subscription data to ensure we have the latest information
        $user = $request->user();
        if ($user) {
            $user->load(['subscription.plan']);
        }

        return view('pricing.success', [
            'sessionId' => $sessionId,
            'user' => $user,
        ]);
    }

    public function cancel(Request $request): RedirectResponse
    {
        $planId = $request->get('plan');

        // If plan ID is provided, redirect to registration with plan context
        if ($planId) {
            return redirect()->route('register', ['plan' => $planId])
                ->with('info', 'Payment was cancelled. You can try again or choose a different plan.');
        }

        // Default redirect to pricing page
        return redirect()->route('pricing.index')
            ->with('info', 'Checkout was cancelled.');
    }
}
