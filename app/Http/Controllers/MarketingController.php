<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class MarketingController extends Controller
{
    /**
     * Show the marketing landing page
     */
    public function landing(): View
    {
        $plans = Plan::orderBy('monthly_price')->get();

        // Get testimonials data
        $testimonials = $this->getTestimonials();

        // Get features data
        $features = $this->getFeatures();

        // Get benefits data
        $benefits = $this->getBenefits();

        // Get statistics
        $stats = $this->getStatistics();

        // Get FAQs for current language
        $locale = app()->getLocale();
        $faqs = \App\Models\Faq::active()
            ->ordered()
            ->whereHas('translations', function ($query) use ($locale) {
                $query->where('locale', $locale);
            })
            ->with(['translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->get();

        return view('marketing.landing', compact(
            'plans',
            'testimonials',
            'features',
            'benefits',
            'stats',
            'faqs'
        ));
    }

    /**
     * Handle newsletter signup
     */
    public function newsletter(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'email' => 'required|email|max:255',
            'locale' => 'nullable|string|in:en,ar,fr,es,pt,ru'
        ]);

        // Store newsletter subscription
        // You can integrate with your email service here

        return redirect()->back()->with('success', __('marketing.newsletter_success'));
    }

    /**
     * Get testimonials data
     */
    protected function getTestimonials(): array
    {
        return [
            [
                'name' => 'Sarah Johnson',
                'company' => 'Digital Marketing Agency',
                'role' => 'Content Manager',
                'content' => 'marketing.testimonial_1',
                'rating' => 5,
                'avatar' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
            ],
            [
                'name' => 'Ahmed Al-Rashid',
                'company' => 'E-commerce Store',
                'role' => 'Marketing Director',
                'content' => 'marketing.testimonial_2',
                'rating' => 5,
                'avatar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
            ],
            [
                'name' => 'Marie Dubois',
                'company' => 'Tech Startup',
                'role' => 'Founder',
                'content' => 'marketing.testimonial_3',
                'rating' => 5,
                'avatar' => 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
            ]
        ];
    }

    /**
     * Get features data
     */
    protected function getFeatures(): array
    {
        return [
            [
                'icon' => '🚀',
                'title' => 'marketing.feature_speed_title',
                'description' => 'marketing.feature_speed_desc',
                'metric' => '10x'
            ],
            [
                'icon' => '🎯',
                'title' => 'marketing.feature_quality_title',
                'description' => 'marketing.feature_quality_desc',
                'metric' => '99%'
            ],
            [
                'icon' => '🌍',
                'title' => 'marketing.feature_multilang_title',
                'description' => 'marketing.feature_multilang_desc',
                'metric' => '6'
            ],
            [
                'icon' => '🤖',
                'title' => 'marketing.feature_ai_title',
                'description' => 'marketing.feature_ai_desc',
                'metric' => 'GPT-4'
            ]
        ];
    }

    /**
     * Get benefits data
     */
    protected function getBenefits(): array
    {
        return [
            [
                'icon' => '⏰',
                'title' => 'marketing.benefit_time_title',
                'description' => 'marketing.benefit_time_desc'
            ],
            [
                'icon' => '💰',
                'title' => 'marketing.benefit_cost_title',
                'description' => 'marketing.benefit_cost_desc'
            ],
            [
                'icon' => '📈',
                'title' => 'marketing.benefit_scale_title',
                'description' => 'marketing.benefit_scale_desc'
            ],
            [
                'icon' => '✨',
                'title' => 'marketing.benefit_quality_title',
                'description' => 'marketing.benefit_quality_desc'
            ]
        ];
    }

    /**
     * Get statistics
     */
    protected function getStatistics(): array
    {
        return [
            [
                'number' => '50,000+',
                'label' => 'marketing.stat_users'
            ],
            [
                'number' => '2M+',
                'label' => 'marketing.stat_content'
            ],
            [
                'number' => '99.9%',
                'label' => 'marketing.stat_uptime'
            ],
            [
                'number' => '4.9/5',
                'label' => 'marketing.stat_rating'
            ]
        ];
    }
}
