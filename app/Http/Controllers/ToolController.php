<?php

namespace App\Http\Controllers;

use App\Models\ContentType;
use Illuminate\View\View;

class ToolController extends Controller
{
    /**
     * Show content creation tools landing page
     */
    public function contentCreation(): View
    {
        $tools = ContentType::where('category', 'Content Creation')
            ->where('is_active', true)
            ->get();

        $category = [
            'name' => 'Content Creation',
            'title' => 'AI Content Creation Tools',
            'description' => 'Create high-quality blog posts, articles, and written content with our AI-powered tools',
            'icon' => '📝',
            'hero_title' => 'Transform Your Ideas Into Compelling Content',
            'hero_subtitle' => 'Generate professional blog posts, articles, and written content in seconds with our advanced AI technology',
            'benefits' => [
                'Save hours of writing time',
                'Generate SEO-optimized content',
                'Maintain consistent quality',
                'Scale your content production'
            ]
        ];

        return view('tools.category', compact('tools', 'category'));
    }

    /**
     * Show e-commerce tools landing page
     */
    public function ecommerce(): View
    {
        $tools = ContentType::where('category', 'E-commerce')
            ->where('is_active', true)
            ->get();

        $category = [
            'name' => 'E-commerce',
            'title' => 'AI E-commerce Content Tools',
            'description' => 'Create compelling product descriptions and e-commerce content that converts',
            'icon' => '🛒',
            'hero_title' => 'Boost Your Sales With AI-Generated Product Content',
            'hero_subtitle' => 'Create persuasive product descriptions that convert visitors into customers',
            'benefits' => [
                'Increase conversion rates',
                'Generate compelling product descriptions',
                'Optimize for search engines',
                'Scale your product catalog'
            ]
        ];

        return view('tools.category', compact('tools', 'category'));
    }

    /**
     * Show social media tools landing page
     */
    public function socialMedia(): View
    {
        $tools = ContentType::where('category', 'Social Media')
            ->where('is_active', true)
            ->get();

        $category = [
            'name' => 'Social Media',
            'title' => 'AI Social Media Content Tools',
            'description' => 'Create engaging social media posts and captions that drive engagement',
            'icon' => '📱',
            'hero_title' => 'Create Viral Social Media Content',
            'hero_subtitle' => 'Generate engaging posts, captions, and content for all major social platforms',
            'benefits' => [
                'Increase engagement rates',
                'Save time on content creation',
                'Maintain consistent posting',
                'Optimize for each platform'
            ]
        ];

        return view('tools.category', compact('tools', 'category'));
    }

    /**
     * Show marketing tools landing page
     */
    public function marketing(): View
    {
        $tools = ContentType::where('category', 'Marketing')
            ->where('is_active', true)
            ->get();

        $category = [
            'name' => 'Marketing',
            'title' => 'AI Marketing Content Tools',
            'description' => 'Generate high-converting marketing emails and promotional content',
            'icon' => '📧',
            'hero_title' => 'Create Marketing Content That Converts',
            'hero_subtitle' => 'Generate compelling email campaigns, ads, and promotional content that drives results',
            'benefits' => [
                'Improve conversion rates',
                'Create compelling copy',
                'A/B test different variations',
                'Scale your marketing efforts'
            ]
        ];

        return view('tools.category', compact('tools', 'category'));
    }

    /**
     * Show SEO tools landing page
     */
    public function seo(): View
    {
        $tools = ContentType::where('category', 'SEO')
            ->where('is_active', true)
            ->get();

        $category = [
            'name' => 'SEO',
            'title' => 'AI SEO Content Tools',
            'description' => 'Create SEO-optimized content that ranks higher in search results',
            'icon' => '🔍',
            'hero_title' => 'Dominate Search Rankings With AI-Optimized Content',
            'hero_subtitle' => 'Generate SEO-friendly content that helps your website rank higher and attract more organic traffic',
            'benefits' => [
                'Improve search rankings',
                'Increase organic traffic',
                'Optimize for target keywords',
                'Create search-friendly content'
            ]
        ];

        return view('tools.category', compact('tools', 'category'));
    }

    /**
     * Show website content tools landing page
     */
    public function websiteContent(): View
    {
        $tools = ContentType::where('category', 'Website Content')
            ->where('is_active', true)
            ->get();

        $category = [
            'name' => 'Website Content',
            'title' => 'AI Website Content Tools',
            'description' => 'Generate professional website copy and landing page content',
            'icon' => '🌐',
            'hero_title' => 'Build Converting Websites With AI-Generated Copy',
            'hero_subtitle' => 'Create compelling website content that engages visitors and drives conversions',
            'benefits' => [
                'Increase website conversions',
                'Create professional copy',
                'Optimize user experience',
                'Build trust with visitors'
            ]
        ];

        return view('tools.category', compact('tools', 'category'));
    }

    /**
     * Show video content tools landing page
     */
    public function videoContent(): View
    {
        $tools = ContentType::where('category', 'Video Content')
            ->where('is_active', true)
            ->get();

        $category = [
            'name' => 'Video Content',
            'title' => 'AI Video Content Tools',
            'description' => 'Create engaging video scripts and captions for your video content',
            'icon' => '🎥',
            'hero_title' => 'Create Engaging Video Content With AI',
            'hero_subtitle' => 'Generate compelling video scripts, captions, and descriptions that captivate your audience',
            'benefits' => [
                'Increase video engagement',
                'Save time on scriptwriting',
                'Improve accessibility',
                'Optimize for platforms'
            ]
        ];

        return view('tools.category', compact('tools', 'category'));
    }
}
