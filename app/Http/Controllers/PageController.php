<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PageController extends Controller
{
    /**
     * Display a dynamic page by slug.
     */
    public function show(Request $request, string $slug): View
    {
        $locale = app()->getLocale();

        // Find the page by slug
        $page = Page::where('slug', $slug)
            ->where('status', 'active')
            ->with(['translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->firstOrFail();

        // Get the translation for current locale
        $translation = $page->translation($locale);

        if (!$translation) {
            // Fallback to English if translation doesn't exist
            $translation = $page->translation('en');
        }

        if (!$translation) {
            abort(404);
        }

        return view('pages.show', compact('page', 'translation'));
    }

    /**
     * Display content type landing page.
     */
    public function contentType(Request $request, string $contentType): View
    {
        $locale = app()->getLocale();

        // Map content type slugs to page slugs
        $contentTypePages = [
            'social-media-posts' => 'social-media-posts',
            'product-descriptions' => 'product-descriptions',
            'youtube-video-ideas' => 'youtube-video-ideas',
            'blog-post-outlines' => 'blog-post-outlines',
            'email-marketing-content' => 'email-marketing-content',
        ];

        if (!isset($contentTypePages[$contentType])) {
            abort(404);
        }

        $pageSlug = $contentTypePages[$contentType];

        // Find the page
        $page = Page::where('slug', $pageSlug)
            ->where('status', 'active')
            ->with(['translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->firstOrFail();

        $translation = $page->translation($locale);

        if (!$translation) {
            $translation = $page->translation('en');
        }

        if (!$translation) {
            abort(404);
        }

        // Get related content types (exclude current one)
        $relatedContentTypes = collect($contentTypePages)
            ->reject(function ($slug) use ($pageSlug) {
                return $slug === $pageSlug;
            })
            ->take(3);

        return view('pages.content-type', compact('page', 'translation', 'contentType', 'relatedContentTypes'));
    }
}
