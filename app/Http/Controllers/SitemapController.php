<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Models\ContentType;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;

class SitemapController extends Controller
{
    /**
     * Generate XML sitemap for the website
     */
    public function index(): Response
    {
        $supportedLocales = ['en', 'ar', 'fr', 'es', 'pt', 'ru'];
        $urls = collect();

        // Add homepage
        $urls->push([
            'url' => url('/'),
            'lastmod' => Carbon::now()->toISOString(),
            'changefreq' => 'daily',
            'priority' => '1.0',
            'alternates' => $this->getAlternateUrls('/', $supportedLocales)
        ]);

        // Add static marketing pages
        $staticPages = [
            'pricing' => ['changefreq' => 'weekly', 'priority' => '0.9'],
            'faq' => ['changefreq' => 'monthly', 'priority' => '0.8'],
            'about' => ['changefreq' => 'monthly', 'priority' => '0.7'],
            'contact' => ['changefreq' => 'monthly', 'priority' => '0.7'],
            'privacy-policy' => ['changefreq' => 'yearly', 'priority' => '0.5'],
            'terms-of-service' => ['changefreq' => 'yearly', 'priority' => '0.5'],
        ];

        foreach ($staticPages as $page => $config) {
            $urls->push([
                'url' => url($page),
                'lastmod' => Carbon::now()->toISOString(),
                'changefreq' => $config['changefreq'],
                'priority' => $config['priority'],
                'alternates' => $this->getAlternateUrls($page, $supportedLocales)
            ]);
        }

        // Add dynamic pages from database
        $pages = Page::where('status', 'active')
            ->with('translations')
            ->get();

        foreach ($pages as $page) {
            foreach ($supportedLocales as $locale) {
                $translation = $page->translations->where('locale', $locale)->first();
                if ($translation) {
                    $pageUrl = $locale === 'en' 
                        ? url($page->slug) 
                        : url($locale . '/' . $page->slug);
                    
                    $urls->push([
                        'url' => $pageUrl,
                        'lastmod' => $page->updated_at->toISOString(),
                        'changefreq' => 'monthly',
                        'priority' => '0.8',
                        'alternates' => $this->getAlternateUrls($page->slug, $supportedLocales)
                    ]);
                }
            }
        }

        // Add content type landing pages
        $contentTypes = ContentType::where('status', 'active')->get();
        foreach ($contentTypes as $contentType) {
            $urls->push([
                'url' => url('content-types/' . $contentType->slug),
                'lastmod' => $contentType->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ]);

            // Add tool landing pages
            $urls->push([
                'url' => url('tool/' . $contentType->slug),
                'lastmod' => $contentType->updated_at->toISOString(),
                'changefreq' => 'weekly',
                'priority' => '0.7'
            ]);
        }

        // Add localized marketing landing pages
        foreach ($supportedLocales as $locale) {
            if ($locale !== 'en') {
                $urls->push([
                    'url' => url($locale),
                    'lastmod' => Carbon::now()->toISOString(),
                    'changefreq' => 'daily',
                    'priority' => '0.9',
                    'alternates' => $this->getAlternateUrls('/', $supportedLocales)
                ]);
            }
        }

        $xml = $this->generateSitemapXml($urls);

        return response($xml, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600'
        ]);
    }

    /**
     * Generate alternate URLs for hreflang
     */
    private function getAlternateUrls(string $path, array $locales): array
    {
        $alternates = [];
        
        foreach ($locales as $locale) {
            $url = $locale === 'en' 
                ? url($path === '/' ? '/' : $path)
                : url($locale . ($path === '/' ? '' : '/' . $path));
            
            $alternates[] = [
                'hreflang' => $locale,
                'href' => $url
            ];
        }

        // Add x-default
        $alternates[] = [
            'hreflang' => 'x-default',
            'href' => url($path === '/' ? '/' : $path)
        ];

        return $alternates;
    }

    /**
     * Generate XML sitemap content
     */
    private function generateSitemapXml($urls): string
    {
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xhtml="http://www.w3.org/1999/xhtml">' . "\n";

        foreach ($urls as $url) {
            $xml .= "  <url>\n";
            $xml .= "    <loc>" . htmlspecialchars($url['url']) . "</loc>\n";
            $xml .= "    <lastmod>" . $url['lastmod'] . "</lastmod>\n";
            $xml .= "    <changefreq>" . $url['changefreq'] . "</changefreq>\n";
            $xml .= "    <priority>" . $url['priority'] . "</priority>\n";

            // Add hreflang alternates if available
            if (isset($url['alternates'])) {
                foreach ($url['alternates'] as $alternate) {
                    $xml .= "    <xhtml:link rel=\"alternate\" hreflang=\"" . $alternate['hreflang'] . "\" href=\"" . htmlspecialchars($alternate['href']) . "\" />\n";
                }
            }

            $xml .= "  </url>\n";
        }

        $xml .= '</urlset>';

        return $xml;
    }

    /**
     * Generate robots.txt file
     */
    public function robots(): Response
    {
        $content = "User-agent: *\n";
        $content .= "Allow: /\n";
        $content .= "Disallow: /admin/\n";
        $content .= "Disallow: /dashboard/\n";
        $content .= "Disallow: /generate/\n";
        $content .= "Disallow: /billing/\n";
        $content .= "Disallow: /settings/\n";
        $content .= "Disallow: /profile/\n";
        $content .= "\n";
        $content .= "Sitemap: " . url('/sitemap.xml') . "\n";

        return response($content, 200, [
            'Content-Type' => 'text/plain',
            'Cache-Control' => 'public, max-age=86400'
        ]);
    }
}
