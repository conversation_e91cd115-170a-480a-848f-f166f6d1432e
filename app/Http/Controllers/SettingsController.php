<?php

namespace App\Http\Controllers;

use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\View\View;

class SettingsController extends Controller
{
    public function index(Request $request): View
    {
        $user = $request->user();

        $languages = [
            'ar' => 'العربية',
            'en' => 'English',
            'fr' => 'Français',
            'es' => 'Español',
            'pt' => 'Português',
            'ru' => 'Русский',
        ];

        return view('settings.index', compact('user', 'languages'));
    }

    public function update(Request $request): RedirectResponse
    {
        $user = $request->user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'preferred_language' => 'required|in:en,ar,fr,ru,es,pt,de,it',
            'current_password' => 'nullable|string',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        if ($validated['current_password'] && !Hash::check($validated['current_password'], $user->password)) {
            return redirect()->back()->withErrors(['current_password' => 'Current password is incorrect.']);
        }

        $user->update([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'preferred_language' => $validated['preferred_language'],
        ]);

        if ($validated['password']) {
            $user->update(['password' => Hash::make($validated['password'])]);
        }

        return redirect()->route('settings.index')->with('success', 'Settings updated successfully!');
    }

    /**
     * Update notification preferences.
     */
    public function updateNotifications(Request $request): RedirectResponse
    {
        $user = $request->user();

        $preferences = [
            'email_notifications' => $request->has('email_notifications'),
            'marketing_emails' => $request->has('marketing_emails'),
            'generation_reminders' => $request->has('generation_reminders'),
        ];

        $user->update([
            'notification_preferences' => json_encode($preferences),
        ]);

        return redirect()->route('settings.index')
            ->with('success', 'Notification preferences updated successfully.');
    }

    /**
     * Export user data.
     */
    public function exportData(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $user = auth()->user();

        // Prepare user data for export
        $userData = [
            'profile' => [
                'name' => $user->name,
                'email' => $user->email,
                'created_at' => $user->created_at->toISOString(),
                'preferred_language' => $user->preferred_language,
            ],
            'subscription' => $user->subscription ? [
                'plan' => $user->subscription->plan->name,
                'billing_frequency' => $user->subscription->billing_frequency ?? 'monthly',
                'starts_at' => $user->subscription->starts_at?->toISOString(),
                'ends_at' => $user->subscription->ends_at?->toISOString(),
                'generations_used' => $user->subscription->generations_used,
            ] : null,
            'generations' => $user->generations()->with('contentType')->get()->map(function ($generation) {
                return [
                    'content_type' => $generation->contentType->name,
                    'language' => $generation->language,
                    'keywords' => $generation->keywords,
                    'result' => $generation->result,
                    'created_at' => $generation->created_at->toISOString(),
                ];
            }),
            'export_date' => now()->toISOString(),
        ];

        $filename = 'user_data_' . $user->id . '_' . now()->format('Y-m-d') . '.json';
        $tempFile = tempnam(sys_get_temp_dir(), 'user_export');
        file_put_contents($tempFile, json_encode($userData, JSON_PRETTY_PRINT));

        return response()->download($tempFile, $filename)->deleteFileAfterSend();
    }
}
