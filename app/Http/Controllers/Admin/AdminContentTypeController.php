<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContentType;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class AdminContentTypeController extends Controller
{
    /**
     * Display a listing of content types.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $contentTypes = ContentType::withCount('generations');

            // Apply filters if they exist
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') {
                    $contentTypes->where('is_active', true);
                } elseif ($status === 'inactive') {
                    $contentTypes->where('is_active', false);
                }
            }

            if ($request->filled('category')) {
                $contentTypes->where('category', $request->get('category'));
            }

            return DataTables::of($contentTypes)
                ->addColumn('status', function ($contentType) {
                    return $contentType->is_active ?
                        '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>' :
                        '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>';
                })
                ->addColumn('category_badge', function ($contentType) {
                    if ($contentType->category) {
                        return '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">' . $contentType->category . '</span>';
                    }
                    return '<span class="text-gray-400">-</span>';
                })
                ->addColumn('action', function ($contentType) {
                    return view('components.admin.action-buttons', [
                        'model' => $contentType,
                        'resource' => 'admin.content-types',
                        'showView' => true,
                        'toggleRoute' => 'admin.content-types.toggle-status',
                        'toggleField' => 'status',
                    ])->render();
                })
                ->rawColumns(['status', 'category_badge', 'action'])
                ->make(true);
        }

        // Get statistics for the non-ajax request
        $stats = [
            'total' => ContentType::count(),
            'active' => ContentType::where('is_active', true)->count(),
            'inactive' => ContentType::where('is_active', false)->count(),
            'total_generations' => ContentType::withCount('generations')->get()->sum('generations_count'),
        ];

        // Get unique categories for filter dropdown
        $categories = ContentType::whereNotNull('category')
            ->distinct()
            ->pluck('category')
            ->toArray();

        return view('admin.content-types.index', compact('stats', 'categories'));
    }

    /**
     * Show the form for creating a new content type.
     */
    public function create(): View
    {
        return view('admin.content-types.create');
    }

    /**
     * Store a newly created content type.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:content_types,name',
            'description' => 'nullable|string|max:1000',
            'prompt_template' => 'required|string|max:5000',
            'category' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        // Generate slug from name
        $validated['slug'] = Str::slug($validated['name']);

        // Ensure slug is unique
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (ContentType::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $validated['is_active'] = $request->has('is_active');

        ContentType::create($validated);

        return redirect()->route('admin.content-types.index')
            ->with('success', 'Content type created successfully.');
    }

    /**
     * Display the specified content type.
     */
    public function show(ContentType $contentType): View
    {
        $contentType->load(['generations' => function ($query) {
            $query->with('user')->latest()->take(10);
        }]);

        $stats = [
            'total_generations' => $contentType->generations()->count(),
            'this_month' => $contentType->generations()->whereMonth('created_at', now()->month)->count(),
            'this_week' => $contentType->generations()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'today' => $contentType->generations()->whereDate('created_at', today())->count(),
        ];

        return view('admin.content-types.show', compact('contentType', 'stats'));
    }

    /**
     * Show the form for editing the specified content type.
     */
    public function edit(ContentType $contentType): View
    {
        return view('admin.content-types.edit', compact('contentType'));
    }

    /**
     * Update the specified content type.
     */
    public function update(Request $request, ContentType $contentType): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:content_types,name,' . $contentType->id,
            'description' => 'nullable|string|max:1000',
            'prompt_template' => 'required|string|max:5000',
            'category' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $contentType->name) {
            $newSlug = Str::slug($validated['name']);
            $originalSlug = $newSlug;
            $counter = 1;
            while (ContentType::where('slug', $newSlug)->where('id', '!=', $contentType->id)->exists()) {
                $newSlug = $originalSlug . '-' . $counter;
                $counter++;
            }
            $validated['slug'] = $newSlug;
        }

        $validated['is_active'] = $request->has('is_active');

        $contentType->update($validated);

        return redirect()->route('admin.content-types.index')
            ->with('success', 'Content type updated successfully.');
    }

    /**
     * Remove the specified content type.
     */
    public function destroy(ContentType $contentType): RedirectResponse
    {
        // Check if content type has generations
        if ($contentType->generations()->count() > 0) {
            return redirect()->route('admin.content-types.index')
                ->with('error', 'Cannot delete content type that has been used for generations. Deactivate it instead.');
        }

        $contentType->delete();

        return redirect()->route('admin.content-types.index')
            ->with('success', 'Content type deleted successfully.');
    }

    /**
     * Toggle the active status of a content type.
     */
    public function toggleStatus(ContentType $contentType): RedirectResponse
    {
        $contentType->update(['is_active' => !$contentType->is_active]);

        $status = $contentType->is_active ? 'activated' : 'deactivated';

        return redirect()->route('admin.content-types.index')
            ->with('success', "Content type {$status} successfully.");
    }

    /**
     * Bulk delete content types
     */
    public function bulkDestroy(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:content_types,id'
        ]);

        $contentTypes = ContentType::whereIn('id', $validated['ids'])->withCount('generations')->get();

        $cannotDelete = [];
        foreach ($contentTypes as $contentType) {
            if ($contentType->generations_count > 0) {
                $cannotDelete[] = $contentType->name;
            }
        }

        if (!empty($cannotDelete)) {
            return back()->with('error', 'Cannot delete content types with generations: ' . implode(', ', $cannotDelete) . '. Deactivate them instead.');
        }

        ContentType::destroy($validated['ids']);

        return back()->with('success', count($validated['ids']) . ' content types deleted successfully.');
    }
}
