<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Testimonial;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;

class AdminTestimonialController extends Controller
{
    /**
     * Display a listing of testimonials.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $testimonials = Testimonial::query();

            // Apply filters if they exist
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') {
                    $testimonials->where('is_active', true);
                } elseif ($status === 'inactive') {
                    $testimonials->where('is_active', false);
                }
            }

            if ($request->filled('featured')) {
                $featured = $request->get('featured');
                if ($featured === 'yes') {
                    $testimonials->where('is_featured', true);
                } elseif ($featured === 'no') {
                    $testimonials->where('is_featured', false);
                }
            }

            return DataTables::of($testimonials)
                ->addColumn('status', function ($testimonial) {
                    return $testimonial->is_active ?
                        '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>' :
                        '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>';
                })
                ->addColumn('featured', function ($testimonial) {
                    return $testimonial->is_featured ?
                        '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Featured</span>' :
                        '<span class="text-gray-400">-</span>';
                })
                ->addColumn('rating_stars', function ($testimonial) {
                    if ($testimonial->rating) {
                        $stars = '';
                        $rating = (int)$testimonial->rating;
                        for ($i = 1; $i <= 5; $i++) {
                            if ($i <= $rating) {
                                $stars .= '<span class="text-yellow-400">★</span>';
                            } else {
                                $stars .= '<span class="text-gray-300">★</span>';
                            }
                        }
                        return $stars;
                    }
                    return '<span class="text-gray-400">-</span>';
                })
                ->addColumn('action', function ($testimonial) {
                    return view('components.admin.action-buttons', [
                        'model' => $testimonial,
                        'resource' => 'admin.testimonials',
                        'showView' => true,
                    ])->render();
                })
                ->rawColumns(['status', 'featured', 'rating_stars', 'action'])
                ->make(true);
        }

        // Get statistics for the non-ajax request
        $stats = [
            'total' => Testimonial::count(),
            'active' => Testimonial::where('is_active', true)->count(),
            'inactive' => Testimonial::where('is_active', false)->count(),
            'featured' => Testimonial::where('is_featured', true)->count(),
        ];

        return view('admin.testimonials.index', compact('stats'));
    }

    /**
     * Show the form for creating a new testimonial.
     */
    public function create(): View
    {
        return view('admin.testimonials.create');
    }

    /**
     * Store a newly created testimonial.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'content' => 'required|string|max:2000',
            'avatar' => 'nullable|string|max:255',
            'rating' => 'nullable|integer|min:1|max:5',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        $validated['is_featured'] = $request->has('is_featured');
        $validated['is_active'] = $request->has('is_active');

        if (!$validated['published_at']) {
            $validated['published_at'] = now();
        }

        Testimonial::create($validated);

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial created successfully.');
    }

    /**
     * Display the specified testimonial.
     */
    public function show(Testimonial $testimonial): View
    {
        return view('admin.testimonials.show', compact('testimonial'));
    }

    /**
     * Show the form for editing the specified testimonial.
     */
    public function edit(Testimonial $testimonial): View
    {
        return view('admin.testimonials.edit', compact('testimonial'));
    }

    /**
     * Update the specified testimonial.
     */
    public function update(Request $request, Testimonial $testimonial): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'title' => 'nullable|string|max:255',
            'company' => 'nullable|string|max:255',
            'content' => 'required|string|max:2000',
            'avatar' => 'nullable|string|max:255',
            'rating' => 'nullable|integer|min:1|max:5',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        $validated['is_featured'] = $request->has('is_featured');
        $validated['is_active'] = $request->has('is_active');

        $testimonial->update($validated);

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial updated successfully.');
    }

    /**
     * Remove the specified testimonial.
     */
    public function destroy(Testimonial $testimonial): RedirectResponse
    {
        $testimonial->delete();

        return redirect()->route('admin.testimonials.index')
            ->with('success', 'Testimonial deleted successfully.');
    }

    /**
     * Toggle the active status of a testimonial.
     */
    public function toggleStatus(Testimonial $testimonial): RedirectResponse
    {
        $testimonial->update(['is_active' => !$testimonial->is_active]);

        $status = $testimonial->is_active ? 'activated' : 'deactivated';

        return redirect()->route('admin.testimonials.index')
            ->with('success', "Testimonial {$status} successfully.");
    }

    /**
     * Toggle the featured status of a testimonial.
     */
    public function toggleFeatured(Testimonial $testimonial): RedirectResponse
    {
        $testimonial->update(['is_featured' => !$testimonial->is_featured]);

        $status = $testimonial->is_featured ? 'featured' : 'unfeatured';

        return redirect()->route('admin.testimonials.index')
            ->with('success', "Testimonial {$status} successfully.");
    }

    /**
     * Bulk delete testimonials
     */
    public function bulkDestroy(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:testimonials,id'
        ]);

        Testimonial::destroy($validated['ids']);

        return back()->with('success', count($validated['ids']) . ' testimonials deleted successfully.');
    }
}
