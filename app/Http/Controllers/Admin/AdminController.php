<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Generation;
use App\Models\Page;
use App\Models\Plan;
use App\Models\Role;
use App\Models\Permission;
use App\Models\Testimonial;
use App\Models\User;
use Illuminate\View\View;

class AdminController extends Controller
{
    public function index(): View
    {
        // Get comprehensive statistics for all admin sections
        $stats = [
            // Users Statistics
            'users' => [
                'total' => User::count(),
                'admins' => User::where('is_admin', true)->count(),
                'regular_users' => User::where('is_admin', false)->count(),
                'recent_registrations' => User::where('created_at', '>=', now()->subDays(7))->count(),
                'active_subscriptions' => User::whereHas('subscriptions', function ($query) {
                    $query->where('stripe_status', 'active');
                })->count(),
            ],

            // Pages Statistics
            'pages' => [
                'total' => Page::count(),
                'published' => Page::where('status', 'published')->count(),
                'draft' => Page::where('status', 'draft')->count(),
                'recent_updates' => Page::where('updated_at', '>=', now()->subDays(7))->count(),
            ],

            // Plans Statistics
            'plans' => [
                'total' => Plan::count(),
                'active' => Plan::where('is_active', true)->count(),
                'featured' => Plan::where('is_featured', true)->count(),
                'inactive' => Plan::where('is_active', false)->count(),
            ],

            // Roles & Permissions Statistics
            'roles' => [
                'total' => Role::count(),
                'default_roles' => Role::where('is_default', true)->count(),
                'custom_roles' => Role::where('is_default', false)->count(),
            ],
            'permissions' => [
                'total' => Permission::count(),
                'groups' => Permission::distinct('group')->count('group'),
            ],

            // Testimonials Statistics
            'testimonials' => [
                'total' => Testimonial::count(),
                'approved' => Testimonial::where('is_active', true)->count(),
                'pending' => Testimonial::where('is_active', false)->count(),
                'featured' => Testimonial::where('is_featured', true)->count(),
            ],

            // Content Generation Statistics
            'generations' => [
                'total' => Generation::count(),
                'this_month' => Generation::whereMonth('created_at', now()->month)->count(),
                'this_week' => Generation::where('created_at', '>=', now()->subDays(7))->count(),
                'today' => Generation::whereDate('created_at', today())->count(),
            ],

            // Recent Activity
            'recent_activity' => [
                'users' => User::latest()->take(5)->get(),
                'generations' => Generation::with(['user', 'contentType'])->latest()->take(5)->get(),
                'pages' => class_exists('App\Models\Page') ? Page::with('translations')->latest()->take(5)->get() : collect(),
            ],
        ];

        return view('admin.index', compact('stats'));
    }
}
