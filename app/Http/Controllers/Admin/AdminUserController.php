<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;

class AdminUserController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $users = User::with(['roles', 'subscription.plan'])->withCount('generations');

            return Datatables::of($users)
                ->addColumn('roles_list', function ($user) {
                    return $user->roles->pluck('name')->implode(', ');
                })
                ->addColumn('subscription_name', function ($user) {
                    return $user->subscription->plan->name ?? 'No Subscription';
                })
                ->addColumn('subscription_status', function ($user) {
                    if (!$user->subscription) {
                        return '<span class="px-2 py-1 text-xs font-semibold text-white bg-gray-500 rounded">None</span>';
                    }

                    $statusClass = $user->subscription->isActive() ? 'bg-green-500' : 'bg-red-500';
                    return '<span class="px-2 py-1 text-xs font-semibold text-white rounded ' . $statusClass . '">' .
                        ($user->subscription->isActive() ? 'Active' : 'Inactive') . '</span>';
                })
                ->addColumn('action', function ($user) {
                    return view('components.admin.action-buttons', [
                        'model' => $user,
                        'resource' => 'admin.users',
                        'showView' => true,
                    ])->render();
                })
                ->rawColumns(['subscription_status', 'action'])
                ->make(true);
        }

        // Get filter options for non-AJAX requests
        $plans = \App\Models\Plan::all();
        $statuses = ['active', 'past_due', 'canceled', 'incomplete'];

        return view('admin.users.index', compact('plans', 'statuses'));
    }

    public function create()
    {
        $roles = Role::all();

        return view('admin.users.create', compact('roles'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => ['required', Password::defaults()],
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
            'is_admin' => 'boolean',
            'preferred_language' => 'nullable|string|max:10',
            'notification_preferences' => 'nullable|array',
        ]);

        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'is_admin' => $validated['is_admin'] ?? false,
            'preferred_language' => $validated['preferred_language'] ?? null,
            'notification_preferences' => $validated['notification_preferences'] ?? ['email' => true, 'in_app' => true],
        ]);

        if (isset($validated['roles'])) {
            $user->roles()->sync($validated['roles']);
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    public function show(User $user): View
    {
        $user->load(['roles', 'subscription.plan', 'generations.contentType']);

        return view('admin.users.show', compact('user'));
    }

    public function edit(User $user)
    {
        $roles = Role::all();
        $userRoles = $user->roles->pluck('id')->toArray();

        return view('admin.users.edit', compact('user', 'roles', 'userRoles'));
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => ['nullable', Password::defaults()],
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,id',
            'is_admin' => 'boolean',
            'preferred_language' => 'nullable|string|max:10',
            'notification_preferences' => 'nullable|array',
        ]);

        $userData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'is_admin' => $validated['is_admin'] ?? false,
            'preferred_language' => $validated['preferred_language'] ?? null,
            'notification_preferences' => $validated['notification_preferences'] ?? ['email' => true, 'in_app' => true],
        ];

        if (!empty($validated['password'])) {
            $userData['password'] = Hash::make($validated['password']);
        }

        $user->update($userData);

        if (isset($validated['roles'])) {
            $user->roles()->sync($validated['roles']);
        } else {
            $user->roles()->detach();
        }

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    public function destroy(User $user)
    {
        // Don't allow deleting your own account
        if ($user->id === auth()->id()) {
            return back()->with('error', 'You cannot delete your own account.');
        }

        $user->roles()->detach();
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    public function bulkDestroy(Request $request)
    {
        $validated = $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:users,id',
        ]);

        // Don't allow deleting your own account
        if (in_array(auth()->id(), $validated['ids'])) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot delete your own account.',
            ]);
        }

        $users = User::whereIn('id', $validated['ids'])->get();

        foreach ($users as $user) {
            $user->roles()->detach();
            $user->delete();
        }

        return response()->json([
            'success' => true,
            'message' => count($users) . ' users deleted successfully.',
        ]);
    }
}
