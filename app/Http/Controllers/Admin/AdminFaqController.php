<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Models\FaqTranslation;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class AdminFaqController extends Controller
{
    /**
     * Display a listing of FAQs.
     */
    public function index(): View
    {
        $faqs = Faq::with('translations')
            ->ordered()
            ->paginate(15);

        return view('admin.faqs.index', compact('faqs'));
    }

    /**
     * Show the form for creating a new FAQ.
     */
    public function create(): View
    {
        $languages = ['en', 'ar', 'fr', 'es', 'pt', 'ru'];
        return view('admin.faqs.create', compact('languages'));
    }

    /**
     * Store a newly created FAQ in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'translations' => 'required|array',
            'translations.*.locale' => 'required|string|in:en,ar,fr,es,pt,ru',
            'translations.*.question' => 'required|string|max:500',
            'translations.*.answer' => 'required|string',
        ]);

        $faq = Faq::create([
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true),
        ]);

        foreach ($request->translations as $translation) {
            if (!empty($translation['question']) && !empty($translation['answer'])) {
                FaqTranslation::create([
                    'faq_id' => $faq->id,
                    'locale' => $translation['locale'],
                    'question' => $translation['question'],
                    'answer' => $translation['answer'],
                ]);
            }
        }

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ created successfully.');
    }

    /**
     * Display the specified FAQ.
     */
    public function show(Faq $faq): View
    {
        $faq->load('translations');
        return view('admin.faqs.show', compact('faq'));
    }

    /**
     * Show the form for editing the specified FAQ.
     */
    public function edit(Faq $faq): View
    {
        $faq->load('translations');
        $languages = ['en', 'ar', 'fr', 'es', 'pt', 'ru'];
        return view('admin.faqs.edit', compact('faq', 'languages'));
    }

    /**
     * Update the specified FAQ in storage.
     */
    public function update(Request $request, Faq $faq): RedirectResponse
    {
        $request->validate([
            'sort_order' => 'required|integer|min:0',
            'is_active' => 'boolean',
            'translations' => 'required|array',
            'translations.*.locale' => 'required|string|in:en,ar,fr,es,pt,ru',
            'translations.*.question' => 'required|string|max:500',
            'translations.*.answer' => 'required|string',
        ]);

        $faq->update([
            'sort_order' => $request->sort_order,
            'is_active' => $request->boolean('is_active', true),
        ]);

        // Delete existing translations
        $faq->translations()->delete();

        // Create new translations
        foreach ($request->translations as $translation) {
            if (!empty($translation['question']) && !empty($translation['answer'])) {
                FaqTranslation::create([
                    'faq_id' => $faq->id,
                    'locale' => $translation['locale'],
                    'question' => $translation['question'],
                    'answer' => $translation['answer'],
                ]);
            }
        }

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ updated successfully.');
    }

    /**
     * Remove the specified FAQ from storage.
     */
    public function destroy(Faq $faq): RedirectResponse
    {
        $faq->delete();

        return redirect()->route('admin.faqs.index')
            ->with('success', 'FAQ deleted successfully.');
    }

    /**
     * Toggle the active status of the FAQ.
     */
    public function toggleStatus(Faq $faq): RedirectResponse
    {
        $faq->update(['is_active' => !$faq->is_active]);

        $status = $faq->is_active ? 'activated' : 'deactivated';
        return redirect()->route('admin.faqs.index')
            ->with('success', "FAQ {$status} successfully.");
    }

    /**
     * Bulk delete FAQs.
     */
    public function bulkDestroy(Request $request): RedirectResponse
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:faqs,id'
        ]);

        $count = Faq::whereIn('id', $request->ids)->delete();

        return redirect()->route('admin.faqs.index')
            ->with('success', "{$count} FAQs deleted successfully.");
    }
}
