<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContentType;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\Facades\DataTables;
use Yajra\DataTables\Facades\DataTables;

class AdminContentTypeController extends Controller
{
    // No constructor needed here

    /**
     * Display a listing of content types.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $contentTypes = ContentType::withCount('generations');
            
            return DataTables::of($contentTypes)
                ->addColumn('status_label', function ($contentType) {
                    return $contentType->is_active ? 'Active' : 'Inactive';
                })
                ->addColumn('formatted_date', function ($contentType) {
                    return $contentType->created_at->format('M j, Y');
                })
                ->addColumn('action', function ($contentType) {
                    return view('components.admin.action-buttons', [
                        'model' => $contentType,
                        'route' => 'admin.content-types',
                        'extraButtons' => [
                            [
                                'route' => route('admin.content-types.toggle-status', $contentType),
                                'label' => $contentType->is_active ? 'Deactivate' : 'Activate',
                                'method' => 'PATCH',
                                'confirm' => 'Are you sure you want to ' . ($contentType->is_active ? 'deactivate' : 'activate') . ' this content type?',
                                'class' => 'text-yellow-600 hover:text-yellow-900'
                            ]
                        ]
                    ])->render();
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        // Get statistics
        $stats = [
            'total' => ContentType::count(),
            'active' => ContentType::where('is_active', true)->count(),
            'inactive' => ContentType::where('is_active', false)->count(),
            'total_generations' => ContentType::withCount('generations')->get()->sum('generations_count'),
        ];

        return view('admin.content-types.index', compact('stats'));
    }

    /**
     * Show the form for creating a new content type.
     */
    public function create(): View
    {
        return view('admin.content-types.create');
    }

    /**
     * Store a newly created content type.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:content_types,name',
            'description' => 'nullable|string|max:1000',
            'prompt_template' => 'required|string|max:5000',
            'category' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        // Generate slug from name
        $validated['slug'] = Str::slug($validated['name']);
        
        // Ensure slug is unique
        $originalSlug = $validated['slug'];
        $counter = 1;
        while (ContentType::where('slug', $validated['slug'])->exists()) {
            $validated['slug'] = $originalSlug . '-' . $counter;
            $counter++;
        }

        $validated['is_active'] = $request->has('is_active');

        ContentType::create($validated);

        return redirect()->route('admin.content-types.index')
            ->with('success', 'Content type created successfully.');
    }

    /**
     * Display the specified content type.
     */
    public function show(ContentType $contentType): View
    {
        $contentType->load(['generations' => function ($query) {
            $query->with('user')->latest()->take(10);
        }]);

        $stats = [
            'total_generations' => $contentType->generations()->count(),
            'this_month' => $contentType->generations()->whereMonth('created_at', now()->month)->count(),
            'this_week' => $contentType->generations()->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
            'today' => $contentType->generations()->whereDate('created_at', today())->count(),
        ];

        return view('admin.content-types.show', compact('contentType', 'stats'));
    }

    /**
     * Show the form for editing the specified content type.
     */
    public function edit(ContentType $contentType): View
    {
        return view('admin.content-types.edit', compact('contentType'));
    }

    /**
     * Update the specified content type.
     */
    public function update(Request $request, ContentType $contentType): RedirectResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:content_types,name,' . $contentType->id,
            'description' => 'nullable|string|max:1000',
            'prompt_template' => 'required|string|max:5000',
            'category' => 'nullable|string|max:100',
            'is_active' => 'boolean',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $contentType->name) {
            $newSlug = Str::slug($validated['name']);
            $originalSlug = $newSlug;
            $counter = 1;
            while (ContentType::where('slug', $newSlug)->where('id', '!=', $contentType->id)->exists()) {
                $newSlug = $originalSlug . '-' . $counter;
                $counter++;
            }
            $validated['slug'] = $newSlug;
        }

        $validated['is_active'] = $request->has('is_active');

        $contentType->update($validated);

        return redirect()->route('admin.content-types.index')
            ->with('success', 'Content type updated successfully.');
    }

    /**
     * Remove the specified content type.
     */
    public function destroy(ContentType $contentType): RedirectResponse
    {
        // Check if content type has generations
        if ($contentType->generations()->count() > 0) {
            return redirect()->route('admin.content-types.index')
                ->with('error', 'Cannot delete content type that has been used for generations. Deactivate it instead.');
        }

        $contentType->delete();

        return redirect()->route('admin.content-types.index')
            ->with('success', 'Content type deleted successfully.');
    }

    /**
     * Toggle the active status of a content type.
     */
    public function toggleStatus(ContentType $contentType): RedirectResponse
    {
        $contentType->update(['is_active' => !$contentType->is_active]);

        $status = $contentType->is_active ? 'activated' : 'deactivated';
        
        return redirect()->route('admin.content-types.index')
            ->with('success', "Content type {$status} successfully.");
    }
}
