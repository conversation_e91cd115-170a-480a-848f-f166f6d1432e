<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Models\PageTranslation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Yajra\DataTables\Facades\DataTables;

class AdminPageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = Page::with(['translations' => function ($query) {
                $query->where('locale', App::getLocale());
            }]);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            $pages = $query->get();

            return Datatables::of($pages)
                ->addColumn('checkbox', function ($page) {
                    return '<input type="checkbox" class="page-checkbox" value="' . $page->id . '">';
                })
                ->addColumn('title', function ($page) {
                    $translation = $page->translations->first();
                    return $translation ? $translation->title : 'No Title';
                })
                ->addColumn('slug', function ($page) {
                    return '<code class="text-sm bg-gray-100 px-2 py-1 rounded">' . $page->slug . '</code>';
                })
                ->addColumn('status_label', function ($page) {
                    $statusClass = $page->status === 'published' ? 'bg-green-500' : 'bg-yellow-500';
                    $statusText = ucfirst($page->status);
                    return '<span class="px-2 py-1 text-xs font-semibold text-white rounded ' . $statusClass . '">' . $statusText . '</span>';
                })
                ->addColumn('created_at_formatted', function ($page) {
                    return $page->created_at->format('M d, Y');
                })
                ->addColumn('action', function ($page) {
                    return view('components.admin.action-buttons', [
                        'model' => $page,
                        'resource' => 'admin.pages',
                        'showView' => true,
                        'toggleRoute' => 'admin.pages.toggle-status',
                        'toggleField' => 'status',
                        'toggleValue' => $page->status === 'published' ? 'Draft' : 'Publish',
                    ])->render();
                })
                ->rawColumns(['checkbox', 'slug', 'status_label', 'action'])
                ->make(true);
        }

        $stats = [
            'total' => Page::count(),
            'published' => Page::where('status', 'published')->count(),
            'draft' => Page::where('status', 'draft')->count(),
        ];

        return view('admin.pages.index', compact('stats'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.pages.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'slug' => 'required|string|max:255|unique:pages,slug|alpha_dash',
            'status' => 'required|in:draft,published',
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'meta_description' => 'nullable|string|max:255',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        $page = Page::create([
            'slug' => $validated['slug'],
            'status' => $validated['status'],
        ]);

        // Create the default translation
        PageTranslation::create([
            'page_id' => $page->id,
            'locale' => App::getLocale(),
            'title' => $validated['title'],
            'content' => $validated['content'] ?? '',
            'meta_description' => $validated['meta_description'] ?? null,
            'meta_keywords' => $validated['meta_keywords'] ?? null,
        ]);

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Page $page)
    {
        $page->load('translations');

        return view('admin.pages.show', compact('page'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Page $page)
    {
        $translation = $page->translations()
            ->where('locale', App::getLocale())
            ->first();

        if (!$translation) {
            // Create an empty translation if none exists for the current locale
            $translation = new PageTranslation([
                'locale' => App::getLocale(),
                'title' => '',
                'content' => '',
            ]);
        }

        return view('admin.pages.edit', compact('page', 'translation'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Page $page)
    {
        $validated = $request->validate([
            'slug' => 'required|string|max:255|unique:pages,slug,' . $page->id . '|alpha_dash',
            'status' => 'required|in:draft,published',
            'title' => 'required|string|max:255',
            'content' => 'nullable|string',
            'meta_description' => 'nullable|string|max:255',
            'meta_keywords' => 'nullable|string|max:255',
        ]);

        $page->update([
            'slug' => $validated['slug'],
            'status' => $validated['status'],
        ]);

        // Update or create the translation
        $page->translations()->updateOrCreate(
            ['locale' => App::getLocale()],
            [
                'title' => $validated['title'],
                'content' => $validated['content'] ?? '',
                'meta_description' => $validated['meta_description'] ?? null,
                'meta_keywords' => $validated['meta_keywords'] ?? null,
            ]
        );

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Page $page)
    {
        $page->translations()->delete();
        $page->delete();

        return redirect()->route('admin.pages.index')
            ->with('success', 'Page deleted successfully.');
    }

    /**
     * Toggle the status of the specified page.
     */
    public function toggleStatus(Page $page)
    {
        $page->update([
            'status' => $page->status === 'published' ? 'draft' : 'published',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Page status updated successfully.',
            'new_status' => $page->status,
        ]);
    }

    /**
     * Remove multiple pages from storage.
     */
    public function bulkDestroy(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'exists:pages,id',
        ]);

        $pages = Page::whereIn('id', $request->ids)->get();

        foreach ($pages as $page) {
            $page->translations()->delete();
            $page->delete();
        }

        return response()->json([
            'success' => true,
            'message' => count($request->ids) . ' page(s) deleted successfully.',
        ]);
    }
}
