<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Yajra\DataTables\Facades\DataTables;

class AdminPlanController extends Controller
{
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = Plan::withCount(['subscriptions' => function ($query) {
                $query->where('stripe_status', 'active');
            }]);

            // Apply filters
            if ($request->filled('is_active')) {
                $query->where('is_active', $request->is_active === '1');
            }

            if ($request->filled('is_featured')) {
                $query->where('is_featured', $request->is_featured === '1');
            }

            $plans = $query->get();

            // Calculate revenue for each plan
            $plans->each(function ($plan) {
                $plan->monthly_revenue = $plan->subscriptions_count * $plan->monthly_price;
                $plan->annual_revenue = $plan->monthly_revenue * 12;
            });

            return Datatables::of($plans)
                ->addColumn('checkbox', function ($plan) {
                    return '<input type="checkbox" class="plan-checkbox" value="' . $plan->id . '">';
                })
                ->addColumn('name_with_status', function ($plan) {
                    $badges = '';
                    if ($plan->is_featured) {
                        $badges .= '<span class="ml-2 px-2 py-1 text-xs font-semibold text-blue-800 bg-blue-100 rounded">Featured</span>';
                    }
                    if (!$plan->is_active) {
                        $badges .= '<span class="ml-2 px-2 py-1 text-xs font-semibold text-red-800 bg-red-100 rounded">Inactive</span>';
                    }
                    return '<div class="flex items-center">' . $plan->name . $badges . '</div>';
                })
                ->addColumn('formatted_monthly_price', function ($plan) {
                    return '$' . number_format($plan->monthly_price / 100, 2);
                })
                ->addColumn('formatted_annual_price', function ($plan) {
                    return '$' . number_format($plan->annual_price / 100, 2);
                })
                ->addColumn('formatted_monthly_revenue', function ($plan) {
                    return '$' . number_format($plan->monthly_revenue / 100, 2);
                })
                ->addColumn('generations_limit', function ($plan) {
                    return $plan->monthly_generations_limit === -1 ?
                        '<span class="text-green-600 font-medium">Unlimited</span>' :
                        number_format($plan->monthly_generations_limit);
                })
                ->addColumn('status_badges', function ($plan) {
                    $active = $plan->is_active ?
                        '<span class="px-2 py-1 text-xs font-semibold text-white bg-green-500 rounded">Active</span>' :
                        '<span class="px-2 py-1 text-xs font-semibold text-white bg-red-500 rounded">Inactive</span>';

                    $featured = $plan->is_featured ?
                        '<span class="ml-1 px-2 py-1 text-xs font-semibold text-white bg-blue-500 rounded">Featured</span>' : '';

                    return $active . $featured;
                })
                ->addColumn('action', function ($plan) {
                    return view('components.admin.action-buttons', [
                        'model' => $plan,
                        'resource' => 'admin.plans',
                        'showView' => true,
                        'toggleRoute' => 'admin.plans.toggle-active',
                        'toggleField' => 'status',
                    ])->render();
                })
                ->rawColumns(['checkbox', 'name_with_status', 'generations_limit', 'status_badges', 'action'])
                ->make(true);
        }

        // Statistics for dashboard
        $stats = [
            'total' => Plan::count(),
            'active' => Plan::where('is_active', true)->count(),
            'featured' => Plan::where('is_featured', true)->count(),
            'total_subscribers' => Subscription::where('stripe_status', 'active')->count(),
        ];

        // Revenue calculations
        $plans = Plan::withCount(['subscriptions' => function ($query) {
            $query->where('stripe_status', 'active');
        }])->get();

        $plans->each(function ($plan) {
            $plan->monthly_revenue = $plan->subscriptions_count * $plan->monthly_price;
            $plan->annual_revenue = $plan->monthly_revenue * 12;
        });

        $revenue = [
            'monthly' => $plans->sum('monthly_revenue'),
            'annual' => $plans->sum('annual_revenue'),
        ];

        return view('admin.plans.index', compact('stats', 'revenue'));
    }

    public function create()
    {
        return view('admin.plans.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:plans,slug|alpha_dash',
            'description' => 'nullable|string',
            'features' => 'nullable|array',
            'monthly_price' => 'required|numeric|min:0',
            'annual_price' => 'required|numeric|min:0',
            'monthly_generations_limit' => 'required|numeric|min:-1',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Convert prices to cents for Stripe
        $validated['monthly_price'] = $validated['monthly_price'] * 100;
        $validated['annual_price'] = $validated['annual_price'] * 100;

        // Set default values
        $validated['features'] = $validated['features'] ?? [];
        $validated['is_featured'] = $validated['is_featured'] ?? false;
        $validated['is_active'] = $validated['is_active'] ?? true;

        $plan = Plan::create($validated);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan created successfully.');
    }

    public function show(Plan $plan): View
    {
        $plan->load(['subscriptions.user']);

        // Get subscription statistics
        $subscriptionStats = [
            'active' => $plan->subscriptions()->where('stripe_status', 'active')->count(),
            'past_due' => $plan->subscriptions()->where('stripe_status', 'past_due')->count(),
            'canceled' => $plan->subscriptions()->where('stripe_status', 'canceled')->count(),
        ];

        // Recent subscribers
        $recentSubscribers = $plan->subscriptions()
            ->with('user')
            ->where('stripe_status', 'active')
            ->latest()
            ->take(10)
            ->get();

        return view('admin.plans.show', compact('plan', 'subscriptionStats', 'recentSubscribers'));
    }

    public function edit(Plan $plan)
    {
        return view('admin.plans.edit', compact('plan'));
    }

    public function update(Request $request, Plan $plan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:plans,slug,' . $plan->id . '|alpha_dash',
            'description' => 'nullable|string',
            'features' => 'nullable|array',
            'monthly_price' => 'required|numeric|min:0',
            'annual_price' => 'required|numeric|min:0',
            'monthly_generations_limit' => 'required|numeric|min:-1',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ]);

        // Convert prices to cents for Stripe
        $validated['monthly_price'] = $validated['monthly_price'] * 100;
        $validated['annual_price'] = $validated['annual_price'] * 100;

        // Set default values
        $validated['features'] = $validated['features'] ?? [];
        $validated['is_featured'] = $validated['is_featured'] ?? false;
        $validated['is_active'] = $validated['is_active'] ?? true;

        $plan->update($validated);

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan updated successfully.');
    }

    public function destroy(Plan $plan)
    {
        // Check if plan has subscriptions
        if ($plan->subscriptions->count() > 0) {
            return back()->with('error', 'Cannot delete a plan that has subscriptions.');
        }

        $plan->delete();

        return redirect()->route('admin.plans.index')
            ->with('success', 'Plan deleted successfully.');
    }

    public function toggleActive(Plan $plan)
    {
        $plan->update([
            'is_active' => !$plan->is_active,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Plan status updated successfully.',
            'new_status' => $plan->is_active ? 'active' : 'inactive',
        ]);
    }

    public function toggleFeatured(Plan $plan)
    {
        $plan->update([
            'is_featured' => !$plan->is_featured,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Plan featured status updated successfully.',
            'new_status' => $plan->is_featured ? 'featured' : 'not featured',
        ]);
    }
}
