<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(Request $request): View
    {
        $selectedPlan = null;

        // Check if a plan was pre-selected
        if ($request->has('plan')) {
            $planId = $request->get('plan');
            $selectedPlan = Plan::where('id', $planId)
                ->where('is_active', true)
                ->first();
        }

        // Get all plans for display
        $plans = Plan::where('is_active', true)->get();

        return view('auth.register', compact('selectedPlan', 'plans'));
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:' . User::class],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'selected_plan_id' => ['nullable', 'integer', 'exists:plans,id'],
        ]);

        $selectedPlanId = $request->input('selected_plan_id');
        $selectedPlan = $selectedPlanId ? Plan::find($selectedPlanId) : null;

        // If a paid plan is selected, store registration data in session and show payment step page
        if ($selectedPlan && !$selectedPlan->isFree()) {
            $registrationData = [
                'name' => $request->name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'selected_plan_id' => $selectedPlanId,
            ];
            session(['pending_registration' => $registrationData]);
            session(['selected_plan_after_registration' => $selectedPlanId]);
            return redirect()->route('register.complete-payment', ['plan' => $selectedPlan->id]);
        }

        // Free plan registration: create user and log in
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => bcrypt($request->password),
        ]);

        event(new Registered($user));
        Auth::login($user);

        return redirect(route('dashboard', absolute: false))
            ->with('success', 'Registration successful! Welcome to Content Spark.');
    }

    /**
     * Show the payment step after registration for paid plans.
     */
    public function completePaymentStep(Request $request, Plan $plan): View
    {
        return view('auth.complete-payment', ['plan' => $plan]);
    }
}
