<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {
        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Check if user was trying to checkout a plan before login
        if (session()->has('intended_plan_checkout')) {
            $planId = session('intended_plan_checkout');
            $billingFrequency = session('intended_billing_frequency', 'monthly');

            session()->forget(['intended_plan_checkout', 'intended_billing_frequency']);

            // Redirect to checkout for the intended plan with billing frequency
            return redirect()->route('pricing.checkout', [
                'plan' => $planId,
                'frequency' => $billingFrequency
            ]);
        }

        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/');
    }
}
