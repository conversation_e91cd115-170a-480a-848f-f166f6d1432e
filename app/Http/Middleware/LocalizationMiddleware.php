<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class LocalizationMiddleware
{
    /**
     * Supported locales
     */
    protected array $supportedLocales = ['en', 'ar', 'fr', 'es', 'pt', 'ru'];

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $this->getLocaleFromRequest($request);
        
        // Set the application locale
        App::setLocale($locale);
        
        // Store locale in session for persistence
        Session::put('locale', $locale);
        
        // Share locale with all views
        view()->share('currentLocale', $locale);
        view()->share('supportedLocales', $this->getSupportedLocales());
        
        return $next($request);
    }

    /**
     * Get locale from request
     */
    protected function getLocaleFromRequest(Request $request): string
    {
        // First, check if locale is in the URL
        $segments = $request->segments();
        if (!empty($segments) && in_array($segments[0], $this->supportedLocales)) {
            return $segments[0];
        }

        // Second, check session
        if (Session::has('locale') && in_array(Session::get('locale'), $this->supportedLocales)) {
            return Session::get('locale');
        }

        // Third, check browser preference
        $browserLocale = $this->getBrowserLocale($request);
        if ($browserLocale) {
            return $browserLocale;
        }

        // Default to English
        return 'en';
    }

    /**
     * Get browser locale preference
     */
    protected function getBrowserLocale(Request $request): ?string
    {
        $acceptLanguage = $request->header('Accept-Language');
        if (!$acceptLanguage) {
            return null;
        }

        // Parse Accept-Language header
        $languages = [];
        foreach (explode(',', $acceptLanguage) as $lang) {
            $parts = explode(';', trim($lang));
            $locale = trim($parts[0]);
            $quality = 1.0;
            
            if (isset($parts[1]) && strpos($parts[1], 'q=') === 0) {
                $quality = (float) substr($parts[1], 2);
            }
            
            $languages[$locale] = $quality;
        }

        // Sort by quality
        arsort($languages);

        // Find best match
        foreach ($languages as $locale => $quality) {
            // Check exact match
            if (in_array($locale, $this->supportedLocales)) {
                return $locale;
            }
            
            // Check language part only (e.g., 'en' from 'en-US')
            $langPart = substr($locale, 0, 2);
            if (in_array($langPart, $this->supportedLocales)) {
                return $langPart;
            }
        }

        return null;
    }

    /**
     * Get supported locales with metadata
     */
    protected function getSupportedLocales(): array
    {
        return [
            'en' => [
                'name' => 'English',
                'native' => 'English',
                'flag' => '🇺🇸',
                'dir' => 'ltr'
            ],
            'ar' => [
                'name' => 'Arabic',
                'native' => 'العربية',
                'flag' => '🇸🇦',
                'dir' => 'rtl'
            ],
            'fr' => [
                'name' => 'French',
                'native' => 'Français',
                'flag' => '🇫🇷',
                'dir' => 'ltr'
            ],
            'es' => [
                'name' => 'Spanish',
                'native' => 'Español',
                'flag' => '🇪🇸',
                'dir' => 'ltr'
            ],
            'pt' => [
                'name' => 'Portuguese',
                'native' => 'Português',
                'flag' => '🇵🇹',
                'dir' => 'ltr'
            ],
            'ru' => [
                'name' => 'Russian',
                'native' => 'Русский',
                'flag' => '🇷🇺',
                'dir' => 'ltr'
            ],
        ];
    }
}
