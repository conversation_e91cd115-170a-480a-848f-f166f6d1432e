<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Supported locales
     */
    protected array $supportedLocales = ['en', 'ar', 'fr', 'es', 'pt', 'ru'];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $locale = $this->getLocale($request);

        if (in_array($locale, $this->supportedLocales)) {
            App::setLocale($locale);
            Session::put('locale', $locale);
        }

        // Share locale data with views
        view()->share('currentLocale', App::getLocale());
        view()->share('supportedLocales', $this->getSupportedLocales());

        return $next($request);
    }

    /**
     * Get the locale from various sources.
     */
    private function getLocale(Request $request): string
    {
        // 1. Check URL segments for locale prefix
        $segments = $request->segments();
        if (!empty($segments) && in_array($segments[0], $this->supportedLocales)) {
            return $segments[0];
        }

        // 2. Check if user is authenticated and has a preference
        if ($request->user() && $request->user()->preferred_language) {
            return $request->user()->preferred_language;
        }

        // 3. Check session
        if (Session::has('locale')) {
            return Session::get('locale');
        }

        // 4. Check browser language
        $browserLocale = $request->getPreferredLanguage($this->supportedLocales);
        if ($browserLocale) {
            return $browserLocale;
        }

        // 5. Default to English
        return 'en';
    }

    /**
     * Get supported locales with metadata
     */
    protected function getSupportedLocales(): array
    {
        return [
            'en' => [
                'name' => 'English',
                'native' => 'English',
                'flag' => '🇺🇸',
                'dir' => 'ltr'
            ],
            'ar' => [
                'name' => 'Arabic',
                'native' => 'العربية',
                'flag' => '🇸🇦',
                'dir' => 'rtl'
            ],
            'fr' => [
                'name' => 'French',
                'native' => 'Français',
                'flag' => '🇫🇷',
                'dir' => 'ltr'
            ],
            'es' => [
                'name' => 'Spanish',
                'native' => 'Español',
                'flag' => '🇪🇸',
                'dir' => 'ltr'
            ],
            'pt' => [
                'name' => 'Portuguese',
                'native' => 'Português',
                'flag' => '🇵🇹',
                'dir' => 'ltr'
            ],
            'ru' => [
                'name' => 'Russian',
                'native' => 'Русский',
                'flag' => '🇷🇺',
                'dir' => 'ltr'
            ],
        ];
    }
}
