<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Plan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'monthly_generations_limit',
        'monthly_price',
        'annual_price',
        'stripe_monthly_price_id',
        'stripe_annual_price_id',
        'is_active',
        'is_featured',
    ];

    protected $casts = [
        'monthly_generations_limit' => 'integer',
        'monthly_price' => 'decimal:2',
        'annual_price' => 'decimal:2',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    public function isFree(): bool
    {
        return $this->monthly_price == 0 && $this->annual_price == 0;
    }

    public function getPrice(string $frequency = 'monthly'): float
    {
        return $frequency === 'annual' ? $this->annual_price : $this->monthly_price;
    }

    public function getStripePrice(string $frequency = 'monthly'): ?string
    {
        return $frequency === 'annual' ? $this->stripe_annual_price_id : $this->stripe_monthly_price_id;
    }

    public function getAnnualSavings(): float
    {
        if ($this->isFree()) {
            return 0;
        }

        $monthlyTotal = $this->monthly_price * 12;
        return $monthlyTotal - $this->annual_price;
    }

    public function getAnnualSavingsPercentage(): int
    {
        if ($this->isFree() || $this->monthly_price == 0) {
            return 0;
        }

        $monthlyTotal = $this->monthly_price * 12;
        return round(($monthlyTotal - $this->annual_price) / $monthlyTotal * 100);
    }
}
