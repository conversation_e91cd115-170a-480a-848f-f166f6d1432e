<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Page extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'slug',
        'status',
    ];

    protected $casts = [
        'status' => 'string',
    ];

    /**
     * Get the translations for the page.
     */
    public function translations(): HasMany
    {
        return $this->hasMany(PageTranslation::class);
    }

    /**
     * Get translation for specific locale.
     */
    public function translation($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $this->translations()->where('locale', $locale)->first();
    }

    /**
     * Get the page title for current locale.
     */
    public function getTitle($locale = null)
    {
        $translation = $this->translation($locale);
        return $translation ? $translation->title : $this->slug;
    }

    /**
     * Get the page content for current locale.
     */
    public function getContent($locale = null)
    {
        $translation = $this->translation($locale);
        return $translation ? $translation->content : '';
    }

    /**
     * Check if page is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
}
