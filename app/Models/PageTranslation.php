<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PageTranslation extends Model
{
    use HasFactory;

    protected $fillable = [
        'page_id',
        'locale',
        'title',
        'description',
        'content',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'featured_image',
    ];

    /**
     * Get the page that owns the translation.
     */
    public function page(): BelongsTo
    {
        return $this->belongsTo(Page::class);
    }

    /**
     * Get the SEO title or fallback to title.
     */
    public function getSeoTitle(): string
    {
        return $this->seo_title ?: $this->title;
    }

    /**
     * Get the SEO description or fallback to description.
     */
    public function getSeoDescription(): string
    {
        return $this->seo_description ?: $this->description;
    }
}
