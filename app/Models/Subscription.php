<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Subscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'stripe_id',
        'stripe_status',
        'stripe_price',
        'billing_frequency',
        'trial_ends_at',
        'starts_at',
        'ends_at',
        'generations_used',
    ];

    protected $casts = [
        'trial_ends_at' => 'datetime',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'generations_used' => 'integer',
        'stripe_price' => 'decimal:2',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    public function isActive(): bool
    {
        return $this->stripe_status === 'active' &&
            ($this->ends_at === null || $this->ends_at->isFuture());
    }

    public function remainingGenerations(): int
    {
        if ($this->plan->monthly_generations_limit === -1) {
            return -1; // Unlimited
        }

        return max(0, $this->plan->monthly_generations_limit - $this->generations_used);
    }
}
