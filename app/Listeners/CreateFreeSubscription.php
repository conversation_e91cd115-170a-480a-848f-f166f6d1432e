<?php

namespace App\Listeners;

use App\Models\Plan;
use App\Models\Subscription;
use Illuminate\Auth\Events\Registered;

class CreateFreeSubscription
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(Registered $event): void
    {
        $user = $event->user;

        // Check if a paid plan was pre-selected during registration
        $selectedPlanId = session('selected_plan_after_registration');

        if ($selectedPlanId) {
            $selectedPlan = Plan::find($selectedPlanId);

            // If it's a paid plan, don't create free subscription yet
            // The paid subscription will be created after successful payment
            if ($selectedPlan && !$selectedPlan->isFree()) {
                return;
            }
        }

        // Find the free plan
        $freePlan = Plan::where('slug', 'free')->first();

        if ($freePlan) {
            // Create a free subscription for the user
            Subscription::create([
                'user_id' => $user->id,
                'plan_id' => $freePlan->id,
                'stripe_status' => 'active',
                'starts_at' => now(),
                'ends_at' => now()->addMonth(), // Free plan renews monthly
                'generations_used' => 0,
            ]);
        }
    }
}
