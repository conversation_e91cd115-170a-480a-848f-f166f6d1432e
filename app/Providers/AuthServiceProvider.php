<?php

namespace App\Providers;

use App\Models\User;
use App\Policies\AdminPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        User::class => AdminPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define gates for admin functionality
        Gate::define('admin-access', function (User $user) {
            return $user->isAdmin() || $user->hasAnyRole(['admin', 'moderator', 'editor']);
        });

        Gate::define('manage-users', function (User $user) {
            return $user->isAdmin() || $user->hasAnyRole(['admin', 'moderator']);
        });

        Gate::define('manage-content', function (User $user) {
            return $user->isAdmin() || $user->hasAnyRole(['admin', 'moderator', 'editor']);
        });

        Gate::define('manage-system', function (User $user) {
            return $user->isAdmin() || $user->hasRole('admin');
        });

        Gate::define('view-analytics', function (User $user) {
            return $user->isAdmin() || $user->hasAnyRole(['admin', 'moderator']);
        });

        Gate::define('export-data', function (User $user) {
            return $user->isAdmin() || $user->hasPermission('data-export');
        });
    }
}
