<?php

namespace App\Providers;

use App\Helpers\LocalizedUrlHelper;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;

class LocalizationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Register Blade directives for localized URLs
        Blade::directive('localizedRoute', function ($expression) {
            return "<?php echo \App\Helpers\LocalizedUrlHelper::route($expression); ?>";
        });

        Blade::directive('localizedPage', function ($expression) {
            return "<?php echo \App\Helpers\LocalizedUrlHelper::page($expression); ?>";
        });

        Blade::directive('isRouteActive', function ($expression) {
            return "<?php echo \App\Helpers\LocalizedUrlHelper::isRouteActive($expression) ? 'true' : 'false'; ?>";
        });

        // Share helper with all views
        view()->composer('*', function ($view) {
            $view->with('localizedUrl', new LocalizedUrlHelper());
        });
    }
}
