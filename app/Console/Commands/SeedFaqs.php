<?php

namespace App\Console\Commands;

use App\Models\Faq;
use App\Models\FaqTranslation;
use Illuminate\Console\Command;

class SeedFaqs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'faq:seed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed the database with FAQ entries';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Seeding FAQs...');

        // Clear existing FAQs
        Faq::truncate();
        FaqTranslation::truncate();

        $faqs = [
            [
                'sort_order' => 1,
                'translations' => [
                    'en' => [
                        'question' => 'How does the AI content generation work?',
                        'answer' => 'Our AI uses advanced language models trained on millions of high-quality content examples. Simply provide keywords or topics, and our AI generates relevant, engaging content in seconds.',
                    ],
                    'ar' => [
                        'question' => 'كيف يعمل إنشاء المحتوى بالذكاء الاصطناعي؟',
                        'answer' => 'يستخدم الذكاء الاصطناعي لدينا نماذج لغوية متقدمة مدربة على ملايين أمثلة المحتوى عالي الجودة. ما عليك سوى تقديم الكلمات المفتاحية أو المواضيع، وسينشئ الذكاء الاصطناعي محتوى ذا صلة وجذاب في ثوانٍ.',
                    ],
                    'fr' => [
                        'question' => 'Comment fonctionne la génération de contenu IA ?',
                        'answer' => 'Notre IA utilise des modèles de langage avancés entraînés sur des millions d\'exemples de contenu de haute qualité. Fournissez simplement des mots-clés ou sujets, et notre IA génère du contenu pertinent et engageant en secondes.',
                    ],
                    'es' => [
                        'question' => '¿Cómo funciona la generación de contenido con IA?',
                        'answer' => 'Nuestra IA utiliza modelos de lenguaje avanzados entrenados en millones de ejemplos de contenido de alta calidad. Simplemente proporciona palabras clave o temas, y nuestra IA genera contenido relevante y atractivo en segundos.',
                    ],
                    'pt' => [
                        'question' => 'Como funciona a geração de conteúdo com IA?',
                        'answer' => 'Nossa IA usa modelos de linguagem avançados treinados em milhões de exemplos de conteúdo de alta qualidade. Simplesmente forneça palavras-chave ou tópicos, e nossa IA gera conteúdo relevante e envolvente em segundos.',
                    ],
                    'ru' => [
                        'question' => 'Как работает генерация контента с ИИ?',
                        'answer' => 'Наш ИИ использует продвинутые языковые модели, обученные на миллионах высококачественных примеров контента. Просто предоставьте ключевые слова или темы, и наш ИИ сгенерирует релевантный и привлекательный контент за секунды.',
                    ],
                ],
            ],
            [
                'sort_order' => 2,
                'translations' => [
                    'en' => [
                        'question' => 'What languages are supported?',
                        'answer' => 'We support 6 languages: English, Arabic, French, Spanish, Portuguese, and Russian. Our Arabic support includes proper RTL layout and cultural context.',
                    ],
                    'ar' => [
                        'question' => 'ما هي اللغات المدعومة؟',
                        'answer' => 'ندعم 6 لغات: الإنجليزية والعربية والفرنسية والإسبانية والبرتغالية والروسية. دعمنا للعربية يشمل تخطيط RTL المناسب والسياق الثقافي.',
                    ],
                    'fr' => [
                        'question' => 'Quelles langues sont supportées ?',
                        'answer' => 'Nous supportons 6 langues : anglais, arabe, français, espagnol, portugais et russe. Notre support arabe inclut une mise en page RTL appropriée et un contexte culturel.',
                    ],
                    'es' => [
                        'question' => '¿Qué idiomas son compatibles?',
                        'answer' => 'Soportamos 6 idiomas: inglés, árabe, francés, español, portugués y ruso. Nuestro soporte para árabe incluye diseño RTL apropiado y contexto cultural.',
                    ],
                    'pt' => [
                        'question' => 'Quais idiomas são suportados?',
                        'answer' => 'Suportamos 6 idiomas: inglês, árabe, francês, espanhol, português e russo. Nosso suporte para árabe inclui layout RTL apropriado e contexto cultural.',
                    ],
                    'ru' => [
                        'question' => 'Какие языки поддерживаются?',
                        'answer' => 'Мы поддерживаем 6 языков: английский, арабский, французский, испанский, португальский и русский. Наша поддержка арабского включает правильную RTL разметку и культурный контекст.',
                    ],
                ],
            ],
        ];

        foreach ($faqs as $faqData) {
            $faq = Faq::create([
                'sort_order' => $faqData['sort_order'],
                'is_active' => true,
            ]);

            foreach ($faqData['translations'] as $locale => $translation) {
                FaqTranslation::create([
                    'faq_id' => $faq->id,
                    'locale' => $locale,
                    'question' => $translation['question'],
                    'answer' => $translation['answer'],
                ]);
            }

            $this->info("Created FAQ {$faq->id} with translations");
        }

        $this->info('FAQs seeded successfully!');
    }
}
