<?php

namespace App\Helpers;

class LocalizedUrlHelper
{
    /**
     * Supported locales
     */
    protected static array $supportedLocales = ['en', 'ar', 'fr', 'es', 'pt', 'ru'];

    /**
     * Generate a localized URL for a given route
     */
    public static function route(string $routeName, array $parameters = [], ?string $locale = null): string
    {
        $locale = $locale ?? app()->getLocale();
        
        // For English, use the default routes without locale prefix
        if ($locale === 'en') {
            return route($routeName, $parameters);
        }
        
        // For other languages, use the marketing routes with locale prefix
        $marketingRouteName = self::getMarketingRouteName($routeName);
        if ($marketingRouteName) {
            return route($marketingRouteName, array_merge(['locale' => $locale], $parameters));
        }
        
        // Fallback to default route
        return route($routeName, $parameters);
    }

    /**
     * Get the marketing route name for localized routes
     */
    protected static function getMarketingRouteName(string $routeName): ?string
    {
        $routeMapping = [
            'home' => 'marketing.landing',
            'pricing' => 'marketing.pricing',
            'faq' => 'marketing.faq',
            'about' => 'marketing.about',
            'contact' => 'marketing.contact',
            'privacy-policy' => 'marketing.privacy-policy',
            'terms-of-service' => 'marketing.terms-of-service',
        ];

        return $routeMapping[$routeName] ?? null;
    }

    /**
     * Generate a localized URL for a page slug
     */
    public static function page(string $slug, ?string $locale = null): string
    {
        $locale = $locale ?? app()->getLocale();
        
        if ($locale === 'en') {
            return url($slug);
        }
        
        return url($locale . '/' . $slug);
    }

    /**
     * Get the current page URL in a different locale
     */
    public static function currentPageInLocale(string $targetLocale): string
    {
        $currentRoute = request()->route();
        $currentLocale = app()->getLocale();
        
        if (!$currentRoute) {
            return self::route('home', [], $targetLocale);
        }
        
        $routeName = $currentRoute->getName();
        $parameters = $currentRoute->parameters();
        
        // Remove current locale from parameters if present
        unset($parameters['locale']);
        
        // Handle marketing routes
        if (str_starts_with($routeName, 'marketing.')) {
            if ($targetLocale === 'en') {
                // Convert marketing route to default route
                $defaultRouteName = str_replace('marketing.', '', $routeName);
                if ($defaultRouteName === 'landing') {
                    $defaultRouteName = 'home';
                }
                return route($defaultRouteName, $parameters);
            } else {
                // Use marketing route with new locale
                return route($routeName, array_merge(['locale' => $targetLocale], $parameters));
            }
        }
        
        // Handle default routes
        if ($targetLocale !== 'en') {
            $marketingRouteName = self::getMarketingRouteName($routeName);
            if ($marketingRouteName) {
                return route($marketingRouteName, array_merge(['locale' => $targetLocale], $parameters));
            }
        }
        
        return route($routeName, $parameters);
    }

    /**
     * Check if current route is active for navigation highlighting
     */
    public static function isRouteActive(string $routeName): bool
    {
        $currentRoute = request()->route();
        if (!$currentRoute) {
            return false;
        }
        
        $currentRouteName = $currentRoute->getName();
        
        // Check direct match
        if ($currentRouteName === $routeName) {
            return true;
        }
        
        // Check marketing route equivalents
        $marketingRouteName = self::getMarketingRouteName($routeName);
        if ($marketingRouteName && $currentRouteName === $marketingRouteName) {
            return true;
        }
        
        // Check reverse mapping (marketing to default)
        if (str_starts_with($currentRouteName, 'marketing.')) {
            $defaultRouteName = str_replace('marketing.', '', $currentRouteName);
            if ($defaultRouteName === 'landing') {
                $defaultRouteName = 'home';
            }
            if ($defaultRouteName === $routeName) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Get all supported locales
     */
    public static function getSupportedLocales(): array
    {
        return self::$supportedLocales;
    }

    /**
     * Get locale display name
     */
    public static function getLocaleDisplayName(string $locale): string
    {
        $names = [
            'en' => 'English',
            'ar' => 'العربية',
            'fr' => 'Français',
            'es' => 'Español',
            'pt' => 'Português',
            'ru' => 'Русский',
        ];

        return $names[$locale] ?? $locale;
    }

    /**
     * Get locale flag emoji
     */
    public static function getLocaleFlag(string $locale): string
    {
        $flags = [
            'en' => '🇺🇸',
            'ar' => '🇸🇦',
            'fr' => '🇫🇷',
            'es' => '🇪🇸',
            'pt' => '🇵🇹',
            'ru' => '🇷🇺',
        ];

        return $flags[$locale] ?? '🌐';
    }
}
