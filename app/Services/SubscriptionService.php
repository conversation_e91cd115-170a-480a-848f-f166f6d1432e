<?php

namespace App\Services;

use App\Models\Plan;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SubscriptionService
{
    public function createFreeSubscription(User $user): Subscription
    {
        $freePlan = Plan::where('monthly_price', 0)->first();

        if (!$freePlan) {
            throw new \Exception('Free plan not found');
        }

        return Subscription::create([
            'user_id' => $user->id,
            'plan_id' => $freePlan->id,
            'stripe_status' => 'active',
            'starts_at' => now(),
            'ends_at' => now()->addMonth(),
            'generations_used' => 0,
        ]);
    }

    public function incrementGenerationCount(User $user): bool
    {
        $subscription = $user->subscription;

        if (!$subscription || !$subscription->isActive()) {
            return false;
        }

        $subscription->increment('generations_used');

        return true;
    }

    public function canGenerateContent(User $user): bool
    {
        return $user->canGenerateContent();
    }

    public function getRemainingGenerations(User $user): int
    {
        if (!$user->subscription) {
            return 0;
        }

        return $user->subscription->remainingGenerations();
    }
}
