<?php

namespace App\Services;

use App\Models\ContentType;
use App\Models\Generation;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OpenAiService
{
    protected string $apiKey;
    protected string $baseUrl = 'https://api.openai.com/v1';
    protected string $defaultModel = 'gpt-3.5-turbo';

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');
    }

    public function generateContent(User $user, ContentType $contentType, string $language, string $keywords): Generation
    {
        $prompt = $this->buildPrompt($contentType, $language, $keywords);
        $maxRetries = 3;
        $retryDelay = 1; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                $response = Http::timeout(30)
                    ->retry(2, 1000) // 2 retries with 1 second delay
                    ->withHeaders([
                        'Authorization' => 'Bearer ' . $this->apiKey,
                        'Content-Type' => 'application/json',
                    ])->post($this->baseUrl . '/chat/completions', [
                        'model' => $this->defaultModel,
                        'messages' => [
                            ['role' => 'system', 'content' => $this->getSystemPrompt($language)],
                            ['role' => 'user', 'content' => $prompt],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => $this->getMaxTokens($contentType),
                        'presence_penalty' => 0.1,
                        'frequency_penalty' => 0.1,
                    ]);

                if ($response->successful()) {
                    $data = $response->json();
                    $result = $data['choices'][0]['message']['content'] ?? '';
                    $tokensUsed = $data['usage']['total_tokens'] ?? 0;

                    // Validate that we got meaningful content
                    if (empty(trim($result))) {
                        throw new \Exception('Empty response from OpenAI API');
                    }

                    return Generation::create([
                        'user_id' => $user->id,
                        'content_type_id' => $contentType->id,
                        'language' => $language,
                        'keywords' => $keywords,
                        'result' => trim($result),
                        'model_used' => $this->defaultModel,
                        'tokens_used' => $tokensUsed,
                    ]);
                } else {
                    $errorData = $response->json();
                    $errorMessage = $errorData['error']['message'] ?? 'Unknown API error';

                    // Handle rate limiting
                    if ($response->status() === 429) {
                        if ($attempt < $maxRetries) {
                            Log::warning("OpenAI rate limit hit, retrying attempt {$attempt}/{$maxRetries}");
                            sleep($retryDelay * $attempt); // Exponential backoff
                            continue;
                        }
                        throw new \Exception('Rate limit exceeded. Please try again later.');
                    }

                    // Handle other API errors
                    Log::error('OpenAI API error', [
                        'status' => $response->status(),
                        'error' => $errorMessage,
                        'attempt' => $attempt,
                    ]);

                    if ($attempt < $maxRetries && in_array($response->status(), [500, 502, 503, 504])) {
                        sleep($retryDelay * $attempt);
                        continue;
                    }

                    throw new \Exception("API Error: {$errorMessage}");
                }
            } catch (\Exception $e) {
                if ($attempt < $maxRetries && !str_contains($e->getMessage(), 'Rate limit')) {
                    Log::warning("OpenAI request failed, retrying attempt {$attempt}/{$maxRetries}: " . $e->getMessage());
                    sleep($retryDelay * $attempt);
                    continue;
                }

                Log::error('OpenAI service error', [
                    'message' => $e->getMessage(),
                    'user_id' => $user->id,
                    'content_type' => $contentType->name,
                    'attempt' => $attempt,
                ]);
                throw $e;
            }
        }

        throw new \Exception('Failed to generate content after multiple attempts');
    }

    protected function buildPrompt(ContentType $contentType, string $language, string $keywords): string
    {
        $template = $contentType->prompt_template;

        // Get language name for better context
        $languageNames = [
            'ar' => 'Arabic',
            'en' => 'English',
            'fr' => 'French',
            'es' => 'Spanish',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
        ];

        $languageName = $languageNames[$language] ?? $language;

        return str_replace(
            ['{language}', '{keywords}'],
            [$languageName, $keywords],
            $template
        );
    }

    protected function getSystemPrompt(string $language): string
    {
        $basePrompt = 'You are a professional content creator assistant. Create high-quality, engaging content that is relevant, accurate, and well-structured.';

        // Add language-specific instructions
        switch ($language) {
            case 'ar':
                return $basePrompt . ' Respond in Arabic with proper grammar and cultural context. Use appropriate Arabic expressions and maintain formal tone when needed.';
            case 'en':
                return $basePrompt . ' Respond in clear, professional English with proper grammar and engaging tone.';
            case 'fr':
                return $basePrompt . ' Répondez en français avec une grammaire appropriée et un ton professionnel.';
            case 'es':
                return $basePrompt . ' Responde en español con gramática apropiada y tono profesional.';
            case 'pt':
                return $basePrompt . ' Responda em português com gramática apropriada e tom profissional.';
            case 'ru':
                return $basePrompt . ' Отвечайте на русском языке с правильной грамматикой и профессиональным тоном.';
            default:
                return $basePrompt;
        }
    }

    protected function getMaxTokens(ContentType $contentType): int
    {
        // Adjust max tokens based on content type
        $tokenLimits = [
            'social-media-post' => 300,
            'instagram-captions' => 400,
            'youtube-captions' => 800,
            'product-description' => 600,
            'email-marketing' => 1000,
            'blog-post-outline' => 1200,
            'website-content-generator' => 1500,
            'seo-content-optimizer' => 1200,
            'youtube-video-idea' => 800,
        ];

        return $tokenLimits[$contentType->slug] ?? 1000;
    }
}
