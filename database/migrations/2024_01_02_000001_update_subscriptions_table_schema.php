<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Add missing columns if they don't exist
            if (!Schema::hasColumn('subscriptions', 'stripe_status')) {
                $table->string('stripe_status')->nullable()->after('stripe_price');
            }
            
            // Rename columns if they exist with old names
            if (Schema::hasColumn('subscriptions', 'status') && !Schema::hasColumn('subscriptions', 'stripe_status')) {
                $table->renameColumn('status', 'stripe_status');
            }
            
            if (Schema::hasColumn('subscriptions', 'stripe_subscription_id') && !Schema::hasColumn('subscriptions', 'stripe_id')) {
                $table->renameColumn('stripe_subscription_id', 'stripe_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            // Reverse the changes
            if (Schema::hasColumn('subscriptions', 'stripe_status') && !Schema::hasColumn('subscriptions', 'status')) {
                $table->renameColumn('stripe_status', 'status');
            }
            
            if (Schema::hasColumn('subscriptions', 'stripe_id') && !Schema::hasColumn('subscriptions', 'stripe_subscription_id')) {
                $table->renameColumn('stripe_id', 'stripe_subscription_id');
            }
        });
    }
};
