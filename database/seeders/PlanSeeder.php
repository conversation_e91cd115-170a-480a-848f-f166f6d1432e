<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $plans = [
            [
                'name' => 'Free',
                'slug' => 'free',
                'description' => 'Perfect for getting started with content generation',
                'monthly_generations_limit' => 5,
                'monthly_price' => 0.00,
                'annual_price' => 0.00,
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Standard',
                'slug' => 'standard',
                'description' => 'Great for regular content creators and small businesses',
                'monthly_generations_limit' => 100,
                'monthly_price' => 9.99,
                'annual_price' => 99.99,
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Pro',
                'slug' => 'pro',
                'description' => 'Unlimited content generation for power users and agencies',
                'monthly_generations_limit' => -1, // Unlimited
                'monthly_price' => 29.99,
                'annual_price' => 299.99,
                'is_active' => true,
                'is_featured' => false,
            ],
        ];

        foreach ($plans as $planData) {
            Plan::updateOrCreate(
                ['slug' => $planData['slug']],
                $planData
            );
        }
    }
}
