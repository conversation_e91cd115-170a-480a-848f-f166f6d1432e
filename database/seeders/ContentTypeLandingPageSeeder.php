<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Page;
use App\Models\PageTranslation;

class ContentTypeLandingPageSeeder extends Seeder
{
    public function run(): void
    {
        $contentTypes = [
            [
                'slug' => 'social-media-post',
                'translations' => [
                    'en' => [
                        'title' => 'Social Media Posts Generator',
                        'description' => 'Create engaging posts for all your social platforms.',
                        'content' => '<h2>What is a Social Media Post?</h2><p>Social media posts are short, impactful messages designed to engage your audience on platforms like Facebook, Twitter, and Instagram.</p><h3>Benefits</h3><ul><li>Boost engagement</li><li>Grow your audience</li><li>Promote your brand</li></ul><h3>Example</h3><blockquote>"Unleash your creativity with our AI-powered post generator! #SocialMedia #AIContent"</blockquote>',
                        'seo_title' => 'AI Social Media Post Generator',
                        'seo_description' => 'Generate catchy and effective social media posts in seconds with AI. Supports all major platforms.',
                        'seo_keywords' => 'social media, post generator, AI, Facebook, Twitter, Instagram',
                        'featured_image' => '/images/social-media.jpg',
                    ],
                ],
            ],
            [
                'slug' => 'product-description',
                'translations' => [
                    'en' => [
                        'title' => 'Product Description Generator',
                        'description' => 'Write compelling product descriptions that sell.',
                        'content' => '<h2>What is a Product Description?</h2><p>Product descriptions highlight the features and benefits of your products, helping customers make informed decisions.</p><h3>Benefits</h3><ul><li>Increase conversions</li><li>Improve SEO</li><li>Save time</li></ul><h3>Example</h3><blockquote>"Experience next-level comfort with our ergonomic office chair—perfect for long workdays."</blockquote>',
                        'seo_title' => 'AI Product Description Generator',
                        'seo_description' => 'Create high-converting product descriptions for your e-commerce store using AI.',
                        'seo_keywords' => 'product description, AI, e-commerce, sales',
                        'featured_image' => '/images/product-description.jpg',
                    ],
                ],
            ],
            [
                'slug' => 'youtube-video-idea',
                'translations' => [
                    'en' => [
                        'title' => 'YouTube Video Ideas Generator',
                        'description' => 'Get creative video ideas for your YouTube channel.',
                        'content' => '<h2>What is a YouTube Video Idea?</h2><p>YouTube video ideas help creators plan engaging content that attracts viewers and grows channels.</p><h3>Benefits</h3><ul><li>Never run out of ideas</li><li>Boost channel growth</li><li>Engage your audience</li></ul><h3>Example</h3><blockquote>"Top 10 Productivity Hacks for Remote Workers"</blockquote>',
                        'seo_title' => 'AI YouTube Video Idea Generator',
                        'seo_description' => 'Discover trending and creative YouTube video ideas with the power of AI.',
                        'seo_keywords' => 'YouTube, video ideas, AI, content creator',
                        'featured_image' => '/images/youtube-ideas.jpg',
                    ],
                ],
            ],
            [
                'slug' => 'blog-post-outline',
                'translations' => [
                    'en' => [
                        'title' => 'Blog Post Outline Generator',
                        'description' => 'Structure your blog posts for maximum impact.',
                        'content' => '<h2>What is a Blog Post Outline?</h2><p>A blog post outline organizes your ideas and ensures your content flows logically for readers.</p><h3>Benefits</h3><ul><li>Write faster</li><li>Stay organized</li><li>Improve readability</li></ul><h3>Example</h3><blockquote>"I. Introduction II. Main Points III. Conclusion"</blockquote>',
                        'seo_title' => 'AI Blog Post Outline Generator',
                        'seo_description' => 'Quickly generate detailed blog post outlines with AI for any topic.',
                        'seo_keywords' => 'blog post, outline, AI, writing',
                        'featured_image' => '/images/blog-outline.jpg',
                    ],
                ],
            ],
            [
                'slug' => 'email-marketing',
                'translations' => [
                    'en' => [
                        'title' => 'Email Marketing Content Generator',
                        'description' => 'Craft persuasive email campaigns that convert.',
                        'content' => '<h2>What is Email Marketing Content?</h2><p>Email marketing content is designed to nurture leads and drive sales through targeted messaging.</p><h3>Benefits</h3><ul><li>Increase open rates</li><li>Drive conversions</li><li>Automate campaigns</li></ul><h3>Example</h3><blockquote>"Don’t miss our exclusive offer—shop now and save 20%!"</blockquote>',
                        'seo_title' => 'AI Email Marketing Content Generator',
                        'seo_description' => 'Generate high-performing email marketing content with AI for any campaign.',
                        'seo_keywords' => 'email marketing, AI, campaign, content',
                        'featured_image' => '/images/email-marketing.jpg',
                    ],
                ],
            ],
        ];

        foreach ($contentTypes as $type) {
            $page = Page::create([
                'slug' => $type['slug'],
                'status' => 'active',
            ]);
            foreach ($type['translations'] as $locale => $data) {
                PageTranslation::create(array_merge($data, [
                    'page_id' => $page->id,
                    'locale' => $locale,
                ]));
            }
        }
    }
}
