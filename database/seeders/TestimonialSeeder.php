<?php

namespace Database\Seeders;

use App\Models\Testimonial;
use Illuminate\Database\Seeder;

class TestimonialSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $testimonials = [
            [
                'name' => '<PERSON>',
                'title' => 'Marketing Director',
                'company' => 'TechStart Inc.',
                'content' => 'Content Spark has revolutionized our content creation process. The AI-generated content is incredibly high-quality and saves us hours of work every week.',
                'avatar' => 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'published_at' => now()->subDays(30),
            ],
            [
                'name' => '<PERSON>',
                'title' => 'Content Manager',
                'company' => 'Digital Solutions Ltd.',
                'content' => 'The variety of content types available is amazing. From blog posts to social media content, Content Spark handles it all with impressive accuracy.',
                'avatar' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'published_at' => now()->subDays(25),
            ],
            [
                'name' => 'Emily Rodriguez',
                'title' => 'Small Business Owner',
                'company' => 'Creative Studio',
                'content' => 'As a small business owner, I don\'t have time to write all my marketing content. Content Spark has been a game-changer for my business growth.',
                'avatar' => 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
                'rating' => 5,
                'is_featured' => true,
                'is_active' => true,
                'published_at' => now()->subDays(20),
            ],
            [
                'name' => 'David Thompson',
                'title' => 'SEO Specialist',
                'company' => 'Growth Marketing Agency',
                'content' => 'The SEO-optimized content generated by Content Spark has significantly improved our clients\' search rankings. Highly recommended!',
                'avatar' => 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
                'rating' => 5,
                'is_featured' => false,
                'is_active' => true,
                'published_at' => now()->subDays(15),
            ],
            [
                'name' => 'Lisa Wang',
                'title' => 'E-commerce Manager',
                'company' => 'Online Retail Co.',
                'content' => 'Creating product descriptions used to take forever. Now with Content Spark, I can generate compelling product copy in minutes.',
                'avatar' => 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
                'rating' => 4,
                'is_featured' => false,
                'is_active' => true,
                'published_at' => now()->subDays(12),
            ],
            [
                'name' => 'James Wilson',
                'title' => 'Social Media Manager',
                'company' => 'Brand Agency',
                'content' => 'The social media content templates are fantastic. They help maintain consistency across all our client campaigns.',
                'avatar' => 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
                'rating' => 5,
                'is_featured' => false,
                'is_active' => true,
                'published_at' => now()->subDays(10),
            ],
            [
                'name' => 'Anna Kowalski',
                'title' => 'Freelance Writer',
                'company' => 'Independent',
                'content' => 'Content Spark helps me deliver better results to my clients faster. The AI suggestions often spark new creative ideas.',
                'avatar' => 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
                'rating' => 4,
                'is_featured' => false,
                'is_active' => true,
                'published_at' => now()->subDays(8),
            ],
            [
                'name' => 'Robert Martinez',
                'title' => 'Startup Founder',
                'company' => 'InnovateTech',
                'content' => 'Perfect for startups on a budget. We get professional-quality content without hiring a full-time copywriter.',
                'avatar' => 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
                'rating' => 5,
                'is_featured' => false,
                'is_active' => true,
                'published_at' => now()->subDays(6),
            ],
            [
                'name' => 'Sophie Laurent',
                'title' => 'Digital Marketing Consultant',
                'company' => 'Marketing Pro',
                'content' => 'The multi-language support is excellent. I can create content for international clients effortlessly.',
                'avatar' => 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
                'rating' => 5,
                'is_featured' => false,
                'is_active' => true,
                'published_at' => now()->subDays(4),
            ],
            [
                'name' => 'Alex Kumar',
                'title' => 'Blog Manager',
                'company' => 'Tech News Daily',
                'content' => 'Content Spark has streamlined our editorial process. We can now publish more articles while maintaining quality.',
                'avatar' => 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',
                'rating' => 4,
                'is_featured' => false,
                'is_active' => true,
                'published_at' => now()->subDays(2),
            ],
        ];

        foreach ($testimonials as $testimonial) {
            Testimonial::create($testimonial);
        }
    }
}
