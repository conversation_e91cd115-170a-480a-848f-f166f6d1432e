<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $permissions = [
            // User Management
            [
                'name' => 'View Users',
                'slug' => 'users-view',
                'description' => 'View user list and details',
                'group' => 'users'
            ],
            [
                'name' => 'Create Users',
                'slug' => 'users-create',
                'description' => 'Create new user accounts',
                'group' => 'users'
            ],
            [
                'name' => 'Edit Users',
                'slug' => 'users-edit',
                'description' => 'Edit user information',
                'group' => 'users'
            ],
            [
                'name' => 'Delete Users',
                'slug' => 'users-delete',
                'description' => 'Delete user accounts',
                'group' => 'users'
            ],

            // Role Management
            [
                'name' => 'View Roles',
                'slug' => 'roles-view',
                'description' => 'View roles and permissions',
                'group' => 'roles'
            ],
            [
                'name' => 'Create Roles',
                'slug' => 'roles-create',
                'description' => 'Create new roles',
                'group' => 'roles'
            ],
            [
                'name' => 'Edit Roles',
                'slug' => 'roles-edit',
                'description' => 'Edit role information',
                'group' => 'roles'
            ],
            [
                'name' => 'Delete Roles',
                'slug' => 'roles-delete',
                'description' => 'Delete roles',
                'group' => 'roles'
            ],

            // Permission Management
            [
                'name' => 'View Permissions',
                'slug' => 'permissions-view',
                'description' => 'View system permissions',
                'group' => 'permissions'
            ],
            [
                'name' => 'Create Permissions',
                'slug' => 'permissions-create',
                'description' => 'Create new permissions',
                'group' => 'permissions'
            ],
            [
                'name' => 'Edit Permissions',
                'slug' => 'permissions-edit',
                'description' => 'Edit permission information',
                'group' => 'permissions'
            ],
            [
                'name' => 'Delete Permissions',
                'slug' => 'permissions-delete',
                'description' => 'Delete permissions',
                'group' => 'permissions'
            ],

            // Content Management
            [
                'name' => 'View Content Types',
                'slug' => 'content-types-view',
                'description' => 'View content types',
                'group' => 'content'
            ],
            [
                'name' => 'Create Content Types',
                'slug' => 'content-types-create',
                'description' => 'Create new content types',
                'group' => 'content'
            ],
            [
                'name' => 'Edit Content Types',
                'slug' => 'content-types-edit',
                'description' => 'Edit content types',
                'group' => 'content'
            ],
            [
                'name' => 'Delete Content Types',
                'slug' => 'content-types-delete',
                'description' => 'Delete content types',
                'group' => 'content'
            ],

            // Content Generation
            [
                'name' => 'Generate Content',
                'slug' => 'content-generate',
                'description' => 'Generate AI content',
                'group' => 'content'
            ],
            [
                'name' => 'View Generations',
                'slug' => 'generations-view',
                'description' => 'View content generations',
                'group' => 'content'
            ],
            [
                'name' => 'Delete Generations',
                'slug' => 'generations-delete',
                'description' => 'Delete content generations',
                'group' => 'content'
            ],

            // Page Management
            [
                'name' => 'View Pages',
                'slug' => 'pages-view',
                'description' => 'View pages',
                'group' => 'pages'
            ],
            [
                'name' => 'Create Pages',
                'slug' => 'pages-create',
                'description' => 'Create new pages',
                'group' => 'pages'
            ],
            [
                'name' => 'Edit Pages',
                'slug' => 'pages-edit',
                'description' => 'Edit pages',
                'group' => 'pages'
            ],
            [
                'name' => 'Delete Pages',
                'slug' => 'pages-delete',
                'description' => 'Delete pages',
                'group' => 'pages'
            ],
            [
                'name' => 'Publish Pages',
                'slug' => 'pages-publish',
                'description' => 'Publish and unpublish pages',
                'group' => 'pages'
            ],

            // Plan Management
            [
                'name' => 'View Plans',
                'slug' => 'plans-view',
                'description' => 'View subscription plans',
                'group' => 'plans'
            ],
            [
                'name' => 'Create Plans',
                'slug' => 'plans-create',
                'description' => 'Create new plans',
                'group' => 'plans'
            ],
            [
                'name' => 'Edit Plans',
                'slug' => 'plans-edit',
                'description' => 'Edit plan information',
                'group' => 'plans'
            ],
            [
                'name' => 'Delete Plans',
                'slug' => 'plans-delete',
                'description' => 'Delete plans',
                'group' => 'plans'
            ],

            // Testimonial Management
            [
                'name' => 'View Testimonials',
                'slug' => 'testimonials-view',
                'description' => 'View testimonials',
                'group' => 'testimonials'
            ],
            [
                'name' => 'Create Testimonials',
                'slug' => 'testimonials-create',
                'description' => 'Create new testimonials',
                'group' => 'testimonials'
            ],
            [
                'name' => 'Edit Testimonials',
                'slug' => 'testimonials-edit',
                'description' => 'Edit testimonials',
                'group' => 'testimonials'
            ],
            [
                'name' => 'Delete Testimonials',
                'slug' => 'testimonials-delete',
                'description' => 'Delete testimonials',
                'group' => 'testimonials'
            ],
            [
                'name' => 'Approve Testimonials',
                'slug' => 'testimonials-approve',
                'description' => 'Approve and reject testimonials',
                'group' => 'testimonials'
            ],

            // FAQ Management
            [
                'name' => 'View FAQs',
                'slug' => 'faqs-view',
                'description' => 'View FAQs',
                'group' => 'faqs'
            ],
            [
                'name' => 'Create FAQs',
                'slug' => 'faqs-create',
                'description' => 'Create new FAQs',
                'group' => 'faqs'
            ],
            [
                'name' => 'Edit FAQs',
                'slug' => 'faqs-edit',
                'description' => 'Edit FAQs',
                'group' => 'faqs'
            ],
            [
                'name' => 'Delete FAQs',
                'slug' => 'faqs-delete',
                'description' => 'Delete FAQs',
                'group' => 'faqs'
            ],

            // System Administration
            [
                'name' => 'View Admin Dashboard',
                'slug' => 'admin-dashboard',
                'description' => 'Access admin dashboard',
                'group' => 'admin'
            ],
            [
                'name' => 'View System Settings',
                'slug' => 'settings-view',
                'description' => 'View system settings',
                'group' => 'admin'
            ],
            [
                'name' => 'Edit System Settings',
                'slug' => 'settings-edit',
                'description' => 'Edit system settings',
                'group' => 'admin'
            ],
            [
                'name' => 'View Reports',
                'slug' => 'reports-view',
                'description' => 'View system reports',
                'group' => 'admin'
            ],
            [
                'name' => 'Export Data',
                'slug' => 'data-export',
                'description' => 'Export system data',
                'group' => 'admin'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::create($permission);
        }
    }
}
