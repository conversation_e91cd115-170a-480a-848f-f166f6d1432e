<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed plans first
        $this->call([
            PlanSeeder::class,
            ContentTypeSeeder::class,
            AdminUserSeeder::class,
            ContentTypeLandingPageSeeder::class,
            FaqSeeder::class,
            MarketingPagesSeeder::class,
        ]);

        // Create test user
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
    }
}
