<?php

namespace Database\Seeders;

use App\Models\ContentType;
use Illuminate\Database\Seeder;

class ContentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $contentTypes = [
            [
                'name' => 'Social Media Post',
                'slug' => 'social-media-post',
                'description' => 'Generate engaging social media posts for various platforms',
                'category' => 'Social Media',
                'prompt_template' => 'Create an engaging social media post about {keywords} in {language}. Make it catchy, relevant, and include appropriate hashtags.',
                'is_active' => true,
            ],
            [
                'name' => 'Product Description',
                'slug' => 'product-description',
                'description' => 'Create compelling product descriptions for e-commerce',
                'category' => 'E-commerce',
                'prompt_template' => 'Write a compelling product description for {keywords} in {language}. Focus on benefits, features, and persuasive language that converts.',
                'is_active' => true,
            ],
            [
                'name' => 'YouTube Video Idea',
                'slug' => 'youtube-video-idea',
                'description' => 'Generate creative YouTube video ideas and concepts',
                'category' => 'Video Content',
                'prompt_template' => 'Generate creative YouTube video ideas related to {keywords} in {language}. Include title suggestions, content outline, and engagement strategies.',
                'is_active' => true,
            ],
            [
                'name' => 'Blog Post Outline',
                'slug' => 'blog-post-outline',
                'description' => 'Create structured blog post outlines',
                'category' => 'Content Creation',
                'prompt_template' => 'Create a detailed blog post outline about {keywords} in {language}. Include main headings, subheadings, and key points to cover.',
                'is_active' => true,
            ],
            [
                'name' => 'Email Marketing',
                'slug' => 'email-marketing',
                'description' => 'Generate effective email marketing content',
                'category' => 'Marketing',
                'prompt_template' => 'Write an effective email marketing campaign about {keywords} in {language}. Include subject line, body content, and call-to-action.',
                'is_active' => true,
            ],
            [
                'name' => 'Website Content Generator',
                'slug' => 'website-content-generator',
                'description' => 'Create web page content, landing pages, and website copy',
                'category' => 'Website Content',
                'prompt_template' => 'Create compelling website content for {keywords} in {language}. Write engaging copy that converts visitors into customers. Include headlines, body text, and call-to-action sections.',
                'is_active' => true,
            ],
            [
                'name' => 'SEO Content Optimizer',
                'slug' => 'seo-content-optimizer',
                'description' => 'Generate SEO-optimized content, meta descriptions, and keyword-rich text',
                'category' => 'SEO',
                'prompt_template' => 'Create SEO-optimized content about {keywords} in {language}. Include relevant keywords naturally, write compelling meta descriptions, and ensure the content is search engine friendly while remaining engaging for readers.',
                'is_active' => true,
            ],
            [
                'name' => 'YouTube Captions',
                'slug' => 'youtube-captions',
                'description' => 'Create engaging YouTube video captions and descriptions',
                'category' => 'Video Content',
                'prompt_template' => 'Write engaging YouTube video captions and descriptions for content about {keywords} in {language}. Include timestamps, key points, relevant hashtags, and encourage viewer engagement.',
                'is_active' => true,
            ],
            [
                'name' => 'Instagram Captions',
                'slug' => 'instagram-captions',
                'description' => 'Generate Instagram post captions with hashtags and engagement hooks',
                'category' => 'Social Media',
                'prompt_template' => 'Create captivating Instagram captions about {keywords} in {language}. Include relevant hashtags, engagement hooks, and call-to-action. Keep it authentic and encourage interaction.',
                'is_active' => true,
            ],
            [
                'name' => 'Coming Soon Placeholder',
                'slug' => 'coming-soon-placeholder',
                'description' => 'More content types coming soon',
                'category' => 'Content Creation',
                'prompt_template' => 'We are constantly adding new content types to help you create amazing content. Stay tuned for more exciting options! For now, here\'s some placeholder content about {keywords} in {language}.',
                'is_active' => false,
            ],
        ];

        foreach ($contentTypes as $contentTypeData) {
            ContentType::updateOrCreate(
                ['slug' => $contentTypeData['slug']],
                $contentTypeData
            );
        }
    }
}
